{"disallowKeywords": ["with"], "requireLeftStickedOperators": [","], "disallowLeftStickedOperators": ["?", "+", "-", "/", "*", "=", "==", "===", "!=", "!==", ">", ">=", "<", "<="], "disallowRightStickedOperators": ["?", "/", "*", ":", "=", "==", "===", "!=", "!==", ">", ">=", "<", "<="], "disallowSpaceAfterPrefixUnaryOperators": ["++", "--", "+", "-", "~"], "disallowSpaceBeforePostfixUnaryOperators": ["++", "--"], "requireLineFeedAtFileEnd": true, "requireRightStickedOperators": ["!"], "requireSpaceAfterBinaryOperators": ["+", "-", "/", "*", "=", "==", "===", "!=", "!=="], "requireSpaceAfterKeywords": ["if", "else", "for", "while", "do", "switch", "return", "try", "catch"], "requireSpaceBeforeBinaryOperators": ["+", "-", "/", "*", "=", "==", "===", "!=", "!=="], "requireSpacesInFunctionExpression": {"beforeOpeningCurlyBrace": true}, "validateLineBreaks": "LF"}