using App_Code;
using customerArea.App_Code;
using customerArea.classes;
using Facebook;
using log4net;
using Newtonsoft.Json.Linq;
using RestSharp;
using RestSharp.Authenticators;
using System;
//using RodWebShopPark.App_Code;
using System.Collections.Generic;
using System.Net;
using System.Web.Configuration;
using System.Web.Script.Serialization;
using System.Web.Script.Services;
using System.Web.Services;
using System.Web.UI;
using Themis.Libraries.Utilities.Crypto;
using utilitaires2010;
using ws_bll.WT;
using ws_DTO;
using ws_DTO.objets_liaisons;

namespace customerArea
{

    public partial class Login : basePage
    {
        private static readonly ILog logger = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);


        public static MyDictionary mySSC { get; set; }


        protected void Page_Load(object sender, EventArgs e)
        {
            //WebPartManager1.StaticConnections.Clear();
            int structureId = 0;
            if (Request.QueryString["idstructure"] != "" && Request.QueryString["idstructure"] != null && int.TryParse(Request.QueryString["idstructure"], out structureId))
            {
                System.Web.HttpContext.Current.Session["idstructure"] = structureId;
            }
            else if (GetStructureId() > 0)
            {
                structureId = GetStructureId();
            }


            GestionTraceManager.WriteLog(structureId, "page_load popIdentification...", TypeLog.LoadPage);

            string fileP = Page.Request.FilePath;
            fileP = fileP.Substring(fileP.LastIndexOf('/') + 1, fileP.LastIndexOf(".") - fileP.LastIndexOf('/') - 1);

            Ihm myIhm = new Ihm();

            int resulEvent = 0;
            if (Request.QueryString["eventId"] != "" && Request.QueryString["eventId"] != null && int.TryParse(Request.QueryString["eventId"], out resulEvent))
            {
                System.Web.HttpContext.Current.Session["eventId"] = resulEvent;
            }
            if (Request.QueryString["FiliereId"] != "" && Request.QueryString["FiliereId"] != null && int.TryParse(Request.QueryString["FiliereId"], out int filiereId))
            {
                System.Web.HttpContext.Current.Session["filiere_id"] = filiereId.ToString();
            }
            string idpa = "0";
            if (System.Web.HttpContext.Current.Session["ProfilAcheteurId"] != null)
            {
                idpa = (string)System.Web.HttpContext.Current.Session["ProfilAcheteurId"];
            }


            myIhm.ReadXMLFile("IHMlogin", resulEvent.ToString(), idpa, "page", "tableau", false);

            /*  if (myIhm.htVisible == null || myIhm.htAttribut == null)
              {
                  GestionTraceManager.WriteLogError(structureId,"Impossible de trouv� fichier IHM");
                  logger.Error("Impossible de trouv� fichier IHM");
                  throw new Exception("Impossible de trouv� fichier IHM");

              }*/

            //si libExplic est null ou vide ca veut dire que l'attribut visible == false ou la cl� n'existe pas
            string libExplic = myIhm.GetLibelle("PhraseEnteteForgotPassword", "msg_entente_forgot_password"); // phrase "creer un compte en tout simplicit�"
            if (string.IsNullOrEmpty(libExplic))
            {
                explicationForgotPassword.Visible = false;
            }
            //   attributEntete
            //     spanIdentification.InnerText = myIhm.GetLibelle("lbForTitleIdentification", "title_Identification");

            // aDejaUnCompte.InnerText = myIhm.GetLibelle("lbForTabSeConnecter", "title_tab_se_connecter");
            //    aCreerCompte.InnerText = myIhm.GetLibelle("lbForTabCreation", "title_tab_create_account");



            /*spanIdentification.InnerText = Initialisations.GetMessageTranslate("title_Identification");

            aDejaUnCompte.InnerText = Initialisations.GetMessageTranslate("title_tab_se_connecter");
            aCreerCompte.InnerText = Initialisations.GetMessageTranslate("title_tab_create_account"); 
            */
            //string CssPaths = System.Configuration.ConfigurationManager.AppSettings["CssPaths"].ToString();
            //CssPaths = CssPaths.Replace("[idstructure]", IdStructure.ToString("0000"));
            //int posLastPoint = CssPaths.LastIndexOf('/') + 1;
            //string CssPathRacine = CssPaths.Substring(0, posLastPoint);


            #region literaux pour insertion commentaires, tags, etc
            litForCommentaireBas.Text = GetLiteralCommentaireEnclosed(fileP, "Bas", resulEvent);
            litForCommentaireHaut.Text = GetLiteralCommentaireEnclosed(fileP, "Haut", resulEvent);
            #endregion

            /* if (litForSteps != null)
                 litForSteps.Text = GetHtmlStepsDiv(IdStructure, resulEvent.ToString(), idpa, fileP);
             */
            #region fichiers javascripts par page/structures
            // envoie le fichier javascript propres � la page 
            System.Text.StringBuilder sb_insere_javascriptsfiles = new System.Text.StringBuilder();
            //basePage bp = new basePage();
            Urls url = new Urls();
            //   sb_insere_javascriptsfiles.AppendLine(url.ScriptJavaScriptFile("commons", resulEvent, true));


            // mySSC = new MyDictionary();
            // mySSC = mySSC.GetDictionaryFromCache(IdStructure);


            string plateformCode = "";
            if (Request.QueryString["plateformCode"] != "" && Request.QueryString["plateformCode"] != null)
            {
                plateformCode = Request.QueryString["plateformCode"].ToString();
                System.Web.HttpContext.Current.Session["plateformCode"] = Request.QueryString["plateformCode"].ToString();
            }

            string lang = App_Code.Initialisations.GetUserLanguage();


            List<CustomJsonSettings> listJsonSettingsToMerge = new List<CustomJsonSettings>
            {
                new CustomJsonSettings() { KeyOfJsonPath = "physicalPathOfSettingsJSON", IsDefault = true },
                new CustomJsonSettings() { KeyOfJsonPath = "physicalPathOfSettingsJSON", IsDefault = false },
                new CustomJsonSettings() { KeyOfJsonPath = "physicalPathOfSettingsPlateformJSON", IsDefault = false }
            };


            dynamic globalPlateform = utilitaires2010.Initialisations.GetMergeSettings(listJsonSettingsToMerge, structureId, resulEvent, int.Parse(idpa), plateformCode, lang);


            dynamic isGuest = globalPlateform.global.guest.Value;
            if (globalPlateform.customer != null && globalPlateform.customer.global != null && globalPlateform.customer.global.guest != null)
            {
                isGuest = globalPlateform.customer.global.guest.Value;
            }


            dynamic isLight = globalPlateform.global.light.Value;
            if (globalPlateform.customer != null && globalPlateform.customer.global != null && globalPlateform.customer.global.light != null)
            {
                isLight = globalPlateform.customer.global.light.Value;
            }


            // var globalPlateform = JsonConvert.DeserializeObject<Global>(jobjectSettingPlateform.ToString());

            if (isGuest)
            {
                divguestloginWrapper.Visible = true;
                sb_insere_javascriptsfiles.AppendLine(url.ScriptJavaScriptFile("guestAccount", resulEvent, true));
            }
            else
            {
                divguestloginWrapper.Visible = false;
            }


            if (isLight)
            {
                WctrLoginCreationLight.Visible = true;
                WctrLoginCreation1.Visible = false;
            }
            else
            {
                WctrLoginCreation1.Visible = true;
                WctrLoginCreationLight.Visible = false;
            }
            /*
            //si on a Light a 1 dans config ini on charge le user controle light
            if (mySSC.Contains("CREATEPROFILLIGHT") && mySSC["CREATEPROFILLIGHT"] == "1")
            {
                WctrLoginCreationLight.Visible = true;
                WctrLoginCreation1.Visible = false;
                //oin charge loginlight.js
                // sb_insere_javascriptsfiles.AppendLine(url.ScriptJavaScriptFile(fileP + "light", resulEvent, true));
            }
            else
            {
                WctrLoginCreation1.Visible = true;
                WctrLoginCreationLight.Visible = false;
            }

            if (mySSC.Contains("CREATEPROFILGUEST") && mySSC["CREATEPROFILGUEST"] == "1")
            {
                divguestloginWrapper.Visible = true;
                sb_insere_javascriptsfiles.AppendLine(url.ScriptJavaScriptFile("guestAccount", resulEvent, true));
            }
            else
            {
                divguestloginWrapper.Visible = false;
            }
            */
            sb_insere_javascriptsfiles.AppendLine(url.ScriptJavaScriptFile(fileP, resulEvent, true));

            //ScriptManager.RegisterStartupScript(this.Page, typeof(Page), string.Format("StartupInclude"), sb_insere_javascriptsfiles.ToString(), false);
            ScriptManager.RegisterStartupScript(Page, typeof(Page), "Master", sb_insere_javascriptsfiles.ToString(), false);


            if (Request.QueryString["isPop"] == "y")
            {
                hlHomePage.NavigateUrl = "closeme";
            }
            else
            {
                if (System.Web.HttpContext.Current.Session["SVarUserIsGuest"] != null && System.Web.HttpContext.Current.Session["SVarUserIsGuest"].ToString() != "" && Convert.ToBoolean(System.Web.HttpContext.Current.Session["SVarUserIsGuest"].ToString()))
                {
                    string pageHome = "HomeGuest.aspx";
                    string qs = Page.Request.Url.Query;
                    fileP = pageHome + qs;
                    hlHomePage.NavigateUrl = fileP;

                }
                else
                {
                    Session["SVarUserIsGuest"] = false;
                    string pageHome = "Home.aspx";
                    string qs = Page.Request.Url.Query;
                    fileP = pageHome + qs;
                    hlHomePage.NavigateUrl = fileP;
                }

            }

            #endregion


            //connectLoginform.Attributes.Add("onsubmit", "return MySubMit('" + IdStructure.ToString("0000") + "');");
            //hlToUpdate.NavigateUrl = "./popIdentificationUpdate.aspx?idstructure=" + IdStructure.ToString("0000");            

            log.Debug(IdStructure, "login...");
            GestionTraceManager.WriteLog(IdStructure, "page_loaded Login ", TypeLog.LoadPage);

            //connectLoginform.Attributes.Add("onsubmit", "return false;");
            //          connectLoginform.Attributes.Add("onkeypress", "CheckKey(event.keyCode," + IdStructure.ToString("0000") + ");");

        }

        [WebMethod]
        [ScriptMethod(ResponseFormat = ResponseFormat.Json)]
        public static string ConnectPayPal(string IdStructure, string tokenPayPal)
        {
            string codePP = "";

            log.Debug(IdStructure, "ConnectPayPal(" + tokenPayPal + ")....");

            codePP = tokenPayPal;

            System.Net.ServicePointManager.SecurityProtocol = System.Net.SecurityProtocolType.Tls12 | System.Net.SecurityProtocolType.Tls11 | System.Net.SecurityProtocolType.Tls11;

            string urlTokenService = utilitaires2010.Initialisations.GetKeyAppSettings("paypalconnect_urltokenservice");

            RestClient client = new RestClient(urlTokenService);
            RestRequest request = new RestRequest(Method.POST);


            mySSC = new MyDictionary();
            mySSC = mySSC.GetDictionaryFromCache(int.Parse(IdStructure));
            string userNamePayPal = "notSet-seeConfigIni";
            string passwordPayPal = "notSet-seeConfigIni";

            if (mySSC.Contains("PAYPAL_CONNECTUSERNAME") && !string.IsNullOrEmpty(mySSC["PAYPAL_CONNECTUSERNAME"].ToString()))
            {
                userNamePayPal = mySSC["PAYPAL_CONNECTUSERNAME"].ToString();
            }
            if (mySSC.Contains("PAYPAL_CONNECTPASSWORD") && !string.IsNullOrEmpty(mySSC["PAYPAL_CONNECTPASSWORD"].ToString()))
            {
                passwordPayPal = mySSC["PAYPAL_CONNECTPASSWORD"].ToString();
            }

            client.Authenticator = new HttpBasicAuthenticator(userNamePayPal, passwordPayPal);

            //                request.AddHeader("Postman-Token", "308cc53b-2a1e-4e89-aba8-a7d99ef759be");
            request.AddHeader("cache-control", "no-cache");
            // request.AddHeader("Authorization", "Basic QWNTMjBCZWczcWQ5SldLOS0zTVNBRjJIaXg2bUJNN21YUm8taGlfdC1CV2ZxM25WcWs0RlEtNlN1aC0xaUVSR0xYZ25JVnN6c1lBTXhodlE6RUlvcFpGN0dvalJyY1E0clZjSU52T21LcEQ2UjhMb3E1ZXB3X0dXVGZHTU9LM2tGLTRLZjBBbmJ3dHdnWTRKUUFKZExnNmZpdXVZUVlRYy0=");
            request.AddHeader("Content-Type", "application/x-www-form-urlencoded");
            request.AddParameter("undefined", "grant_type=authorization_code&code=" + codePP, ParameterType.RequestBody);
            IRestResponse response = client.Execute(request);

            if (response.StatusCode == HttpStatusCode.OK)
            {
                if (response.Content != "")
                {
                    string stringResponse = response.Content;
                    JavaScriptSerializer js = new JavaScriptSerializer();
                    PayPalResponseGetToken jsonrespon = js.Deserialize<PayPalResponseGetToken>(stringResponse);

                    string myAccToken = jsonrespon.access_token;
                    string myIdToken = jsonrespon.id_token;


                    string urlUInfo = MyConfigurationManager.AppSettings("paypalconnect_urluserinfo");

                    client = new RestClient(urlUInfo);
                    request = new RestRequest(Method.POST);
                    client.AddDefaultHeader("Authorization", string.Format("Bearer {0}", myAccToken));

                    response = client.Execute(request);
                    if (response.StatusCode == HttpStatusCode.OK)
                    {
                        if (response.Content != "")
                        {
                            stringResponse = response.Content;

                            logger.Debug("paypal userinfo ok content=" + stringResponse);
                            js = new JavaScriptSerializer();

                            string eventid = "0";

                            PayPalResponseGetInfo jsonPayPalUser = js.Deserialize<PayPalResponseGetInfo>(stringResponse);

                            string structureId = System.Web.HttpContext.Current.Session["idstructure"].ToString();


                            string cryptoKey = WebConfigurationManager.AppSettings["CryptoKey"].ToString();

                            Sha1 sha1 = new Sha1(string.Format("{0,4:0000}", int.Parse(structureId)) + "|" + eventid + cryptoKey);
                            // trace = "t2";
                            string myhash = sha1.getSha1();

                            CustomerEntity customer = new CustomerEntity()
                            {
                                Email = jsonPayPalUser.email,
                                Name = jsonPayPalUser.name,
                                FirstName = jsonPayPalUser.name,
                                Hash = myhash,
                                Password = jsonPayPalUser.email,

                                Address1 = jsonPayPalUser.address.street_address,
                                City = jsonPayPalUser.address.locality,
                                PostalCode = jsonPayPalUser.address.postal_code,
                                Country = jsonPayPalUser.address.country
                            };

                            LoginDefault l = new LoginDefault();
                            int resultLogin = l.GetLogin(structureId, "0", customer);
                            if (resultLogin == 0)
                            {
                                return Login.CreateCustomerWM(int.Parse(structureId), "0", customer, false, true);
                            }
                            else
                            {
                                customer.Password = "PAYPALCONNECT";
                                return Login.LoginCustomerWM(int.Parse(structureId), customer);
                            }

                        }
                    }
                    else
                    {
                        logger.Error("paypal userinfo response=" + response.StatusCode);
                        return "danger:msg_error_paypal_bad_authorization";
                    }
                }
            }
            else
            {
                logger.Error("paypal tokenservice response=" + response.StatusCode);
                return "danger:msg_error_paypal_bad_request";
            }


            return "danger:";
        }


        [WebMethod]
        [ScriptMethod(ResponseFormat = ResponseFormat.Json)]
        public static string ConnectUnidy(string IdStructure, string codeUnidy, string redirectUrl)
        {
            //string codeUnidy = "";

            log.Debug(IdStructure, "ConnectUnidy(" + codeUnidy + ")....");

            //codeUnidy = codeUnidy;


            //JObject jsonObj = JObject.Parse(codeUnidy);

            System.Net.ServicePointManager.SecurityProtocol = System.Net.SecurityProtocolType.Tls12 | System.Net.SecurityProtocolType.Tls11 | System.Net.SecurityProtocolType.Tls11;

            mySSC = new MyDictionary();
            mySSC = mySSC.GetDictionaryFromCache(int.Parse(IdStructure));


            // string urlTokenService = utilitaires2010.Initialisations.GetKeyAppSettings("unidyconnect_urltokenservice");

            //jsonObj.GetValue("access_token");
            //  Session["refresh_token"] = jsonObj.GetValue("refresh_token");
            //Session["scope"] = jsonObj.GetValue("scope");

            /*
              < add key = "unidyconnect_urluserinfo" value = "https://sv98.staging.unidy.de/oauth/userinfo" />
         < add key = "unidyconnect_redirect_uri" value = "https://dev.themisweb.fr/customer/login.aspx?idstructure=0224" />      
            < add key = "unidyconnect_clientid" value = "TZieCq4keD4p3cPX1nLHFr-fo5WXrgNssOdtpFf-V2Y" />         
               < add key = "unidyconnect_clientsecret" value = "9dS9YKHb-Xv-kzGySyM3ry4WWWGnyPYwWqnrOzKWRkk" />            
                  < add key = "unidyconnect_unidyconnect_urluserinfo" value = "https://sv98.staging.unidy.de/oauth/userinfo" />
                  */

            string urlTokenService = "";
            if (mySSC.Contains("UNIDYURLTOKENSERVICE") && !string.IsNullOrWhiteSpace(mySSC["UNIDYURLTOKENSERVICE"].ToString()))
            {
                urlTokenService = mySSC["UNIDYURLTOKENSERVICE"].ToString();
            }
            string urlUInfo = "";
            if (mySSC.Contains("UNIDYURLUINFO") && !string.IsNullOrWhiteSpace(mySSC["UNIDYURLUINFO"].ToString()))
            {
                urlUInfo = mySSC["UNIDYURLUINFO"].ToString();
            }

            //  string urlUInfo = MyConfigurationManager.AppSettings("unidyconnect_urluserinfo");

            string openIdSecret = "";
            if (mySSC.Contains("UNIDYSECRET") && !string.IsNullOrWhiteSpace(mySSC["UNIDYSECRET"].ToString()))
            {
                openIdSecret = mySSC["UNIDYSECRET"].ToString();
            }
            string openIdClient = "";
            if (mySSC.Contains("UNIDYCLIENT") && !string.IsNullOrWhiteSpace(mySSC["UNIDYCLIENT"].ToString()))
            {
                openIdClient = mySSC["UNIDYCLIENT"].ToString();
            }
            //string redirectUrl = "";
            //if (mySSC.Contains("OPENIDREDIRECTURL") && !string.IsNullOrWhiteSpace(mySSC["OPENIDREDIRECTURL"].ToString()))
            //{
            //    redirectUrl = mySSC["OPENIDREDIRECTURL"].ToString();
            //}

            RestClient client = new RestClient(urlTokenService);
            RestRequest request = new RestRequest(Method.POST);

            request.AddHeader("cache-control", "no-cache");
            // request.AddHeader("Authorization", "Basic QWNTMjBCZWczcWQ5SldLOS0zTVNBRjJIaXg2bUJNN21YUm8taGlfdC1CV2ZxM25WcWs0RlEtNlN1aC0xaUVSR0xYZ25JVnN6c1lBTXhodlE6RUlvcFpGN0dvalJyY1E0clZjSU52T21LcEQ2UjhMb3E1ZXB3X0dXVGZHTU9LM2tGLTRLZjBBbmJ3dHdnWTRKUUFKZExnNmZpdXVZUVlRYy0=");
            request.AddHeader("Content-Type", "application/x-www-form-urlencoded");

            request.AddParameter("grant_type", "authorization_code");
            request.AddParameter("code", "" + codeUnidy);
            //request.AddParameter("redirect_uri", "https://dev.themisweb.fr/customer/login.aspx?idstructure=0224");
            request.AddParameter("redirect_uri", redirectUrl);
            request.AddParameter("client_id", openIdClient);
            request.AddParameter("client_secret", openIdSecret);
            request.AddParameter("response_type", "token");
            //request.AddParameter("undefined", "client_secret=9dS9YKHb-Xv-kzGySyM3ry4WWWGnyPYwWqnrOzKWRkk");

            //https://sv98.staging.unidy.de/oauth/authorize?client_id=TZieCq4keD4p3cPX1nLHFr-fo5WXrgNssOdtpFf-V2Y&response_type=code&scope=openid profile email address&redirect_uri=https://dev.themisweb.fr/customer/login.aspx?idstructure=0224


            //    "code=" + codeUnidy, ParameterType.RequestBody);


            IRestResponse response = client.Execute(request);

            if (response.StatusCode == HttpStatusCode.OK)
            {
                if (response.Content != "")
                {
                    string stringResponse = response.Content;
                    JavaScriptSerializer js = new JavaScriptSerializer();
                    App_Code.OpenIdResponseGetToken jsonrespon = js.Deserialize<App_Code.OpenIdResponseGetToken>(stringResponse);

                    string myAccToken = jsonrespon.access_token;
                    string myIdToken = jsonrespon.id_token;

                    client = new RestClient(urlUInfo);
                    request = new RestRequest(Method.GET);
                    client.AddDefaultHeader("Authorization", string.Format("Bearer {0}", myAccToken));

                    response = client.Execute(request);
                    if (response.StatusCode == HttpStatusCode.OK)
                    {
                        if (response.Content != "")
                        {
                            stringResponse = response.Content;

                            log.Debug(IdStructure, "unidyconnect_urluserinfo ok content=" + stringResponse);
                            js = new JavaScriptSerializer();

                            string eventid = "0";

                            OpenResponseGetInfo jsponOpenIdUserInfo = js.Deserialize<OpenResponseGetInfo>(stringResponse);

                            //string structureId = System.Web.HttpContext.Current.Session["idstructure"].ToString();


                            string cryptoKey = WebConfigurationManager.AppSettings["CryptoKey"].ToString();

                            Sha1 sha1 = new Sha1(string.Format("{0,4:0000}", int.Parse(IdStructure)) + "|" + eventid + cryptoKey);
                            // trace = "t2";
                            string myhash = sha1.getSha1();

                            string myExterneId = jsponOpenIdUserInfo.sub;

                            CustomerEntity customer = new CustomerEntity()
                            {



                                ComplementWeb = new CustomerEntity.IdentiteComplementWebcl()
                                {
                                    Login = myExterneId,
                                    DoitChanger = false,
                                    Password = jsponOpenIdUserInfo.email
                                },

                                //IdentiteComplement = myExterneId,

                                Email = jsponOpenIdUserInfo.email,
                                Name = jsponOpenIdUserInfo.family_name,
                                SurName = jsponOpenIdUserInfo.family_name,
                                FirstName = jsponOpenIdUserInfo.given_name,
                                Hash = myhash,
                                Password = myExterneId,
                                DateOfBirthday = jsponOpenIdUserInfo.birthdate,
                                Address1 = jsponOpenIdUserInfo.address.address_line_1,
                                Address2 = jsponOpenIdUserInfo.address.address_line_2,
                                City = jsponOpenIdUserInfo.address.locality,
                                PostalCode = jsponOpenIdUserInfo.address.postal_code,
                                Country = jsponOpenIdUserInfo.address.country_code

                            };

                            LoginOpenId l = new LoginOpenId();



                            int resultLogin = l.GetLogin(int.Parse(IdStructure), jsponOpenIdUserInfo);
                            if (resultLogin == 0) // creer une nouvelle identit�
                            {
                                int newIdentityId = l.CreateIdentity(int.Parse(IdStructure), customer);

                                resultLogin = l.GetLogin(int.Parse(IdStructure), jsponOpenIdUserInfo);
                                newIdentityId = resultLogin;

                                //return "true:" + newIdentityId.ToString() + ":" + "123";

                                customer.Password = myExterneId;
                                customer.IdentiteId = newIdentityId;
                                customer.Email = "";

                                return Login.LoginCustomerWM(int.Parse(IdStructure), customer);

                                //return Login.CreateCustomerWM(int.Parse(IdStructure), "0", customer, false, true);
                            }
                            else
                            {
                                customer.Password = myExterneId;
                                customer.Email = "";
                                customer.IdentiteId = resultLogin;

                                return Login.LoginCustomerWM(int.Parse(IdStructure), customer);
                                //   return Login.LoginCustomerWM(int.Parse(IdStructure), customer);
                            }

                        }
                    }
                    else
                    {
                        log.Error(IdStructure, "unidyconnect_urluserinforesponse=" + response.StatusCode);
                        return "danger:msg_error_unidy_bad_authorization";
                    }
                }
            }
            else
            {
                log.Error(IdStructure, "unidy tokenservice response=" + response.StatusCode + " " + response.Content);
                return "danger:msg_error_unidy_bad_request";
            }


            return "danger:";
        }




        [WebMethod]
        [ScriptMethod(ResponseFormat = ResponseFormat.Json)]
        public static string ConnectFb(int structureId, string fbuserid, int eventid, string plateformCode, string hash, string fbemail, string fbaccesstoken)
        {

            //int numPostalTelEmail = int.Parse(mySSC["VARIABLESEMAIL"].ToString());
            string lang = App_Code.Initialisations.GetUserLanguage();

            List<CustomJsonSettings> listJsonSettingsToMerge = new List<CustomJsonSettings>
            {
                new CustomJsonSettings() { KeyOfJsonPath = "physicalPathOfSettingsJSON", IsDefault = true },
                new CustomJsonSettings() { KeyOfJsonPath = "physicalPathOfSettingsJSON", IsDefault = false },
                new CustomJsonSettings() { KeyOfJsonPath = "physicalPathOfSettingsPlateformJSON", IsDefault = false }
            };


            dynamic globalPlateform = utilitaires2010.Initialisations.GetMergeSettings(listJsonSettingsToMerge, structureId, eventid, 0, plateformCode, lang);


            dynamic facebookConnect = globalPlateform.login.facebookConnect.Value;
            if (globalPlateform.login != null && globalPlateform.customer != null && globalPlateform.customer.login != null && globalPlateform.customer.login.facebookConnect != null)
            {
                facebookConnect = globalPlateform.customer.login.facebookConnect.Value;
            }

            //si le facebook est activ�
            if (facebookConnect != null)
            {
                //on vient lire la cl� secr�te dans le config.ini
                MyDictionary mySSC = new MyDictionary();
                mySSC = mySSC.GetDictionaryFromCache(structureId);

                string facebookAppSecret = "";
                if (mySSC.Contains("FACEBOOKAPPLICATIONSECRET") && !string.IsNullOrWhiteSpace(mySSC["FACEBOOKAPPLICATIONSECRET"].ToString()))
                {
                    facebookAppSecret = mySSC["FACEBOOKAPPLICATIONSECRET"].ToString();
                }


                dynamic facebookApplicationId = globalPlateform.login.facebook.applicationId.Value;
                if (globalPlateform.login != null && globalPlateform.customer != null && globalPlateform.customer.login != null && globalPlateform.customer.login.facebook != null && globalPlateform.customer.login.facebook.applicationId != null)
                {
                    facebookApplicationId = globalPlateform.customer.login.facebook.applicationId.Value;
                }



                //si l'application Id de facebook et la cl� secr�te ne sont pas vides ou nulles alors on check Facebook
                if (!string.IsNullOrEmpty(facebookAppSecret) && !string.IsNullOrEmpty(facebookApplicationId))
                {
                    FacebookClient fb = new FacebookClient(fbaccesstoken);

                    string fbAppId = facebookApplicationId;
                    string fbAppSecret = facebookAppSecret;

                    /*
                       string fbAppId = utilitaires2010.Initialisations.GetKeyAppSettings("facebook_appid");
                       string fbAppSecret = utilitaires2010.Initialisations.GetKeyAppSettings("facebook_appsecret");

                        dynamic result = fb.Get("oauth/access_token", new
                        {
                            client_id = fbAppId,
                            client_secret = fbAppSecret,
                            grant_type = "client_credentials"
                        });
                    */

                    // var accessToken = result.access_token;

                    // fb.AccessToken = accessToken;
                    fb.AppId = fbAppId;//ConfigurationManager.AppSettings["facebook:AppId"],
                    fb.AppSecret = fbAppSecret; // ConfigurationManager.AppSettings["facebook:AppSecret"]


                    dynamic me = fb.Get(fbuserid + "?fields=first_name,last_name,id,email");

                    // dynamic me = fb.Get("me?fields=first_name,last_name,id,email");
                    dynamic uId = me.id;
                    string email = me.email;
                    string first_name = me.first_name;
                    string last_name = me.last_name;

                    if (fbuserid == uId && fbemail == email)
                    {
                        //ok verif in DB
                        string idStructure = System.Web.HttpContext.Current.Session["IdStructure"].ToString();

                        try
                        {
                            // var test = LoginType.GetTypeLogin(int.Parse(idStructure)).GetLog(idStructure, email, "", "", true);
                            CustomerEntity customer = new CustomerEntity()
                            {
                                Email = email,
                                Name = last_name,
                                FirstName = first_name,
                                Hash = hash,
                                Password = uId
                            };

                            LoginDefault l = new LoginDefault();
                            int resultLogin = l.GetLogin(idStructure, eventid.ToString(), customer);

                            if (resultLogin == 0)
                            {
                                return Login.CreateCustomerWM(int.Parse(idStructure), eventid.ToString(), customer, false, true);
                            }
                            customer.Password = "FACEBOOKCONNECT";
                            return Login.LoginCustomerWM(int.Parse(idStructure), customer);
                        }
                        catch (Exception ex)
                        {
                            throw ex;
                        }
                    }

                }
                else
                {
                    GestionTraceManager.WriteLogError(structureId, "facebookAppSecret dans le config.ini ou facebookApplicationId dans APPSETINGS est vide ou null ");
                }
            }

            return "danger:";

        }


        [WebMethod]
        [ScriptMethod(ResponseFormat = ResponseFormat.Json)]
        public static string IsExistEmail(int idStructure, string email)
        {
            //int idStructure = 0;
            //string email = "";

            bool bUnicity = true;
            MyDictionary mySSC = new MyDictionary();
            mySSC = mySSC.GetDictionaryFromCache(idStructure);
            if (mySSC.Contains("CREATEPROFILMAILUNICITY") && mySSC["CREATEPROFILMAILUNICITY"] == "-1")
            {
                bUnicity = false;
            }

            if (bUnicity)
            {

                int valueAttributConfigini = int.Parse(mySSC["VARIABLESEMAIL"].ToString());
                //DataSet dsIdentCheck = LoginType.GetTypeLogin(idStructure).GetLoginTable(String.Format("{0,4:0000}", idStructure), email, "");
                IdentiteEntity identiteCheck = LoginType.GetTypeLogin(idStructure).GetLoginTable(string.Format("{0,4:0000}", idStructure), email, "", valueAttributConfigini, true);


                if (identiteCheck != null && (!string.IsNullOrEmpty(identiteCheck.Comment) && identiteCheck.Comment.Contains("Error")))
                {
                    logger.Error("IsExistEmail : true");
                    var logsLevel = customerArea.App_Code.Initialisations.GetKeyAppSettings("logsLevel");

                    if (logsLevel != utilitaires2010.LogLevel.NORMAL.ToString())
                    {
                        GestionTraceManager.WriteLog(idStructure, "IsExistEmail - error : dsIdentCheck est null");

                    }

                    return "true:" + "EXISTINGEMAIL";
                    /* if (dsIdentCheck.Tables[0].Rows.Count > 0)
                     {
                         logger.Error("IsExistEmail : true");
                         return "true:" + "EXISTINGEMAIL";
                     }*/
                }
                else
                {
                    if (identiteCheck == null)
                    {
                        //GestionTraceManager.WriteLog(idStructure, "identiteCheck == null   - existe pas d�j� !! " + email );
                        logger.Debug("identiteCheck == null -existe pas !!" + email);
                    }
                    else
                    {
                        // GestionTraceManager.WriteLog(idStructure, "identiteCheck != null ou  existe  " + email);
                        logger.Debug("identiteCheck != null ou existe d�j�");

                        logger.Error("IsExistEmail : true");
                        return "true:" + "EXISTINGEMAIL";
                    }
                    //throw new Exception("IsExistEmail - error : identiteCheck est null !!! param�trage");
                }


            }
            else
            {
                logger.Error("IsExistEmail - CREATEPROFILMAILUNICITY : " + mySSC["CREATEPROFILMAILUNICITY"]);
                GestionTraceManager.WriteLogError(idStructure, "IsExistEmail - CREATEPROFILMAILUNICITY : " + mySSC["CREATEPROFILMAILUNICITY"]);

                throw new Exception("IsExistEmail - CREATEPROFILMAILUNICITY : " + mySSC["CREATEPROFILMAILUNICITY"]);
            }

            return "false:OK";

        }

        [WebMethod]
        [System.Web.Script.Services.ScriptMethod]
        public static string BtnSendPassWord(int idStructure, string eventid, string identif)
        {


            if (mySSC != null)
            {

                int numPostalTelEmail = int.Parse(mySSC["VARIABLESEMAIL"].ToString());

                GestionTraceManager.WriteLog(idStructure, "SendPassWord numpostal email : " + numPostalTelEmail);

                return wuctrlLoginConnect.SendPassWord(idStructure, eventid, identif, numPostalTelEmail);

            }
            //return "false:msg_error_reload_page"; TODO g�rer le cas ou il n'y a plus de session
            return string.Empty;

        }

        /*
                [WebMethod]
                [System.Web.Script.Services.ScriptMethod]
                public static string BtnLoginWM(int idStructure, string email, string passw, string identifid)
                {

                    logger.Debug("BtnLoginWM(" + idStructure + "," + email + "," + passw + ", "+identifid+")...");
                    GestionTraceManager.WriteLog(idStructure, "BtnLoginWM ");


                    if (!string.IsNullOrEmpty(email))
                        return wuctrlLoginConnect.CheckLoginStatic(idStructure, email, passw);
                    else
                        return wuctrlLoginConnect.CheckLoginStatic(idStructure, identifid, passw);
                }*/


        [WebMethod]
        [System.Web.Script.Services.ScriptMethod]
        public static string LoginCustomerWM(int structureid, CustomerEntity customer)
        {

            logger.Debug("BtnLoginWM(" + structureid + "," + customer.Email + "," + customer.Password + ", " + customer.IdentiteId + ")...");
            GestionTraceManager.WriteLog(structureid, "BtnLoginWM ");


            if (!string.IsNullOrEmpty(customer.Email))
            {
                return wuctrlLoginConnect.CheckLoginStatic(structureid, customer.Email, customer.Password);
            }
            else
            {
                return wuctrlLoginConnect.CheckLoginStatic(structureid, customer.IdentiteId.ToString(), customer.Password);
            }
        }

        [WebMethod]
        [System.Web.Script.Services.ScriptMethod]
        public static string GetPasswordCustomerWM(int structureid, string eventid, string forgottenemail)
        {

            GestionTraceManager.WriteLog(structureid, "GetPasswordCustomerWM - start ");

            MyDictionary mySSC = new MyDictionary();
            mySSC = mySSC.GetDictionaryFromCache(structureid);


            if (mySSC != null)
            {
                if (App_Code.Initialisations.IsValidEmail(forgottenemail))
                {
                    var logsLevel = customerArea.App_Code.Initialisations.GetKeyAppSettings("logsLevel");

                    if (logsLevel != utilitaires2010.LogLevel.NORMAL.ToString())
                    {
                        GestionTraceManager.WriteLog(structureid, "GetPasswordCustomerWM - Email valid ");
                    }

                    int numPostalTelEmail = int.Parse(mySSC["VARIABLESEMAIL"].ToString());
                    if (logsLevel != utilitaires2010.LogLevel.NORMAL.ToString())
                    {
                        GestionTraceManager.WriteLog(structureid, "SendPassWord numpostal email : " + numPostalTelEmail);
                    }
                    return wuctrlLoginConnect.SendPassWord(structureid, eventid, forgottenemail, numPostalTelEmail);

                }
                GestionTraceManager.WriteLogError(structureid, "GetPasswordCustomerWM - Email invalid ");
                return "danger:msg_error_get_password_bad_email";
            }
            GestionTraceManager.WriteLogError(structureid, "GetPasswordCustomerWM - mySSC is null");

            return "danger:msg_error_get_password";
        }




        [WebMethod]
        [System.Web.Script.Services.ScriptMethod]
        public static int ConnectUser_id(string identiteid, string _hash)
        {
            // check hash
            string idStructure = System.Web.HttpContext.Current.Session["IdStructure"].ToString();
            string cryptoKey = WebConfigurationManager.AppSettings["CryptoKey"].ToString();
            Sha1 sha1 = new Sha1(idStructure + "|" + identiteid + cryptoKey);
            string hash = sha1.getSha1();
            if (_hash != hash)
            {
                return 0;
            }
            else
            {
                return LoginType.GetTypeLogin(int.Parse(idStructure)).GetLogin(idStructure, "", identiteid, "", true);
            }

        }



        /* [WebMethod]
         [System.Web.Script.Services.ScriptMethod]
         public static string BtnCreateWM(int idStructure, string eventid, string name, string firstname, string email,
             string civ, string sex, string dob, string adress1, string adress2, string adress3, string adress4, string cp, string city, string country, 
             string homenum, string mobilenum, string faxnum,
             string password,
             string htFortblInfosComps, string htFortblInfosCompsNotCheck, string ctrlh, string comment)
         {
             string idpa = "0";
             if (System.Web.HttpContext.Current.Session["ProfilAcheteurId"] != null)
             {
                 idpa = (string)System.Web.HttpContext.Current.Session["ProfilAcheteurId"];
             }

             return wctrLoginCreation.CreateLoginStatic(idStructure, eventid, name, firstname, email, 
                 civ, sex, dob, adress1, adress2, adress3, adress4, cp, city, country, homenum, mobilenum, faxnum, password,
                 htFortblInfosComps, htFortblInfosCompsNotCheck,   ctrlh, idpa, comment);
             //return wuctrlLoginConnect.CheckLoginStatic(idStructure, identif, passw);
         }

         [WebMethod]
         [System.Web.Script.Services.ScriptMethod]
         public static string BtnCreateWM2(int idStructure, string eventid, string name, string firstname, string email,
             string civ, string sex, string dob, string adress1, string adress2, string adress3, string adress4, string cp, string city, string country,
             string homenum, string mobilenum, string faxnum,
             string password,
             string htFortblInfosComps, string htFortblInfosCompsNotCheck, string ctrlh, string comment)
         {
             //string idpa = "0";
             //if (System.Web.HttpContext.Current.Session["ProfilAcheteurId"] != null)
             //{
             //    idpa = (string)System.Web.HttpContext.Current.Session["ProfilAcheteurId"];
             //}
             logger.Debug("BtnCreateWM2 : creer identite step1 ");
             string idpa = App_Code.Initialisations.GetProfilAcheteurID();

             return wctrLoginCreation.CreateLoginStatic(idStructure, eventid, name, firstname, email,
                 civ, sex, dob, adress1, adress2, adress3, adress4, cp, city, country, homenum, mobilenum, faxnum, password,
                 htFortblInfosComps, htFortblInfosCompsNotCheck, ctrlh, idpa, comment);
             //return wuctrlLoginConnect.CheckLoginStatic(idStructure, identif, passw);
         }
         */


        /// <summary>
        /// 
        /// </summary>
        /// <param name="structureid"></param>
        /// <param name="eventid"></param>
        /// <param name="customer"></param>
        /// <param name="isLight"></param>
        /// <param name="isCreateFromApi">true si on vien de facebook ou paypal connect ou autres</param>
        /// <returns></returns>
        [WebMethod]
        [System.Web.Script.Services.ScriptMethod]
        public static string CreateCustomerWM(int structureid, string eventid, CustomerEntity customer, bool isLight, bool isCreateFromApi)
        {
            logger.Debug("CreateCustomerWM : creer identite step1 ");
            string idpa = App_Code.Initialisations.GetProfilAcheteurID();


            System.Web.HttpContext.Current.Session["idstructure"] = structureid;
            logger.Debug("CreateCustomerWM : idstructure en session " + structureid);

            return wctrLoginCreation.CreateLoginStatic(structureid, eventid, customer.Name, customer.FirstName, customer.Email,
             customer.CiviliteId, customer.Sex, customer.DateOfBirthday, customer.Address1, customer.Address2, customer.Address3, customer.Address4,
             customer.PostalCode, customer.City, customer.Country, customer.HomeNum, customer.MobileNum, customer.FaxNum, customer.Password,
             customer.InfosCompChecked, customer.InfosCompNotChecked, customer.Hash, idpa, customer.Comment, customer.IdentiteComplement, customer.PhysiqueMorale, customer.TitreId, false, customer.LangCodeOfUser, isLight, isCreateFromApi);
        }

        [WebMethod]
        [System.Web.Script.Services.ScriptMethod]
        public static string CreateGuestCustomer(int structureid, string eventid, CustomerEntity customer, bool isLight)
        {
            logger.Debug("CreateGuestCustomer : creer identite step1 ");
            string idpa = App_Code.Initialisations.GetProfilAcheteurID();


            System.Web.HttpContext.Current.Session["idstructure"] = structureid;
            logger.Debug("CreateGuestCustomer : idstructure en session " + System.Web.HttpContext.Current.Session["idstructure"]);

            return wctrLoginCreation.CreateLoginStatic(structureid, eventid, customer.Name, customer.FirstName, customer.Email,
             customer.CiviliteId, customer.Sex, customer.DateOfBirthday, customer.Address1, customer.Address2, customer.Address3, customer.Address4,
             customer.PostalCode, customer.City, customer.Country, customer.HomeNum, customer.MobileNum, customer.FaxNum, customer.Password,
             customer.InfosCompChecked, customer.InfosCompNotChecked, customer.Hash, idpa, customer.Comment, customer.IdentiteComplement, customer.PhysiqueMorale, customer.TitreId, true, "", isLight, false);
        }

        [WebMethod]
        [System.Web.Script.Services.ScriptMethod]
        public static string GetHtmlAuthentifDiv()
        {

            int resultEvent = 0;
            if (System.Web.HttpContext.Current.Session["eventId"] != null && int.TryParse(System.Web.HttpContext.Current.Session["eventId"].ToString(), out resultEvent))
            {

            }

            string idpa = "0";
            if (System.Web.HttpContext.Current.Session["ProfilAcheteurId"] != null)
            {
                idpa = (string)System.Web.HttpContext.Current.Session["ProfilAcheteurId"];
            }

            return Authentifications.GethtmlDivAuthentif(resultEvent.ToString(), idpa);
        }
    }
}
