<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="debug-paypal.aspx.cs" Inherits="customerArea.debug_paypal" %>

<!DOCTYPE html>
<html>
<head>
    <title>Debug PayPal</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h1>Debug PayPal - Structure 991</h1>
    
    <div id="debug-info"></div>
    
    <h2>Test direct de l'API PayPal</h2>
    <button id="test-paypal-api">Tester l'API PayPal</button>
    <div id="api-result"></div>
    
    <span id="settingsCustomer" runat="server" class="d-none"></span>
    <span id="settingsDefaultCustomer" runat="server" class="d-none"></span>

    <script type="text/javascript">
        $(document).ready(function() {
            // Afficher les settings
            var settings = "";
            if ($('#settingsDefaultCustomer').text() != "") {
                settings = $.extend(true, {}, JSON.parse($('#settingsDefaultCustomer').text()), JSON.parse($('#settingsCustomer').text()));
                
                var info = "<h2>Settings PayPal actuels :</h2>";
                
                if (settings.login && settings.login.payPal) {
                    info += "<p><strong>Client ID:</strong> " + settings.login.payPal.appid + "</p>";
                    info += "<p><strong>Secret:</strong> " + (settings.login.payPal.secret ? settings.login.payPal.secret.substring(0, 10) + "..." : "Non défini") + "</p>";
                    info += "<p><strong>Return URL:</strong> " + settings.login.payPal.returnurl + "</p>";
                    
                    // Construire l'URL PayPal pour test
                    var clientId = settings.login.payPal.appid;
                    var redirectUri = settings.login.payPal.returnurl;
                    var scopes = "openid email profile address";
                    
                    var authUrl = "https://www.sandbox.paypal.com/signin/authorize" +
                        "?client_id=" + clientId +
                        "&response_type=code" +
                        "&scope=" + encodeURIComponent(scopes) +
                        "&redirect_uri=" + encodeURIComponent(redirectUri);
                    
                    info += "<h3>URL PayPal générée :</h3>";
                    info += "<p><a href='" + authUrl + "' target='_blank'>" + authUrl + "</a></p>";
                    
                } else {
                    info += "<p style='color: red;'><strong>ERREUR:</strong> Settings PayPal non trouvés !</p>";
                }
                
                info += "<h3>Settings complets :</h3>";
                info += "<pre>" + JSON.stringify(settings, null, 2) + "</pre>";
                
                $('#debug-info').html(info);
            } else {
                $('#debug-info').html("<p style='color: red;'>Aucun settings trouvé !</p>");
            }
            
            // Test API PayPal
            $('#test-paypal-api').click(function() {
                if (settings.login && settings.login.payPal) {
                    var clientId = settings.login.payPal.appid;
                    var clientSecret = settings.login.payPal.secret;
                    
                    // Test de base64 encoding des credentials
                    var credentials = btoa(clientId + ':' + clientSecret);
                    
                    $('#api-result').html('<p>Test en cours...</p>');
                    
                    $.ajax({
                        url: 'https://api.sandbox.paypal.com/v1/oauth2/token',
                        type: 'POST',
                        headers: {
                            'Authorization': 'Basic ' + credentials,
                            'Content-Type': 'application/x-www-form-urlencoded'
                        },
                        data: 'grant_type=client_credentials',
                        success: function(response) {
                            $('#api-result').html('<p style="color: green;">✓ API PayPal fonctionne !</p><pre>' + JSON.stringify(response, null, 2) + '</pre>');
                        },
                        error: function(xhr, status, error) {
                            $('#api-result').html('<p style="color: red;">✗ Erreur API PayPal:</p><pre>' + xhr.responseText + '</pre>');
                        }
                    });
                }
            });
        });
    </script>
</body>
</html>
