<%@ Page Language="C#" MasterPageFile="~/pagemaster.Master" AutoEventWireup="true" CodeBehind="Login.aspx.cs" Inherits="customerArea.Login" Culture="auto" meta:resourcekey="PageResource1" UICulture="auto" %>
<%@ Register Assembly="System.Web.Extensions, Version=1.0.61025.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35"
    Namespace="System.Web.UI" TagPrefix="asp" %>
<%@ Register Src="wctrlLoginConnect.ascx" TagName="wctrlLoginConnect" TagPrefix="uc1" %>
<%@ Register Src="wctrLoginCreation.ascx" TagName="wctrLoginCreation" TagPrefix="uc2" %>
<%@ Register Src="wctrLoginCreationLight.ascx" TagName="wctrLoginCreationLight" TagPrefix="ucLight" %>
<%@ Register Src="wctrlGuestLogin.ascx" TagName="wctrlGuestLogin" TagPrefix="ucGuestLogin" %>
<asp:Content ID="ctIdentification" ContentPlaceHolderID="ctPlaceHold" runat="Server">
    <asp:ScriptManager ID="ScriptManager1" runat="server" EnablePageMethods="True">
    </asp:ScriptManager>
    <span id='receptVar' class='hide' data-structureid="<%=System.Web.HttpContext.Current.Session[" idstructure"] %>" ></span>
    <div class="container">
        <div class="row">
            <div class="col">
                <div class="card-header d-none"><span runat="server" id="spanIdentification" clientidmode="Static">Identification</span></div>
                <div id="steps">
                    <asp:Literal ID="litForSteps" runat="server"></asp:Literal>
                </div>
                <!-- commentaires haut -->
                <div id="commentsPageHaut">
                    <asp:Literal ID="litForCommentaireHaut" runat="server"></asp:Literal>
                </div>
                <div class="panel-body" id="logincontainer">
                    <!-- Nav tabs -->
                    <ul class="nav nav-tabs d-none" id="myTab" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active show" href="#loginTab" role="tab" data-toggle="tab" runat="server" id="aDejaUnCompte" clientidmode="Static">J'ai déjà un compte</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#createprofilTab" role="tab" data-toggle="tab" runat="server" id="aCreerCompte" clientidmode="Static">Créer un compte</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#forgottenPwTab" role="tab" data-toggle="tab" runat="server" id="aOublierCompte" clientidmode="Static">Mot de passe oublié</a>
                        </li>
                    </ul>
                    <div class="tab-content">
                        <div class="tab-pane fade in active show " id="loginTab">
                            <div class="row">
                                <div class="col-12 col-lg" id="divconnectWrapper">
                                    <div id="divconnect">
                                        <h2 class="text-center" data-trad="title_login">Login</h2>
                                        <!-- UC login connect -->
                                        <uc1:wctrlLoginConnect ID="WctrlLoginConnect1" runat="server" />
                                    </div>
                                    <hr>
                                    <p class="text-center font-weight-bold"><span data-trad="msg_dont_have_account">Do not have an account yet ?</span> <a href="#" data-tabtarget="#createprofilTab" class="" id="createAccount" data-trad="link_sign_up">Sign up</a></p>
                                </div>
                                <div class="col-12 col-lg" id="divguestloginWrapper" runat="server" ClientIDMode="Static">
                                    <div id="divguestlogin">
                                        <h2 class="text-center" data-trad="title_guest_login">Achat sans compte</h2>
                                        <ucGuestLogin:wctrlGuestLogin ID="wctrlGuestLogin" runat="server" />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="createprofilTab">
                            <div class="row">
                                <div class="col-12 col-lg-6 offset-lg-3 col-md-8 offset-md-2">
                                    <div id="divcreate">
                                        <h2 class="text-center" data-trad="title_sign_up">Sign up</h2>
                                        <uc2:wctrLoginCreation ID="WctrLoginCreation1" runat="server" />
                                        <ucLight:wctrLoginCreationLight ID="WctrLoginCreationLight" runat="server" />
                                        <hr>
                                        <p class="text-center font-weight-bold"><span data-trad="lbl_already_have_account">I already have an account ?</span> <a href="#" data-tabtarget="#loginTab" class="" id="loginAccount" data-trad="btn_login">Connect</a></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="forgottenPwTab">
                            <div class="row">
                                <div class="col-12 col-lg-6 offset-lg-3 col-md-8 offset-md-2">
                                    <div id="divforgotten">
                                        <h2 class="text-center" data-trad="title_forgot_password">Forgot your password?</h2>
                                        <div id="explicationForgotPassword" runat="server" data-trad="msg_entente_forgot_password">
                                            Enter your email address with which you registered. We will send you an email with a link to reset your password.
                                        </div>
                                        <div id="forgottenForm" role="form">
                                            <div class="col-12">
                                                <div class="row form-group">
                                                    <label class="sr-only">E-mail</label>
                                                    <input type="email" name="forgottenemail" class="form-control" placeholder="E-mail" data-tradplaceholder="placeholder_resetemail" required>
                                                    <div class="invalid-feedback" data-trad="msg_error_email">Please enter a valid email</div>
                                                </div>
                                                <div class="row form-group">
                                                    <button class="btn btn-primary btn-block" data-trad="btn_send" id="btnSendForgetPass">Send</button>
                                                </div>
                                            </div>
                                            <hr>
                                            <p class="text-center font-weight-bold"><span data-trad="msg_remember_my_account">Finally, I remember it ...</span> <a href="#" data-tabtarget="#loginTab" class="" id="loginAccount" data-trad="btn_login">Connect</a></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- commentaires bas -->
                <div id="divCommentaireBas" class="small">
                    <asp:Literal ID="litForCommentaireBas" runat="server"></asp:Literal>
                </div>
            </div>
        </div>
    </div>
    <asp:HyperLink ID="hlHomePage" runat="server" class="d-none">homePage</asp:HyperLink>
</asp:Content>
<asp:Content ContentPlaceHolderID="ExtraScripts" runat="server">
    <!-- *************** FACEBOOK *************** -->
    <script>
    if (settings.login.facebookConnect &&  settings.login.facebook.applicationId != "") {
        console.log("Facebook App ID utilisé:", settings.login.facebook.applicationId);
        window.fbAsyncInit = function() {
            FB.init({
                appId: settings.login.facebook.applicationId,
                //appId: '683630601987963',
                cookie: true, // enable cookies to allow the server to access 
                // the session
                autoLogAppEvents: true,
                xfbml: true, // parse social plugins on this page
                version: 'v9.0' // use graph api version 2.8
            });
            FB.AppEvents.logPageView();
        };

        var urlJsLang = "//connect.facebook.net/en_US/sdk.js";
        if (settings.login.facebook.urlJsLang != "")
            urlJsLang = settings.login.facebook.urlJsLang;


        (function(d, s, id) {
            var js, fjs = d.getElementsByTagName(s)[0];
            if (d.getElementById(id)) return;
            js = d.createElement(s);
            js.id = id;
            js.src = urlJsLang;
            fjs.parentNode.insertBefore(js, fjs);
        }(document, 'script', 'facebook-jssdk'));


        function LoginAPI() {
            FB.login(function(response) {

                if (response.authResponse) {
                    //console.log('Welcome!  Fetching your information.... ');
                    //console.log(response); // dump complete info
                    access_token = response.authResponse.accessToken; //get access token
                    user_id = response.authResponse.userID; //get FB UID

                    FB.api('/me', { fields: 'email' }, function(response) {
                        var email = response.email;
                        var name = response.name;

                        //call webservice
                        ConnectUserFB(user_id, access_token, email);
                    });

                } else {
                    //user hit cancel button
                    console.log('User cancelled login or did not fully authorize.');

                }
            }, {
                scope: 'email'
            });

        }


    }



    /*
    var urlJsLang = "//connect.facebook.net/en_US/sdk.js";
    switch ($('html').attr('lang')) {
        case "de":
            urlJsLang = "//connect.facebook.net/de_DE/sdk.js";
            break;

        case "fr":
            urlJsLang = "//connect.facebook.net/fr_FR/sdk.js";
            break;

        default:
            urlJsLang = "//connect.facebook.net/en_US/sdk.js";
            break;
    }*/

    // Load the SDK asynchronously
    </script>
    <!-- *************** END FACEBOOK *************** -->
    <!-- PAYPAL *************** -->
    <script src='https://www.paypalobjects.com/js/external/api.js'></script>
    <script>
    var thisStructureId = getStructure().trim();

    $(document).ready(function() {
        // Vérifier que les settings PayPal existent
        if (!settings || !settings.login || !settings.login.payPal) {
            console.error('Settings PayPal non trouvés !');
            return;
        }

        // Lire les valeurs depuis les settings
        var clientId = settings.login.payPal.appid;
        var returnUrl = settings.login.payPal.returnurl;

        // Vérifier que les valeurs ne sont pas vides
        if (!clientId || clientId === "") {
            console.error('PayPal appid vide dans les settings !');
            return;
        }

        if (!returnUrl || returnUrl === "") {
            console.error('PayPal returnurl vide dans les settings !');
            return;
        }

        // Remplacer [structureid] dans l'URL de retour
        returnUrl = returnUrl.replace('[structureid]', thisStructureId);

        console.log('Initialisation du bouton PayPal officiel...');
        console.log('Client ID:', clientId.substring(0, 10) + '...');
        console.log('Return URL:', returnUrl);

        // Utiliser l'API officielle PayPal
        paypal.use(['login'], function (login) {
            login.render({
                "appid": clientId,
                "scopes": "openid email profile address",
                "containerid": "manual-paypal-btn",
                "responseType": "code",
                "locale": $('html').attr('lang') || "fr-fr",
                "buttonType": "LWP",
                "buttonShape": "rectangle",
                "buttonSize": "lg",
                "fullPage": "true",
                "returnurl": returnUrl,
                "state": "idstructure=" + thisStructureId
            });

            // Forcer les styles après le rendu du bouton
            setTimeout(function() {
                forcePayPalButtonStyles();
            }, 500);

            // Observer les changements dans le DOM pour maintenir les styles
            var observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList') {
                        forcePayPalButtonStyles();
                    }
                });
            });

            var targetNode = document.getElementById('manual-paypal-btn');
            if (targetNode) {
                observer.observe(targetNode, { childList: true, subtree: true });
            }
        });

        // Fonction pour forcer les styles du bouton PayPal
        function forcePayPalButtonStyles() {
            var container = $('#manual-paypal-btn');
            if (container.length) {
                // Forcer les styles sur le conteneur
                container.css({
                    'width': '100%',
                    'display': 'block',
                    'box-sizing': 'border-box',
                    'margin': '0'
                });

                // Forcer les styles sur tous les éléments enfants
                container.find('*').css({
                    'width': '100%',
                    'box-sizing': 'border-box'
                });

                // Styles spécifiques pour les iframes
                container.find('iframe').css({
                    'width': '100%',
                    'height': 'auto',
                    'min-height': '40px',
                    'border': 'none'
                });

                // Styles pour les boutons
                container.find('button, .paypal-button, [role="button"]').css({
                    'width': '100%',
                    'display': 'block',
                    'padding': '10px 15px',
                    'font-size': '14px',
                    'line-height': '1.42857143',
                    'border-radius': '4px',
                    'border': '1px solid #0070ba',
                    'background-color': '#0070ba',
                    'color': 'white',
                    'text-align': 'center',
                    'text-decoration': 'none',
                    'cursor': 'pointer',
                    'box-sizing': 'border-box'
                });

                console.log('Styles PayPal forcés');
            }
        }

        console.log('Bouton PayPal officiel initialisé !');
    });
    </script>
    <!-- END PAYPAL *************** -->
</asp:Content>