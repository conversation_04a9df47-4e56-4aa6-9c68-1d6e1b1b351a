﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="AntiXSS" version="4.3.0" targetFramework="net45" />
  <package id="Antlr" version="*******" targetFramework="net471" />
  <package id="Antlr3.Runtime" version="3.5.1" targetFramework="net462" />
  <package id="DotNetZip" version="1.16.0" targetFramework="net462" />
  <package id="Facebook" version="7.0.6" targetFramework="net462" />
  <package id="log4net" version="3.1.0" targetFramework="net48" />
  <package id="Microsoft.AspNet.Cors" version="5.2.9" targetFramework="net471" />
  <package id="Microsoft.AspNet.Razor" version="3.2.9" targetFramework="net471" />
  <package id="Microsoft.AspNet.Web.Optimization" version="1.1.3" targetFramework="net45" />
  <package id="Microsoft.AspNet.WebApi.Client" version="5.2.9" targetFramework="net471" />
  <package id="Microsoft.AspNet.WebApi.Core" version="5.2.9" targetFramework="net471" />
  <package id="Microsoft.AspNet.WebApi.Cors" version="5.2.9" targetFramework="net471" />
  <package id="Microsoft.AspNet.WebApi.WebHost" version="5.2.9" targetFramework="net471" />
  <package id="Microsoft.AspNet.WebPages" version="3.2.9" targetFramework="net471" />
  <package id="Microsoft.Web.Infrastructure" version="2.0.0" targetFramework="net471" />
  <package id="Modernizr" version="2.8.3" targetFramework="net471" />
  <package id="Newtonsoft.Json" version="13.0.3" targetFramework="net471" />
  <package id="ObjectDumper.NET" version="4.0.6" targetFramework="net471" />
  <package id="RestSharp" version="106.15.0" targetFramework="net462" />
  <package id="WebGrease" version="1.6.0" targetFramework="net471" />
</packages>