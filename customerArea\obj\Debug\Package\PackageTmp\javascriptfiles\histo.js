var baseUrlDV = "";
var baseUrlEdition = "";
var status_working_current = "";
var allselectLinkedConsumers = []
var allConsumers = [];

function showDetails(structureid, orderId, typel, eventid) {
    var pgmeth = PageMethods.GetHtmlDetailCmd(structureid, orderId, typel, eventid,
        GetHistoMethodSuccess, MethodeJS_Error);
}

function GetHistoMethodSuccess(result, userContext, methodName) {
    if (result.indexOf(":") >= 0) {
        var orderId = result.substring(0, result.indexOf(":"))
        var td = $('#tddetail' + orderId);
        if (td != null) {
            var myhtml = result.substring(result.indexOf(":") + 1)
            td.html(myhtml);
        }
    }
}

function MethodeJS_Error(error, userContext, methodName) {
    alert("!" + error.get_message());
    alert('MethodeJS_Error');
}

function googlepayError(e) {
    console.log(e)
}

function googlepaySuccess(e) {
    console.log(e)
}


$(document).ready(function() {
    const container = document.querySelector('#loyaltysave');

    //resize l'iframe parent
    resizeParentIframe();

    $('#tblOrderList2').hide();

    GetListLinkConsumer();

    loadCommandesList();

    $('#filter').attr('placeholder', ReadXmlTranslate('rechercher'));

    $('#btnValiderDepot').on('click', function() {
        doDV(status_working_current);
    });

    $('#btnAnnulerDepot').on('click', function() {
        $(status_working_current).removeClass('status_working').addClass('status_B');
        //cache la dialog 
        $('#modalDepot').modal('hide');
    });
});

function loadCommandesList(orderId) {
    $.ajax({
        type: "POST",
        url: 'Histo.aspx/GetListCommandes',
        contentType: 'application/json; charset=utf-8',
        dataType: "json",
        success: function(response) {
            datajson = JSON.parse(response.d);
            fillDataTableHisto(datajson);
            fillDataTableProducts(datajson);
        },
        error: function(XMLHttpRequest, textStatus, errorThrown) {
            $('#tblOrderList2').show();
            //showAlertError('', XMLHttpRequest.statusText, '')
            showAlertError('', ReadXmlTranslate('msg_error_generic'), '')
        },
        complete: function() {
            resizeParentIframe();

            if (orderId != undefined)
                $('#tblOrderList2 tbody tr[id="cmd_' + orderId + '"]').find('td.details-control').click()
        }
    });
}

function GetListLinkConsumer() {
    var structureid = $('#myhead').attr('structureid');
    var lang = getLang();
    var sData = JSON.stringify({
        structureid: structureid,
        langCode: lang
    });
    $.ajax({
        type: "POST",
        url: 'Commons.asmx/GetListLinkConsumer',
        contentType: 'application/json; charset=utf-8',
        dataType: "json",
        data: sData,
        success: function(data) {
            allConsumers = data.d
        },
        error: function(XMLHttpRequest, textStatus, errorThrown) {
            showAlertError('', ReadXmlTranslate('msg_error_generic'), '');
        }
    });
}

function htmlStatusPlace(seat) {
    var txtReservee = ReadXmlTranslate('entreeisreserved');
    var txtAnnulee = ReadXmlTranslate('entreeiscanceled');
    var txtPayee = ReadXmlTranslate('entreeispaid');
    var txtEditee = ReadXmlTranslate('entreeisedited');
    var txtFacture = ReadXmlTranslate('entreeisfactured');

    if (seat.Etat == "B") {
        return "<span class='label label-success sp-tooltip-bbb' data-toggle='tooltip' > <i class='fa fa-ticket'/>" + txtEditee + "</span> ";
    }
    if (seat.Etat == "P") {
        return "<span class='label label-success sp-tooltip-ppp' data-toggle='tooltip' > <i class='fa fa-eur'/> " + txtPayee + "</span> ";
    }

    if (seat.Etat == "R") {
        return "<span class='label label-info sp-tooltip-r' data-toggle='tooltip'> <i class='fa fa-check-square-o'/>" + txtReservee + "</span> ";
    }
    if (seat.Etat == "L") {
        return "<span class='label label-danger sp-tooltip-l' data-toggle='tooltip'> <i class='fa fa-trash-o'/>" + txtAnnulee + "</span> ";
    }

    if (seat.Etat == "F") {
        return "<span class='label label-danger sp-tooltip-l' data-toggle='tooltip'> <i class='fa fa-trash-o'/>" + txtFacture + "</span> ";
    }
}

function RewriteSessions(data) {

    var arr = new Array();
    $.each(data, function(index, commande) {
        // chaque commande
        var thiscmdId = commande.Commande_id;
        var thisstatus = commande.Status;
        var thisDateAchat = commande.DateAchat;
        $.each(commande.ListSeatLines, function(index, seat) {
            thissessionId = seat.Seance_id;
            seat.Commande_id = thiscmdId;
            var tblThisSessionComm = jQuery.grep(arr, function(elem, index) {
                return elem.Seance_id == thissessionId & elem.Commande_id == thiscmdId;
            });
            if (tblThisSessionComm.length > 0) {
                ThisSessionComm = tblThisSessionComm[0];
                var arrSeats = ThisSessionComm.seats;
                arrSeats.push(seat);
                ThisSessionComm.seats = arrSeats;
            } else {
                var ThisSessionComm = new Object;
                ThisSessionComm.Seance_id = thissessionId;
                ThisSessionComm.Commande_id = thiscmdId;
                ThisSessionComm.Date_achat = thisDateAchat;
                ThisSessionComm.Session_date_format = seat.StrSessionDateDeb;
                ThisSessionComm.Session_date_deb = seat.SessionDateDeb;
                ThisSessionComm.Session_date_fin = seat.SessionDateFin;
                ThisSessionComm.Event_name = seat.Event_name
                ThisSessionComm.Status = thisstatus;
                ThisSessionComm.Customer = seat.identite_id;
                var arrSeats = new Array();
                arrSeats.push(seat);
                ThisSessionComm.seats = arrSeats;
                arr.push(ThisSessionComm);
            }
        });
    });
    return arr;
}

var oTableOrdersList;

function fillDataTableHisto(data) {
    $('#tblOrderList2').show();
    var hideDateAchatRow = true;
    data = RewriteSessions(data)
    oTableOrdersList = $('#tblOrderList2').DataTable({
        "bDestroy": true,
        "bProcessing": false,
        "responsive": {
            details: {
                type: 'none',
                target: '',
                display: $.fn.dataTable.Responsive.display.childRowImmediate,
            }
        },
        "bAutoWidth": true,
        "iDisplayLength": 10,
        "aLengthMenu": [
            [5, 10, 25, 50, 100, -1],
            [5, 10, 25, 50, 100, ReadXmlTranslate('lbl_choose_all_datatable')]
        ],
        "bDeferRender": true,
        "aaData": data,
        "oLanguage": GetLanguageDataTable(),
        "aaSorting": [
            [2, "desc"]
        ],
        "createdRow": function(row, data, dataIndex) {
            $(row).addClass('important');
            $(row).attr('id', 'cmd_' + data.Commande_id)
        },
        "aoColumns": [
            {
                "class": "details-control align-middle",
                "orderable": false,
                "data": null,
                "defaultContent": "<div class='btn btn-secondary'><i class='fas fa-ticket-alt'></i> " + ReadXmlTranslate('btn_tickets_details') + "</div>"
            },
            {
                //Date achat
                "mRender": function(data, type, full) {
                    if (full.Date_achat == undefined) {
                        hideDateAchatRow = false;
                        return "";
                    } else if (full.Date_achat.contains('0001-01-01')) {
                        hideDateAchatRow = false;
                    }
                    return full.Date_achat;
                },
                "sClass": "align-middle date_achat"
            }, {
                "mDataProp": "Event_name",
                "sClass": "align-middle"
            }, {
                "mDataProp": "Commande_id",
                "sClass": "align-middle"
            }, {
                //nb de places
                "mRender": function(data, type, full) {
                    return full.seats.length;
                },
                "sClass": "align-middle"
            }, {
                //amount
                "mRender": function(data, type, full) {
                    var thisAmount = 0;
                    $.each(full.seats, function(index, seat) {
                        thisAmount += seat.Amount;
                    });
                    return '<span class="montant"> ' + thisAmount + '</span>'
                },
                "sClass": "align-middle"
            }, {
                "mDataProp": "Session_date_format",
                "sClass": "align-middle"
            }, {
                "mDataProp": "Status",
                "mRender": function(data, type, full) {
                    return displayStatusCmd(full);
                },
                "sClass": "align-middle status"
            },
        ]
    });


    if (!hideDateAchatRow) {
        $('#tblOrderList2 th span[data-row="date"]').closest('th').hide();
        $('#tblOrderList2 tbody tr td.date_achat').hide();
    }


    function formatDetail(d) {
        var d = d;
        var dvp = ($('#dvpflag').attr("poss") == "true")
        var html = ''
        var dCount = 1
        var isconsumerlinkable = false; // savoir si on a déjà lier un consommateur au moins 1 fois
        html += "<div class='allTickets'  data-commandeid='" + d.Commande_id + "' >"
        $.each(d.seats, function(index, seat) {

            var isMandatory = false;
            if (settings.histo.listPriceIdNotMandatory.length > 0) {
                //si le tableau est vide alors le tarif est obligatoire pour le consommateur avec un consommateur différent
                var arrPriceIdMandatory = $.grep(settings.histo.listPriceIdNotMandatory, function(x) {
                    return x == seat.PriceId;
                });

                if (arrPriceIdMandatory.length == 0)
                    isMandatory = true;
            }


            html += '<div class="oneTicket row mb-1 align-items-center" data-priceid="' + seat.PriceId + '" >'
            if (seat.Rank && seat.Rank.trim() != '' && seat.Rank != null) {
                html += '<div class="col-lg-4">' + ReadXmlTranslate('lbl_rank') + ' : ' + seat.Rank + ', ' + ReadXmlTranslate('lbl_seat') + ' : ' + seat.Seat
            } else {
                html += '<div class="col-lg-4">' + ReadXmlTranslate('lbl_free_placement')
            }
            html += ', ' + seat.Category_name + '</div>'
            html += '<div class="col-lg-auto"><span class="montant">' + seat.Amount + '</span></div>'
            html += '<div class="col-lg">' + seat.Price_name + '</div>'
            //si commande réservé ou payé
            if ((seat.Etat == "R" || seat.Etat == "P") && seat.depotVenteId == 0) {

                var identiyassignedtoseat = "";
                //si des consommateurs existent
                if (seat.IdentiteConsumerId > 0 || seat.identite_id != $('#receptVar').data('iid') || (seat.AboId > 0 && seat.identite_id == $('#receptVar').data('iid')) || !settings.histo.canChangeConsumers) {
                    var identiyassignedtoseat = $.grep(allConsumers, function(x) {
                        return x.Identite_id == seat.IdentiteConsumerId
                    })[0];

                    if (identiyassignedtoseat == undefined) {
                        identiyassignedtoseat = $.grep(allConsumers, function(x) {
                            return x.Identite_id == seat.identite_id
                        })[0];
                    }

                    if (identiyassignedtoseat != undefined) {
                        html += '<div class="col-lg-auto">' +
                            '<div data-identiteid="' + identiyassignedtoseat.Identite_id + '" >' + ReadXmlTranslate('lbl_attached_to') + " " + identiyassignedtoseat.FirstName + ' ' + identiyassignedtoseat.SurName + '</div>' +
                            '</div>';
                    }

                } else {
                    // if (allConsumers.length > 0 && (identiyassignedtoseat == undefined || identiyassignedtoseat == "")) {
                    html += '<div class="col-lg-auto text-right">' +
                        '<select class="selectLinkedConsumers form-control" data-ismandatory="' + isMandatory + '"  data-entreeid="' + seat.Entree_id + '"  data-eventid="' + seat.Event_id + '" data-seanceid="' + seat.Seance_id + '" aria-label="' + ReadXmlTranslate('lbl_link_ticket_to_consummer') + '">' +
                        '<option value="0" >' + ReadXmlTranslate('lbl_link_ticket_to_consummer') + '</option>';
                    html += '<option value="newconsumer">' + ReadXmlTranslate('lbl_attach_new_consumer') + '</option>'
                    // LOAD ALL CONSUMERS HERE EX : '<option value="idconsomateur">firstname lastname</option>'+
                    $.each(allConsumers, function(index, item) {
                        html += '<option value="' + item.Identite_id + '">' + item.FirstName + ' ' + item.SurName + '</option>'
                    })

                    html += '</select>' +
                        '</div>'
                    isconsumerlinkable = true;
                }

            }

            if (seat.Etat == "B") {
                /* edité, afficher le consommateur eventuel */
                var identiyassignedtoseat = $.grep(allConsumers, function(x) {
                    return x.Identite_id == seat.IdentiteConsumerId
                })[0];

                if (identiyassignedtoseat == undefined) {
                    identiyassignedtoseat = $.grep(allConsumers, function(x) {
                        return x.Identite_id == seat.identite_id
                    })[0];
                }

                if (identiyassignedtoseat != undefined) {
                    html += '<div class="col-lg-auto">' +
                        '<div data-identiteid="' + identiyassignedtoseat.Identite_id + '" >' + ReadXmlTranslate('lbl_attached_to') + " " + identiyassignedtoseat.FirstName + ' ' + identiyassignedtoseat.SurName + '</div>' +
                        '</div>';
                }
            }

            html += '<div class="col-lg-12 text-right">'

            var sessionDate = (new Date(seat.Session_date).getUTCMonth() + 1) + "/" + new Date(seat.Session_date).getUTCDate() + "/" + new Date(seat.Session_date).getUTCFullYear();
            var todayDate = (new Date().getUTCMonth() + 1) + "/" + new Date().getUTCDate() + "/" + new Date().getUTCFullYear();

            //if ((seat.Etat == "B" || seat.Etat == "P") && ((seat.depotVenteId == 0 || seat.depotVenteStatus == "Refus"))) { // modif pg pour pouvoir faire du jyvaisplus sur des dates passées
            // if ((seat.Etat == "B" || seat.Etat == "P") && ((seat.depotVenteId == 0 || seat.depotVenteStatus == "Refus")) && new Date(sessionDate) >= new Date(todayDate)) {

            if ((seat.Etat == "B" || seat.Etat == "P") && (seat.depotVenteId == 0 || seat.depotVenteStatus == "Refus")) {


                if (dvp && settings.histo.showJyVaisPlus && new Date(sessionDate) >= new Date(todayDate)) {
                    html += "<a href='' class='doDV btn btn-secondary' data-hash ='" + seat.Hash + "' data-cmdid='" + seat.Commande_id + "' data-eventid='" + seat.Event_id + "' data-seanceid='" + seat.Seance_id + "' data-entreeid='" + seat.Entree_id + "' data-maquetteid='" + seat.Maquetteid + "' data-amnt='" + seat.Amount + "' data-seat='" + seat.Seat + "' data-rang='" + seat.Rank + "' data-categname='" + seat.Category_name + "' data-price='" + seat.Price_name + "' data-date='" + d.Session_date_format + "' data-eventName='" + d.Event_name + "'> <i class='fa fa-recycle'></i> " + ReadXmlTranslate('msg_jyvaiplu') + "</a> ";
                }

                if (!seat.IsControlAccessPassed) {

                    var showBtnEditerMaPlaceOnPrice = true;
                    if (settings.histo.listPriceIdToHideBtnEditPlace.length > 0) {
                        $.grep(settings.histo.listPriceIdToHideBtnEditPlace, function(x) {
                            if (x == seat.PriceId) {
                                showBtnEditerMaPlaceOnPrice = false;
                            }
                            return x;
                        })

                    }

                    if (seat.Etat == 'B') {
                        // BOUTON MTICKET et PDF
                        if ((settings.histo.showBtnDownloadPdf || settings.mticket.apple.isActive || settings.mticket.google.isActive || settings.mticket.samsung.isActive) && showBtnEditerMaPlaceOnPrice ) {
                            var identityId = $('head').attr('myidentity')

                            html += '<a href="" class="doPassbook btn btn-primary mr-2" data-hash ="' + seat.Hash + '" data-identiteid="' + identityId + '" data-orderid="' + seat.Commande_id + '" data-eventid="' + seat.Event_id + '" data-sessionid="' + seat.Seance_id + '"  data-seatid="' + seat.Entree_id + '" data-langcode="' + getLang() + '" >' + ReadXmlTranslate('btn_download_my_ticket') + '</a>';
                        }
                        //BOUTON TICKET PDF
                        /*if (settings.histo.showBtnDownloadPdf) {
                            html += '<a href="" class="doPdf btn btn-primary" data-hash ="' + seat.Hash + '" data-cmdid="' + seat.Commande_id + '" data-eventid="' + seat.Event_id + '" data-seanceid="' + seat.Seance_id + '" data-entreeid="' + seat.Entree_id + '" data-maquetteid="' + seat.Maquetteid + '"  data-dossierid="' + seat.dossier_id + '"">' + ReadXmlTranslate('btn_download_my_ticket') + '</a>'
                        }*/
                    }



                    if (seat.Etat == 'P' && seat.Maquetteid != "" && seat.Maquetteid > 0 && new Date(sessionDate) >= new Date(todayDate) && settings.histo.showBtnEditPlace && showBtnEditerMaPlaceOnPrice) {

                        // payé et maquette ok mais les consommateurs ne sont pas reliés
                        var disablClass = "";
                        if (isconsumerlinkable)
                            disablClass = "disabled"

                        html += '<a href="" class="doEdition btn btn-primary ' + disablClass + '" data-hash ="' + seat.Hash + '" data-dossierid="' + seat.dossier_id + '"  data-cmdid="' + seat.Commande_id + '" data-eventid="' + seat.Event_id + '" data-seanceid="' + seat.Seance_id + '"  data-entreeid="' + seat.Entree_id + '" data-maquetteid="' + seat.Maquetteid + '">' + ReadXmlTranslate('btn_edit_my_ticket') + '</a>'
                    }
                }
            }
            if (dvp) {
                if (seat.depotVenteId != 0) {
                    if (seat.depotVenteStatus == "Repris") {
                        html += "<span class='text-success'> <i class='fas fa-ticket-alt'/> " + ReadXmlTranslate('msg_place_recovery') + "</span> ";
                    }
                    if (seat.depotVenteStatus == "Depot") {
                        html += "<span class='text-warning'> <i class='fas fa-ticket-alt'/> " + ReadXmlTranslate('msg_request_resume_in_progress') + "</span> ";
                    }
                    if (seat.depotVenteStatus == "Refus") {
                        html += "<span class='text-danger'> <i class='fas fa-ticket-alt'/> " + ReadXmlTranslate('msg_request_resume_rejected') + "</span> ";
                    }
                }
            }

            html += '</div>'
            html += '</div>'
            if (dCount < d.seats.length) {
                html += '<hr/>'
            }
            dCount++
        });
        var dStatus = $.grep(d.Status, function(r) {
            return r == 'R' || r == 'P';
        }).length;
        if (dStatus > 0 && allConsumers.length > 0 && isconsumerlinkable) {
            html += "<hr/>"
            html += "<div class='row mb-1 align-items-center'>"
            html += "<div class='col text-right'>"
            html += "<a href='' class='validSelectLinkedConsumers btn btn-primary btn-primary-light disabled'>" + ReadXmlTranslate('btn_link_all_consumers_to_tickets') + "</a>"
            html += "</div>"
            html += "</div>"
            html += '</div>'
        }

        return html;
    }

    var detailRows = [];
    // Add event listener for opening and closing details
    $('#tblOrderList2 tbody').on('click', 'tr td.details-control', function() {
        var tr = $(this).closest('tr');
        var row = oTableOrdersList.row(tr);
        var idx = $.inArray(tr.attr('id'), detailRows);

        if ($('.details_' + tr.attr('id')).length > 0) {
            $('.details_' + tr.attr('id')).remove()
        } else {
            var trPosition;
            if (tr.nextUntil('[id^="cmd_"]').length > 0) {
                trPosition = tr.nextUntil('[id^="cmd_"]')
            } else {
                trPosition = tr
            }

            $("<tr class='details_" + tr.attr('id') + "'><td colspan='8'></td></tr>").insertAfter(trPosition);
            $(".details_" + tr.attr('id') + " td").html(formatDetail(row.data()))
            //selectionne l'identité principal sur les tarif non obligatoire (possibilité de choisir plusieurs fois la même identité sur ce tarif)

            //CHANGE SELECT CONSUMER
            var previousConsumerSelected;

            $(".selectLinkedConsumers").on('focus', function() {
                // Store the current value on focus and on change
                previousConsumerSelected = $(this).val();
            }).on('change', function(e) {

                var valueConsumerSelected = $(this).val();
                var thisSelect = $(this);

                //nouveau consommateur
                if (valueConsumerSelected == "newconsumer") {

                    //ShowGenericModal('test', '<object type="text/html" data="linkedconsumerslist.aspx?idstructure=' + structureid + '&EventId=0&iswidget=1" width="100%"  />', '', '', '', '');
                    window.location.replace("linkedconsumerslist.aspx?idstructure=" + structureid);

                }

                //obligatoire et que la valeur selectionnée est différente de 0
                if ($(this).data('ismandatory')) {
                    //parcours tous les selected de la seance 
                    $.each($('.selectLinkedConsumers[data-seanceid="' + $(this).data('seanceid') + '"][data-ismandatory="true"]'), function(i, k) {

                        if ($(k).data('entreeid') != $(thisSelect).data('entreeid') && valueConsumerSelected != 0 && valueConsumerSelected != "newconsumer")
                            $(k).find('option[value="' + valueConsumerSelected + '"]').attr('disabled', true);


                        if (previousConsumerSelected != valueConsumerSelected)
                            $(k).find('option[value="' + previousConsumerSelected + '"]').removeAttr('disabled');

                    });
                }

                // on force à ce que les consommateurs soient différents pour chaque billet
                if (settings.histo.forcedDifferentConsumerToSeatForAllSelect) {

                    $(this).closest('.allTickets').find('option').attr('disabled', false);
                    $.each($(this).closest('.allTickets').find('option[value!=0]:selected'), function(index, obj) {
                        $(this).closest('.allTickets').find('option[value=' + $(obj).val() + ']').attr('disabled', true);
                    });

                }

                //si on ne force pas à sélectionné un consommateur pour chaque place et que tout est sélectionné
                var nbconsummerselected = $(this).closest('.allTickets').find('select option:selected[value="0"]').length;
                var nbnewconsummerselected = $(this).closest('.allTickets').find('select option:selected[value="newconsumer"]').length;
                var nbselect = $(this).closest('.allTickets').find('select').length;

                console.log("settings.histo.forcedDifferentConsumerToSeatForAllSelect=" + settings.histo.forcedDifferentConsumerToSeatForAllSelect)


                if ((settings.histo.forcedConsumerToSeatForAllSelect && nbconsummerselected == 0 && nbnewconsummerselected == 0) ||
                    (!settings.histo.forcedConsumerToSeatForAllSelect && nbselect > (nbconsummerselected + nbnewconsummerselected))) {
                    //on active le bouton valider
                    $(this).closest('.allTickets').find('.validSelectLinkedConsumers').removeClass('disabled');

                } else {

                    $(this).closest('.allTickets').find('.validSelectLinkedConsumers').addClass('disabled');
                }
            });

            //VALID SELECTED LINKED CONSUMERS
            $('.validSelectLinkedConsumers').on('click', function(e) {
                //var oneTicket = $(this).closest('.oneTicket')
                var allTickets = $(this).closest('.allTickets')
                e.preventDefault();
                //on recupère chaque select de chaque ticket et on les stock
                var commandeId = $(allTickets).data('commandeid')

                allselectLinkedConsumers = []
                $.each($(allTickets).find('.selectLinkedConsumers'), function(index, item) {
                    if ($(item).find("option:selected").val() != "0") {
                        var LinkConsumerToSeat = {};
                        LinkConsumerToSeat.EntreeId = $(item).data('entreeid');
                        LinkConsumerToSeat.IdentiteId = $(item).find("option:selected").val();
                        LinkConsumerToSeat.EventId = $(item).data('eventid');
                        LinkConsumerToSeat.SeanceId = $(item).data('seanceid');
                        LinkConsumerToSeat.CommandeId = commandeId;
                        allselectLinkedConsumers.push(LinkConsumerToSeat);
                    }
                });
                console.log(allselectLinkedConsumers)
                if (Object.keys(allselectLinkedConsumers).length > 0) {
                    ShowGenericModal(ReadXmlTranslate('title_link_tickets_to_consummers'), ReadXmlTranslate('msg_link_tickets_to_consummers'), ReadXmlTranslate('btn_validate'), ReadXmlTranslate('btn_cancel'), 'confirmSelectLinkedConsumers', 'cancelSelectLinkedConsumers')

                    $('#confirmSelectLinkedConsumers').on('click', function(e) {
                        //lie un consommateur à une entrée
                        linkconsumertoseat(allselectLinkedConsumers, allTickets)
                    });
                }
            })

            tr.addClass('shown');
            formatPrice();

            doPassbookOnClick();
            doPdfOnClick();
            doEditionOnClick();
            doDVonClick();
        }
    });

    /* au changement de page du datatable*/
    oTableOrdersList.on('draw', function() {
        formatPrice();

    });
    oTableOrdersList.on('responsive-display', function(e, datatable, row, showHide, update) {
        formatPrice();
    });
    oTableOrdersList.draw()

    function linkconsumertoseat(arrseattoidentite, allTickets) {
        //tableau d'entrée sur une identite

        console.log('confirmSelectLinkedConsumers ' + Object.keys(arrseattoidentite).length)


        $.ajax({
            type: "POST",
            url: 'Histo.aspx/linkEntreeConsommateur',
            contentType: 'application/json; charset=utf-8',
            async: false,
            data: JSON.stringify({
                listlinkconsumertoseat: arrseattoidentite
            }),
            success: function(data, textStatus, jqXHR) {
                console.log("success");

                $.each($(allTickets).find('.oneTicket'), function(index, item) {
                    $(item).find('a.doEdition').removeClass('disabled').removeAttr('disabled'); // permet l'edition

                    var identityIDSelected = $(item).find('.selectLinkedConsumers option:selected').val()
                    var identityNameSelected = $(item).find('.selectLinkedConsumers option:selected').text()
                    if (identityIDSelected != 0) {
                        $(item).find('.selectLinkedConsumers').parent().html('<div data-identiteid="' + identityIDSelected + '">' + ReadXmlTranslate('lbl_attached_to') + " " + identityNameSelected + '</div>')
                    }
                })

                $('.validSelectLinkedConsumers').closest('.row').hide()
                $('.validSelectLinkedConsumers').closest('.allTickets').find('hr:last').hide()

                $('#tblOrderList2 tbody').unbind("click");
                var orderId = $('.validSelectLinkedConsumers').closest('.allTickets').data('commandeid')
                loadCommandesList(orderId);
            },
            error: function(XMLHttpRequest, textStatus, errorThrown) {
                showAlertError('', ReadXmlTranslate('msg_error_generic'), '')
            },
            complete: function() {
            }
        });

    }

    function displayStatusCmd(event) {
        var html = '';
        if ($.grep(event.seats, function(x) {
                return x.Etat == 'B'
            }).length > 0) {

            html = '<span class="font-weight-bold text-primary">' + ReadXmlTranslate('lbl_statusB') + '</span>';
        } else if ($.grep(event.seats, function(x) {
                return x.Etat == 'P'
            }).length > 0) {
            html = '<span class="font-weight-bold text-success">' + ReadXmlTranslate('lbl_statusP') + '</span>';

        } else if ($.grep(event.seats, function(x) {
                return x.Etat == 'R'
            }).length > 0) {
            html = '<span class="font-weight-bold text-warning">' + ReadXmlTranslate('lbl_statusR') + '</span>';

        } else {
            html = '<span class="font-weight-bold text-muted">' + ReadXmlTranslate('lbl_statusL') + '</span>';
        }
        return html;
    }

    formatPrice();


    function doPdfOnClick() {

        $(".doPdf").unbind("click"); // unbind tous les .pdf pour le remettre ensuite (sinon bind +sieures fois le même .dopdf)

        $('.doPdf').on('click', function(e) {
            if ($(this).attr('href') != '' && !$(this).hasClass('disabled')) { //si le pdf est déja génèré, on le dl direct
                var thisDL = $(".doPdf[data-cmdid='" + $(this).data('cmdid') + "']")
                thisDL.html(ReadXmlTranslate('msg_downloaded')).addClass('disabled')
                setTimeout(function() {
                    thisDL.html(ReadXmlTranslate('btn_download_my_ticket')).removeClass('disabled')
                }, 4000);
            } else { //sinon on va le génèrer.
                e.preventDefault();
                e.stopPropagation();
                redoPdf(this);
            }
            //console.log("a.doPdf bindé dans doPdfOnClick()");
        });
    }

    doPdfOnClick();

    function doDVonClick() {
        $(".doDV").unbind("click");
        $('.doDV').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            proposeDV(this);
        });
    }
    doDVonClick();


    function doEditionOnClick() {
        $(".doEdition").unbind("click");
        $('.doEdition').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            edition(this);
        });
    }
    doEditionOnClick();

    $(".sp-tooltip-b").tooltip({
        title: ReadXmlTranslate('tooltip_editee')
    });

    $(".sp-tooltip-p").tooltip({
        title: ReadXmlTranslate('tooltip_payee')
    });

    $(".sp-tooltip-r").tooltip({
        title: ReadXmlTranslate('tooltip_reservee')
    });

    $(".sp-tooltip-l").tooltip({
        title: ReadXmlTranslate('tooltip_annulee')
    });
}

//BIND PASSBOOK BUTTONS
function doPassbookOnClick() {
    $('.doPassbook').off('click').on('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        initPassbook($('#receptVar').data('structureid'), parseInt($(this).attr('data-identiteid')), parseInt($(this).attr('data-eventid')), parseInt($(this).attr('data-sessionid')), parseInt($(this).attr('data-orderid')), parseInt($(this).attr('data-seatid')), $(this).attr('data-langcode'))
    });

    /*$(".doPassbookUrl").unbind("click");
    $('.doPassbookUrl').on('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        initPassbookUrl($('#receptVar').data('structureid'), parseInt($(this).attr('data-identiteid')), parseInt($(this).attr('data-eventid')), parseInt($(this).attr('data-sessionid')), parseInt($(this).attr('data-orderid')), parseInt($(this).attr('data-seatid')), $(this).attr('data-langcode'))
    });*/

    //$(".doPassbookFile").unbind("click");
}

//ANCIEN MTICKET
// function initPassbookFile(structureid, identiteid, eventid, sessionid, orderid, seatid, langcode) {
//     // call widget local mvc
//     var widgetData = getWidgetSignature();

//     if (widgetData != undefined) {
//         var thisPartnerName = widgetData[0];
//         var thisSignature = widgetData[1];

//         SphereCustomer.Passbook({
//             selector: '#passbook',
//             structureId: structureid,
//             identiteId: identiteid,
//             eventId: eventid,
//             sessionId: sessionid,
//             orderId: orderid,
//             seatId: seatid,
//             langCode: langcode,
//             partnerName: thisPartnerName,
//             signature: thisSignature
//         })
//     }
// }

function initPassbook(structureid, identiteid, eventid, sessionid, orderid, seatid, langcode) {
    // call widget local mvc
    var widgetData = getWidgetSignature();

    if (widgetData != undefined) {
        $("#passbookButtonsArea").html("")
        var thisPartnerName = widgetData[0];
        var thisSignature = widgetData[1];

        SphereCustomer.TicketEditionButtons({
            selector: '#passbookButtonsArea',
            structureId: structureid,
            identiteId: identiteid,
            eventId: eventid,
            sessionId: sessionid,
            orderId: orderid,
            seatId: seatid,
            langCode: langcode,
            partnerName: thisPartnerName,
            signature: thisSignature
        });

        $("#passbookModal").modal("show");
    }
}

//GET SIGNATURE & PARTERNAME
function getWidgetSignature() {
    var resultData;
    $.ajax({
        type: "POST",
        url: 'commons.asmx/GetWidgetSignature',
        contentType: 'application/json; charset=utf-8',
        async: false,
        data: JSON.stringify({
            structureId: structureid
        }),
        success: function(data, textStatus, jqXHR) {
            resultData = data.d;

        },
        error: function(XMLHttpRequest, textStatus, errorThrown) {

        }
    });
    return resultData;
}

function entree(_entreeid, _cmdid, _dossid, _seanceid, _eventid, _maqid, _hash, _namec, _surnamec, _emailc) {
    this.entreeid = _entreeid;
    this.cmdid = _cmdid;
    this.seanceid = _seanceid;
    this.eventid = _eventid;
    this.hash = _hash;
    this.namec = _namec;
    this.surnamec = _surnamec;
    this.emailc = _emailc;
    this.dossid = _dossid;
    this.maqid = _maqid;
}

function formatPrice() {
    $('.montant').priceFormat({
        prefix: '',
        suffix: ' ' + deviseCode,
        thousandsSeparator: ' '
    });
}

function RewriteProducts(data) {
    var arr = new Array();
    $.each(data, function(index, commande) {
        $.each(commande.ListProductLines, function(index, product) {
            var thisProduct = new Object;
            thisProduct.ProductName = product.Product_name;
            thisProduct.ProductAmountInCent = product.ProductAmountCent;
            thisProduct.OrderId = product.OrderId;
            arr.push(thisProduct);
        });
    });
    return arr;
}

function fillDataTableProducts(data) {
    $('#tblProductsList').show();
    data = RewriteProducts(data)
    oTableProductsList = $('#tblProductsList').DataTable({
        "bDestroy": true,
        "bProcessing": false,
        "bAutoWidth": true,
        "iDisplayLength": 10,
        "aLengthMenu": [
            [5, 10, 25, 50, 100, -1],
            [5, 10, 25, 50, 100, ReadXmlTranslate('lbl_choose_all_datatable')]
        ],
        "bDeferRender": true,
        "aaData": data,
        "oLanguage": GetLanguageDataTable(),
        "aaSorting": [
            [2, "desc"]
        ],
        "aoColumns": [
            {
                "mDataProp": "OrderId",
                "sClass": "align-middle"
            }, {
                "mDataProp": "ProductName",
                "sClass": "align-middle"
            }, {
                //amount
                "mRender": function(data, type, full) {
                    return '<span class="montant"> ' + full.ProductAmountInCent + '</span>'
                },
                "sClass": "align-middle"
            },
        ]
    });
}

function redoPdf(myi) {
    //string commandeid, string entree_id, string event_id, string seance_id, string hash
    $(myi).removeClass('status_B').addClass('status_working');

    var cmdid = $(myi).data("cmdid");
    var dossierid = $(myi).data("dossierid");
    $.ajax({
        type: "POST",
        url: 'Histo.aspx/doPdf',
        contentType: 'application/json; charset=utf-8',
        data: JSON.stringify({
            commande_id: $(myi).data("cmdid"),
            entree_id: $(myi).data("entreeid"),
            event_id: $(myi).data("eventid"),
            seance_id: $(myi).data("seanceid"),
            dossier_id: $(myi).data("dossierid"),
            hash: $(myi).data("hash")

        }),
        dataType: "json",
        success: function(data, textStatus, jqXHR) {

            if (data.d.contains(":")) {
                if (data.d.split(':')[0] == "ok") {
                    console.log(data.d);
                    cmdid = $(myi).data('cmdid');
                    dossierid = $(myi).data('dossierid');

                    $('<li/>', {
                            class: 'list-group-item',
                            id: "LIcmdid" + cmdid
                        })
                        .appendTo($('#ul_download_orders'));

                    $("<span/>", {
                            text: ReadXmlTranslate('command_num') + " " + cmdid + ":",
                            id: "cmdid" + cmdid,
                            class: "label label-info"
                        })
                        .appendTo($('li#LIcmdid' + cmdid));

                    var cnt = 1;

                    $('#iframe_forDownloadPdf').attr('src', 'get_printathome.aspx?name=' + data.d);

                    $('a.doPdf[data-cmdid="' + cmdid + '"][data-dossierid="' + dossierid + '"]').html(ReadXmlTranslate('msg_downloaded')).addClass('disabled')

                    setTimeout(function() {
                        $('a.doPdf[data-cmdid="' + cmdid + '"][data-dossierid="' + dossierid + '"]').html(ReadXmlTranslate('btn_download_my_ticket')).removeClass('disabled')
                    }, 4000);



                    //mets à jour le status de la commande en edité
                    $('#tblOrderList2 tbody').find('tr[id="cmd_' + cmdid + '"] .status span').html(ReadXmlTranslate('lbl_statusB'))
                } else {
                    ShowGenericModal('', data.d.Split(':')[1], '', '', '', '')
                    // ShowModalError('modalMessage', data.d.Split(':')[1], 'alert alert-danger', 5000);
                }
            }

        },
        error: function(XMLHttpRequest, textStatus, errorThrown) {
            //showAlertError('', XMLHttpRequest.statusText, '')
            showAlertError('', ReadXmlTranslate('msg_error_generic'), '')
        }
    });
    // myid
}


// affiche le message 
function edition(myi) {
    doEdition(myi);
    // chercher toutes les places de cette cmd et ce dossier
}

// fait l'edition elle même
function doEdition(myi) {
    console.log(myi);


    listLi = $('#dialogForEditionInPop ul li');


    var arrEntrees = Array();

    var cmdid = $(myi).data("cmdid");
    var dossid = $(myi).data("dossierid");
    data = RewriteSessions(datajson);
    var seanceid = $(myi).data("seanceid");
    var myCmd = $.grep(data, function(obj) {
        return obj.Commande_id == cmdid && obj.Seance_id == seanceid;
    });

    var mySeats = $.grep(myCmd[0].seats, function(obj) {
        return obj.dossier_id == dossid;
    });
    $.each(mySeats, function(index, seat) {

        cmdId = cmdid;
        entreeid = seat.Entree_id;
        seanceid = seat.Seance_id;
        eventid = seat.Event_id;
        maqid = seat.Maquetteid;
        dossid = seat.dossier_id
        //dossid = dossid;

        hash = seat.Hash;

        var thisentree = new entree(entreeid, cmdId, dossid, seanceid, eventid, maqid, hash, '', '', '');
        console.log("entreeid=" + entreeid);

        arrEntrees.push(thisentree);
    });

    $.ajax({
        type: "POST",
        url: 'Histo.aspx/editionsEntrees',
        contentType: 'application/json; charset=utf-8',
        data: JSON.stringify({
            structEntrees: arrEntrees
        }),
        success: function(data, textStatus, jqXHR) {
            // download pdf
            redoPdf(myi);
            $('#modalEdition').modal('hide');
        },
        error: function(XMLHttpRequest, textStatus, errorThrown) {
            //showAlertError('', XMLHttpRequest.statusText, '')
            showAlertError('', ReadXmlTranslate('msg_error_generic'), '')
        }
    });
}


function doDV(myi) {
    //string commandeid, string entree_id, string event_id, string seance_id, string hash
    $(myi).removeClass('status_B').addClass('status_working');

    $.ajax({
        type: "POST",
        url: 'Histo.aspx/doDV',
        contentType: 'application/json; charset=utf-8',
        data: JSON.stringify({
            commande_id: $(myi).data("cmdid"),
            entree_id: $(myi).data("entreeid"),
            event_id: $(myi).data("eventid"),
            seance_id: $(myi).data("seanceid"),
            amount: $("#montantRepriseFinalValue").val(),
            hash: $(myi).data("hash")

        }),
        dataType: "json",
        success: function(data, textStatus, jqXHR) {
            $('#modalDepot').modal('hide');
            if (data.d == "ok") {

                $(myi).removeClass('status_working').addClass('status_done');
                var txt2dMarcheOk = ReadXmlTranslate('2dmarcheok');
                newSpan = $("<span class='label label-success'> <i class='fa fa-recycle'/>" + txt2dMarcheOk + "</span>");
                var td = $(myi).parent();
                $(td).append(newSpan)
                newSpan.hide().fadeIn('slow');
                $(myi).fadeOut('slow');
                td.find('.doPdf').fadeOut('slow')
            }
        },
        error: function(XMLHttpRequest, textStatus, errorThrown) {
            //showAlertError('', XMLHttpRequest.statusText, '')
            showAlertError('', ReadXmlTranslate('msg_error_generic'), '')
        }
    });
}



function proposeDV(myi) {
    //string commandeid, string entree_id, string event_id, string seance_id, string hash
    $(myi).removeClass('status_B').addClass('status_working');
    status_working_current = $(myi);

    $.ajax({
        type: "POST",
        url: 'Histo.aspx/infoDV',
        contentType: 'application/json; charset=utf-8',
        data: JSON.stringify({
            commande_id: $(myi).data("cmdid"),
            entree_id: $(myi).data("entreeid"),
            event_id: $(myi).data("eventid"),
            seance_id: $(myi).data("seanceid"),
            hash: $(myi).data("hash")
        }),
        dataType: "json",
        success: function(data, textStatus, jqXHR) {
            $('#btnValiderDepot').hide()
            datajson = JSON.parse(data.d);
            var url = "";
            var language = "";
            language = getLang();
            baseUrlDV = GetUrlTemplate("pageDepotVenteMessage");
            baseUrlEdition = GetUrlTemplate("pageEditionMessage");

            if (datajson != null && fileExists(baseUrlDV)) {
                // url = baseUrlDV + language + ".html";
                url = baseUrlDV;
                var text = $.ajax({
                    url: url,
                    dataType: 'text',
                    async: false
                });

                var title = text.responseText.split('||')[0];
                title = title.replace('[commandeId]', $(myi).data("cmdid"))
                $('#dialogForPropositionTitle').html(title);

                if (parseInt(datajson.montantReprise) > 0) {
                    var newhtml = text.responseText.split('||')[1].replace('[montantreprise]', parseFloat(datajson.montantReprise) / 100);
                    newhtml = newhtml.replace('[montantpaye]', parseFloat($(myi).data("amnt") / 100));
                    newhtml = newhtml.replace('[eventname]', $(myi).data("eventname"));
                    newhtml = newhtml.replace('[date]', $(myi).data("date"));
                    newhtml = newhtml.replace('[rang]', $(myi).data("rang"));
                    newhtml = newhtml.replace('[seat]', $(myi).data("seat"));
                    newhtml = newhtml.replace('[price]', $(myi).data("price"));
                    newhtml = newhtml.replace('[categname]', $(myi).data("categname"));
                    //
                    $('#dialogForPropositionDVInPop').html(newhtml);
                    //
                    depotVenteSetValues(myi, datajson)
                    $('#chkConditions').off('click').on('click', function() {
                        if ($('#chkConditions:checked').length > 0) {
                            $('#btnNextDepot, #btnValiderDepot').removeAttr('disabled');
                        } else {
                            $('#btnNextDepot, #btnValiderDepot').prop('disabled', 'disabled')
                        }
                    });

                    $('#btnNextDepot, #btnValiderDepot').prop('disabled', 'disabled')

                    if (datajson.typeReprise == "chosen") {
                        //si l'option repsise libre est activée
                        $('#btnAnnulerDepot, #btnNextDepot').show()
                        $('#btnBackDepot, #btnValiderDepot').hide()

                        $('#btnNextDepot').off('click').on('click', function() {
                            $('#btnAnnulerDepot, #btnNextDepot, .dodv-step1').hide()
                            $('#btnBackDepot, #btnValiderDepot, .dodv-step2').show()
                        })
                        $('#btnBackDepot').off('click').on('click', function() {
                            $('#btnAnnulerDepot, #btnNextDepot, .dodv-step1').show()
                            $('#btnBackDepot, #btnValiderDepot, .dodv-step2').hide()
                        })
                    } else {
                        //si l'option repsise libre est désactivée
                        $('#btnAnnulerDepot, #btnValiderDepot, .dodv-step1').show()
                        $('#btnNextDepot, #btnBackDepot, .dodv-step2').hide()
                    }

                    // show modal propose Dv
                    $('#modalDepot').modal('show');
                } else {
                    // mont < 0 : reprise impossible
                    if (parseInt(datajson.montantReprise) == -2) {
                        // renseigner son rib
                        idstructure = $('head').attr('structureid');

                        ShowGenericModal(ReadXmlTranslate('title_error_depot_vente_rib_not_found'), ReadXmlTranslate('msg_error_depot_vente_rib_not_found'), ReadXmlTranslate('btn_fill_rib'), ReadXmlTranslate('btn_cancel'), 'btn-fill-rib-confirm', '')

                        $('#btn-fill-rib-confirm').off('click').on('click', function() {
                            location.href = basUrl + "/BankDetails.aspx?idstructure=" + idstructure
                        })

                    } else {
                        ShowGenericModal('', ReadXmlTranslate('msg_error_depot_vente_forbidden'), ReadXmlTranslate('btn_got_it'), '', '', '')
                    }
                }

            }


        },
        error: function(XMLHttpRequest, textStatus, errorThrown) {
            //showAlertError('', XMLHttpRequest.statusText, '')
            showAlertError('', ReadXmlTranslate('msg_error_generic'), '')
        }
    });
    // myid
}

function depotVenteSetValues(item, datajson) {
    var rangeStep = 0.01
    var MontantPaye = parseFloat($(item).data("amnt"))
    var MontantReprise = parseFloat(datajson.montantReprise)
    var lastGap = ((MontantPaye / 100) - Math.floor(MontantPaye / 100)).toFixed(2)
    console.log("lastGap " + lastGap)
    $('#montantPayeWrapper').html("<strong>" + (MontantPaye / 100).toFixed(2) + " " + deviseCode + "</strong>")

    if (datajson.typeReprise == "chosen") {
        //si l'option repsise libre est activée
        switch (settings.histo.jyvaipluFreeType) {
            case 'allornothing':
                console.log('jyvaipluFreeType -> allornothing');
                $('#montantRepriseExplain').removeClass('text-right')
                $('#montantRepriseWrapper').html('<span id="montantRepriseAllOrNothingValue"><strong>' + 0 / 100 + ' ' + deviseCode + '</strong></span>')
                $('#montantRepriseRangeWrapper').html('<div class="form-group mt-2">' +
                    '<span class="mr-2 material-switch">' +
                    '<input id="montantRepriseAllOrNpthing" name="montantRepriseAllOrNpthing" type="checkbox">' +
                    '<label for="montantRepriseAllOrNpthing" class="bg-success"></label>' +
                    '</span>' +
                    '<span>' + ReadXmlTranslate('msg_depot_vente_allornothing_choice') + '</span>' +
                    '</div>');
                $('#montantRepriseRangeWrapper').addClass('col-12')
                $('#montantRepriseExplain').html('<span id="depotVenteExplainDonation">' + ReadXmlTranslate('msg_depot_vente_explain_montantdiff').replace('[montantdiff]', (MontantPaye / 100).toFixed(2) + ' ' + deviseCode) + '</span>')
                $('#montantRepriseFinalValue').val(0)
                $('#lastWarningDepotVente').html('<p>' + ReadXmlTranslate('msg_depot_vente_last_warning').replace('[montantreprise]', parseFloat(0 / 100).toFixed(2) + " " + deviseCode).replace('[montantdiff]', (MontantPaye / 100).toFixed(2) + ' ' + deviseCode) + '</p>')
                //switche update
                $('#montantRepriseAllOrNpthing').off('change').on('change', function() {
                    var montantToShow = 0
                    if ($(this).is(':checked')) {
                        montantToShow = MontantPaye
                    } else {
                        montantToShow = 0
                    }
                    $('#montantRepriseAllOrNothingValue').html("<strong>" + parseFloat(montantToShow / 100).toFixed(2) + " " + deviseCode + "</strong>")
                    $('#montantRepriseFinalValue').val(montantToShow)
                    $('#montantRepriseExplain').html('<span id="depotVenteExplainDonation">' + ReadXmlTranslate('msg_depot_vente_explain_montantdiff').replace('[montantdiff]', ((MontantPaye - montantToShow) / 100).toFixed(2) + ' ' + deviseCode) + '</span>')
                    $('#lastWarningDepotVente').html('<p>' + ReadXmlTranslate('msg_depot_vente_last_warning').replace('[montantreprise]', parseFloat(montantToShow / 100).toFixed(2) + " " + deviseCode).replace('[montantdiff]', ((MontantPaye - montantToShow) / 100).toFixed(2) + ' ' + deviseCode) + '</p>')
                })
                break;
            case 'donationonly':
                console.log('jyvaipluFreeType -> donationonly');
                $('#montantRepriseLblWrapper').hide()
                $('#montantRepriseWrapper').hide()
                $('.titleChooseAcompteAmount').hide()
                $('#montantRepriseFinalValue').val(0)
                $('#montantRepriseRangeWrapper').addClass('col-12')
                $('#montantRepriseRangeWrapper').html('<p id="depotVenteExplainDonation">' + ReadXmlTranslate('msg_depot_vente_donationonly_choice').replace('[montantdiff]', ((MontantPaye) / 100).toFixed(2) + ' ' + deviseCode) + '</p>');
                $('#lastWarningDepotVente').html('<p>' + ReadXmlTranslate('msg_depot_vente_last_warning').replace('[montantreprise]', parseFloat(0).toFixed(2) + " " + deviseCode).replace('[montantdiff]', ((MontantPaye) / 100).toFixed(2) + ' ' + deviseCode) + '</p>')
                break;
            default:
                console.log('jyvaipluFreeType -> libre');
                $('#montantRepriseWrapper').html('<span id="montantRepriseRangeValue"><strong>' + MontantReprise / 100 + ' ' + deviseCode + '</strong></span>')
                $('#montantRepriseRangeWrapper').html('<input type="range" class="custom-range" value="' + MontantReprise / 100 + '" min="0" max="' + MontantPaye / 100 + '" step="' + rangeStep + '" id="montantRepriseRange">');
                $("#montantRepriseRange").TouchSpin({
                    step: 0.01,
                    decimals: 2,
                    maxboostedstep: 500,
                    buttondown_class: "btn btn-sm btn-secondary text-center",
                    buttonup_class: "btn btn-sm btn-secondary text-center"
                });
                $('#montantRepriseExplain').html('<span id="depotVenteExplainDonation">' + ReadXmlTranslate('msg_depot_vente_explain_montantdiff').replace('[montantdiff]', ((MontantPaye - MontantReprise) / 100).toFixed(2) + ' ' + deviseCode) + '</span>')
                $('#montantRepriseFinalValue').val(MontantReprise)
                $('#lastWarningDepotVente').html('<p>' + ReadXmlTranslate('msg_depot_vente_last_warning').replace('[montantreprise]', (MontantReprise / 100).toFixed(2) + " " + deviseCode).replace('[montantdiff]', ((MontantPaye - MontantReprise) / 100).toFixed(2) + ' ' + deviseCode) + '</p>')
                //slide update
                $('#montantRepriseRange').off('touchspin.on.stopspin change input').on('touchspin.on.stopspin change input', function() {
                    $('#montantRepriseRangeValue').html("<strong>" + parseFloat($(this).val()).toFixed(2) + " " + deviseCode + "</strong>")
                    $('#montantRepriseFinalValue').val($(this).val() * 100)
                    $('#montantRepriseExplain').html('<span id="depotVenteExplainDonation">' + ReadXmlTranslate('msg_depot_vente_explain_montantdiff').replace('[montantdiff]', ((MontantPaye - $(this).val() * 100) / 100).toFixed(2) + ' ' + deviseCode) + '</span>')
                    $('#lastWarningDepotVente').html('<p>' + ReadXmlTranslate('msg_depot_vente_last_warning').replace('[montantreprise]', parseFloat($(this).val()).toFixed(2) + " " + deviseCode).replace('[montantdiff]', ((MontantPaye - $(this).val() * 100) / 100).toFixed(2) + ' ' + deviseCode) + '</p>')
                })
        }


    } else {
        //si l'option repsise libre est désactivée
        $('.titleChooseAcompteAmount').hide()
        $('#montantRepriseWrapper').html("<strong>" + (MontantReprise / 100).toFixed(2) + " " + deviseCode + "</strong>")
        $('#montantRepriseFinalValue').val(MontantReprise)
    }


}