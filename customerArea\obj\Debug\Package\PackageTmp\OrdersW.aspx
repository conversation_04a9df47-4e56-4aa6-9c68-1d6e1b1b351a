﻿<%@ Page Title="" Language="C#" MasterPageFile="~/pagemaster.Master" AutoEventWireup="true" CodeBehind="OrdersW.aspx.cs" Inherits="customerArea.OrdersW" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ctPlaceHold" runat="server">
    <!-- commentaire haut -->
    <div id="commentsPageHaut">
        <asp:Literal ID="litForCommentaireHaut" runat="server"></asp:Literal>
    </div>
    <h2 data-trad="title_my_reservations">My reservations</h2>
    <!-- liste -->
    <div class="resasHistoryWrapper" id="resasHistoryWrapper" style="">
        <div class="boxedBlock">
		  
                  <div id="InsertWidgetOrders"></div>	
        </div>
    </div>
    <!-- commentaires bas -->
    <div id="divCommentaireBas" class="small">
        <asp:Literal ID="litForCommentaireBas" runat="server"></asp:Literal>
    </div>

        <script src='<% =System.Web.Configuration.WebConfigurationManager.AppSettings["WidgetCustomersUrl"]%>'></script> 

      <script>
        // Page ready
        $(document).ready(function() {
            var queryString = window.location.search;
            var urlParams = new URLSearchParams(queryString);
            var ForceDate = urlParams.get('yymm')
            var ForceSession = urlParams.get('sessionid')
            initOrdersView();
            window.addEventListener('message', widgetListener);
        })
        //ecouteur des evenements, specifiques à la plteforme, des widgets
        function widgetListener(event) {
            //console.log(event.data);
            switch (event.data.action) {
                case ("openCustomerArea"):
                    $('#linkconnectLogin_inMenu').trigger('click');
                    break;
                
            }
        }
		function initOrdersView() {

	        SphereCustomer.OrdersView({
				selector: '#InsertWidgetOrders',
				structureId: <% = System.Web.HttpContext.Current.Session["idstructure"] %>,
                identityId: <% = System.Web.HttpContext.Current.Session["SVarUserIdentityID"].ToString() %>,
                identiteHash: '<% = identiteHash %>',
                webUserId: <% = System.Web.HttpContext.Current.Session["currIdUser"] %>,
                webUserHash: '<% = webUserHash %>',
				langCode: '<% = WidgetLangCode %>',
				partnerName: '<%=WidgetPartner %>',
				signature: '<%=WidgetSignature %>'
			});
		 
        }
        
    </script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ExtraScripts" runat="server">
</asp:Content>