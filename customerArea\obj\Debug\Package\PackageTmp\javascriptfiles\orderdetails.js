var urlVars;
$(document).ready(function() {
    urlVars = getUrlVars()
    //écrit l'order id
    if (urlVars.oid != null && urlVars.oid != undefined && urlVars.oid != "") {
        $('.orderId').html(urlVars.oid)
    }
    if ($('#receptVar').attr("data-iid") != null && $('#receptVar').attr("data-iid") != undefined && $('#receptVar').attr("data-iid") != "") {
        $('.identityId').html($('#receptVar').attr("data-iid"))
    }

    $('#backToOrders').attr('href', 'orders.aspx?idstructure=' + structureid)
    //resize l'iframe parent
    resizeParentIframe();

    if ($('head').attr('devisecode') != undefined)
        deviseCode = $('head').attr('devisecode');
    
    //init link consumers
    initLinkConsumers()
    
});

function initLinkConsumers() {
    //atacher les consommateurs par seance
    $('.selectLinkedConsumers').change(function() {
        if($(this).val() != "0" && $(this).val() != "newconsumer") {
            var selectConsumer = $(this)
            var selectConsumerVal = $(selectConsumer).val()
            var selectConsumerText = $(selectConsumer).find('option:selected').text()
            ShowGenericModal(ReadXmlTranslate('title_link_tickets_to_consummers'), ReadXmlTranslate('msg_link_tickets_to_consummers'), ReadXmlTranslate('btn_validate'), ReadXmlTranslate('btn_cancel'), 'confirmSelectLinkedConsumers', 'cancelSelectLinkedConsumers')

            $('#confirmSelectLinkedConsumers').on('click', function(e) {
                //lie un consommateur à une entrée
                var thisProduct = $(selectConsumer).closest('.productDetailsWrapper')
                $(thisProduct).find('.downLoadTicketBtn').prop('disabled', false).removeClass('disabled')
                $(thisProduct).find('.LinkthisConsumerWrapper').html("<span class=' text-success'>Attaché à " + selectConsumerText+"</span>")
            });
        }
        /*$.each($(this).closest('.productSeatsWrapper').find('.selectLinkedConsumers'), function(i, k) {
            var selectConsumerVal = $(k).val()
            var selectConsumerText = $(k).find('option:selected').text()
            if (selectConsumerVal != "" && selectConsumerVal != "0" && selectConsumerVal != "newconsumer") {
                $(k).closest('.LinkthisConsumerWrapper').html("Attaché à " + selectConsumerText)
            }
        })*/
    })
}