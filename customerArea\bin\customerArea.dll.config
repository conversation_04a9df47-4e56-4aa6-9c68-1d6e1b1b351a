﻿<?xml version="1.0" encoding="utf-8"?>
<!--
  For more information on how to configure your ASP.NET application, please visit
  http://go.microsoft.com/fwlink/?LinkId=169433
  -->
<configuration>
	<configSections>
		<sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
			<section name="customerArea.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
		</sectionGroup>
	</configSections>
	<!--
    Pour obtenir une description des modifications de web.config, voir http://go.microsoft.com/fwlink/?LinkId=235367.

    Les attributs suivants peuvent être définis dans la balise <httpRuntime>.
      <system.Web>
        <httpRuntime targetFramework="4.7.1" />
      </system.Web>
  -->
	<system.web>
		<compilation debug="true" targetFramework="4.8" />
		<httpRuntime targetFramework="4.5" />
		<trace enabled="true" pageOutput="true" />
		
	</system.web>
	<appSettings>
		<add key="vs:EnableBrowserLink" value="false" />
		<add key="ValidationSettings:UnobtrusiveValidationMode" value="None" />
		<add key="cacheListCommandes" value="90" />
		<add key="ThemisIniPathLocal" value="D:\Themisserver\[idstructureSur4zeros]\CONFIGSERVER\config.ini.xml" />
		<add key="ThemisIniPath" value="\\Srv-paiement64\customerfiles\TEST\[idstructureSur4zeros]\CONFIGSERVER\config.ini.xml" />
		<add key="ThemisIniPathProd" value="\\Srv-paiement64\customerfiles\PROD\[idstructureSur4zeros]\CONFIGSERVER\config.ini.xml" />
		<add key="physicalPathOfJs" value="D:\WORK\VIEUXPROJETS\customerAreaV2\customerArea\javascriptfiles\[idstructureSur4zeros]\" />
		<add key="physicalPathOfJsTest" value="\\**************\sites\TEST\Customer\current\javascriptfiles\[idstructureSur4zeros]\" />
		<add key="relativePathJsOfSite" value="http://localhost:56846/javascriptfiles/" />
		<!--<add key="relativePathJsOfSite" value="https://test.themisweb.fr/customer_dev/javascriptfiles/"/>-->
		<add key="physicalPathOfImages" value="D:\customerfiles\TEST\[idstructureSur4zeros]\Customer\IMAGES\banner.png" />
		<add key="relativePathImagesOfSite" value="https://test.themisweb.fr/customer/files/IMAGES/" />
		<add key="physicalPathOfCss" value="D:\customerfiles\[idstructureSur4zeros]\Customer\CSS\[idstructureSur4zeros].css" />
		<add key="physicalPathOfCssTest" value="D:\customerfiles\TEST\[idstructureSur4zeros]\Customer\CSS\[idstructureSur4zeros].css" />
		<add key="relativePathLessOfkiosk" value="https://dev.themisweb.fr/kiosk/Content/css/style.less" />
		<!--<add key="relativePathLessOfkioskProd" value="https://www.themisweb.fr/kiosk/[idstructureSur4zeros]/Content/css/style.less"/>-->
		<add key="relativePathCssOfSite" value="https://test.themisweb.fr/files/[idstructureSur4zeros]/Customer/CSS/" />
		<add key="TypeRun" value="TEST" />
		<add key="listWTsConns" value="\\**************\webservices\TEST\listConnexionsWTFlag\flagWTConnexions.flg" />
		<add key="FileAttenteMaxActivesUsers" value="250" />
		<add key="FileAttenteDelayInSeconds" value="120" />
		<add key="FileAttenteMaxActivesUsersForOneStructDefaultValue" value="100" />
		<add key="FileAttenteDelayInSecondsForOneStructDefaultValue" value="120" />
		<add key="configFileForLog4net" value="D:\logs\THEMIS\DEV\Customer\log4netconfig.xml" />
		<add key="LogDirectory" value="D:\LOGS\THEMIS\DEV\Customer" />
		<add key="template_menu_basketDiv" value="./templates/[idstructureSur4zeros]/menu.basketdiv.htm" />
		<add key="template_menu_basketDiv_empty" value="./templates/[idstructureSur4zeros]/menu.basketdiv.empty.htm" />
		<add key="template_menu_authentifdiv" value="./templates/[idstructureSur4zeros]/menu.authentifdiv.htm" />
		<add key="template_menu_authentifdiv_notauthentif" value="./templates/[idstructureSur4zeros]/menu.authentifdiv.notauthentif.htm" />
		<add key="template_menu_profilacheteurlogindiv_notauthentif" value="./templates/[idstructureSur4zeros]/menu.profilacheteurlogindiv.notauthentif.htm" />
		<add key="template_menu_profilacheteurlogindiv" value="./templates/[idstructureSur4zeros]/menu.profilacheteurlogindiv.htm" />
		<!--<add key="template_master_Login_stepsdiv" value="./templates/[idstructureSur4zeros]/master.steppopIdentificationDiv.htm" />
    <add key="template_master_Update_stepsdiv" value="./templates/[idstructureSur4zeros]/master.steppopIdentificationDiv.htm" />
    <add key="template_master_passWordReset_stepsdiv" value="./templates/[idstructureSur4zeros]/master.steppopIdentificationDiv.htm" />-->
		<add key="template_home" value="D:\customerfiles\DEV\[idstructureSur4zeros]\Customer\TEMPLATES\home.htm" />
		<add key="template_home_guest" value="D:\customerfiles\DEV\[idstructureSur4zeros]\Customer\TEMPLATES\homeguest.htm" />
		<add key="templateDisconnectProfil" value="D:\customerfiles\[idstructureSur4zeros]\Customer\TEMPLATES\menu.disconnectprofil.htm" />
		<add key="templateUpdateProfil" value="D:\customerfiles\DEV\[idstructureSur4zeros]\Customer\TEMPLATES\menu.updateprofil.htm" />
		<add key="templateHistoriqueProfil" value="D:\customerfiles\DEV\[idstructureSur4zeros]\Customer\TEMPLATES\menu.historique.htm" />
		<add key="templateBankDetailsProfil" value="D:\customerfiles\DEV\[idstructureSur4zeros]\Customer\TEMPLATES\menu.bankdetails.htm" />
		<add key="templateFansProfil" value="D:\customerfiles\DEV\[idstructureSur4zeros]\Customer\TEMPLATES\menu.reaboFans.htm" />
		<add key="templateHistoTransationProfil" value="D:\customerfiles\DEV\[idstructureSur4zeros]\Customer\TEMPLATES\menu.histoTransact.htm" />
		<add key="templateWaitList" value="D:\customerfiles\DEV\[idstructureSur4zeros]\Customer\TEMPLATES\menu.waitlist.htm" />
		<add key="templateLinkedConsumersList" value="D:\customerfiles\DEV\[idstructureSur4zeros]\Customer\TEMPLATES\menu.linkedconsumers.htm" />
		<add key="templateAttachments" value="D:\customerfiles\DEV\[idstructureSur4zeros]\Customer\TEMPLATES\menu.attachments.htm" />
		<add key="templateAdhesions" value="D:\customerfiles\DEV\[idstructureSur4zeros]\Customer\TEMPLATES\menu.adhesions.htm" />
		<add key="templateResaList" value="D:\customerfiles\DEV\[idstructureSur4zeros]\Customer\TEMPLATES\menu.resalist.htm" />
		<add key="templateOrdersW" value="D:\customerfiles\DEV\[idstructureSur4zeros]\Customer\TEMPLATES\menu.ordersw.htm" />
		<add key="templateTicketsW" value="D:\customerfiles\DEV\[idstructureSur4zeros]\Customer\TEMPLATES\menu.ticketsw.htm" />
		<add key="templateUrlTopopUpdateIdentification" value="Update.aspx?idstructure=[StructureId]&amp;EventId=[EventId]" />
		<add key="templateUrlTopopIdentification" value="Login.aspx?idstructure=[StructureId]&amp;EventId=[EventId]" />
		<add key="templateUrlToHistoCustomer" value="Histo.aspx?idstructure=[StructureId]&amp;EventId=[EventId]" />
		<add key="templateUrlToResaListCustomer" value="ResaList.aspx?idstructure=[StructureId]&amp;EventId=[EventId]" />
		<add key="templateUrlToOrdersWCustomer" value="OrdersW.aspx?idstructure=[StructureId]" />
		<add key="templateUrlToTicketsWCustomer" value="Ticketsw.aspx?idstructure=[StructureId]" />
		<add key="templateUrlToBankDetailsCustomer" value="BankDetails.aspx" />
		<add key="templateUrlTofans" value="Fans.aspx" />
		<add key="templateUrlToTransaction" value="HistoTransaction.aspx" />
		<add key="templateUrlToWaitList" value="WaitList.aspx" />
		<add key="templateUrlToLinkedConsumersList" value="linkedconsumerslist.aspx" />
		<add key="templateUrlToAttachments" value="attachments.aspx" />
		<add key="templateUrlToAdhesions" value="adhesions.aspx" />
		<add key="templateUrlToOrdersW" value="ordersW.aspx?idstructure=[StructureId]&amp;EventId=[EventId]" />
		<add key="templateUrlToTicketsW" value="ticketsW.aspx?idstructure=[StructureId]&amp;EventId=[EventId]" />
		<add key="templateAddConsumerFormTEST" value="D:\customerfiles\DEV\[idstructureSur4zeros]\Customer\TEMPLATES\addLinkedConsumerform.htm" />
		<add key="templateAddConsumerExistingFormTEST" value="D:\customerfiles\DEV\[idstructureSur4zeros]\Customer\TEMPLATES\addLinkedConsumerExistingform.htm" />
		<add key="templateAddConsumerForm" value="D:\customerfiles\DEV\[idstructureSur4zeros]\Customer\TEMPLATES\addLinkedConsumerform.htm" />
		<add key="templateAddConsumerExistingForm" value="D:\customerfiles\DEV\[idstructureSur4zeros]\Customer\TEMPLATES\addLinkedConsumerExistingform.htm" />
		<add key="IHMlogin" value="D:\customerfiles\DEV\[idstructureSur4zeros]\Customer\IHM\page_fUserLogin.xml" />
		<!--<add key="IHMlogin" value="D:\customerfiles\DEV\[idstructureSur4zeros]\customer\IHM\page_fUserLogin.xml" />-->
		<add key="IHMPROFILCreateLight" value="D:\customerfiles\DEV\[idstructureSur4zeros]\Customer\IHM\page_profil_create_light.xml" />
		<add key="IHMPROFILCreate" value="D:\customerfiles\DEV\[idstructureSur4zeros]\Customer\IHM\page_profil_create.xml" />
		<!--<add key="IHMPROFILCreateKioskLight" value="D:\customerfiles\[idstructureSur4zeros]\Customer\IHM\page_profil_create_kiosk_light.xml"/>
    <add key="IHMPROFILCreateKiosk" value="D:\customerfiles\[idstructureSur4zeros]\Customer\IHM\page_profil_create_kiosk.xml"/>
    <add key="IHMPROFILGuestKiosk" value="D:\customerfiles\[idstructureSur4zeros]\Customer\IHM\page_profil_guest_kiosk.xml"/>-->
		<add key="ihmAddConsumerForm" value="D:\customerfiles\DEV\[idstructureSur4zeros]\Customer\IHM\addLinkedConsumerform.json" />
		<add key="waitingListForm" value="D:\customerfiles\DEV\[idstructureSur4zeros]\Customer\IHM\waitingListform.json" />
		<add key="IHMPROFILCreateKioskLight" value="D:\customerfiles\DEV\[idstructureSur4zeros]\Customer\IHM\page_profil_create_kiosk_light.xml" />
		<add key="IHMPROFILCreateKiosk" value="D:\customerfiles\DEV\[idstructureSur4zeros]\Customer\IHM\\page_profil_create.xml" />
		<add key="IHMPROFILGuestKiosk" value="D:\customerfiles\DEV\[idstructureSur4zeros]\customer\IHM\page_profil_guest_kiosk.xml" />
		<add key="IHMPROFILGuest" value="D:\customerfiles\DEV\[idstructureSur4zeros]\Customer\IHM\page_profil_guest.xml" />
		<add key="IHMPROFILUpdate" value="D:\customerfiles\DEV\[idstructureSur4zeros]\Customer\IHM\page_profil_update.xml" />
		<add key="IHMPROFILAddChildFans" value="D:\customerfiles\DEV\[idstructureSur4zeros]\Customer\IHM\page_profil_add_child_fans.xml" />
		<add key="ListPaysFile" value="D:\customerfiles\TEST\[idstructure]\customerv3\filesinfos\iso_3166-1_list_en.xml" />
		<add key="MESSAGES" value="D:\customerfiles\TEST\[idstructureSur4zeros]\customer\IHM\messages_parametrable.xml" />
		<add key="MailPassWord" value="\\Srv-paiement64\customerfiles\TEST\[idstructureSur4zeros]\[PAIEMENT]\TEMPLATES\MAILS\mail_password.html" />
		<add key="MailInscription" value="\\Srv-paiement64\customerfiles\TEST\[idstructureSur4zeros]\[PAIEMENT]\TEMPLATES\MAILS\mail_inscription.html" />
		<add key="MailKiosk" value="\\Srv-paiement64\customerfiles\TEST\[idstructureSur4zeros]\[PAIEMENT]\TEMPLATES\MAILS\mail_kiosk.html" />
		<add key="MailDemandeDepotVente" value="\\Srv-paiement64\customerfiles\TEST\[idstructureSur4zeros]\[PAIEMENT]\TEMPLATES\MAILS\mail_depotvente.html" />
		<add key="MailSupportingDocuments" value="\\Srv-paiement64\customerfiles\TEST\[idstructureSur4zeros]\[PAIEMENT]\TEMPLATES\MAILS\mail_supportingdocuments.html" />
		<add key="MailSupportingDocumentsCustomer" value="\\Srv-paiement64\customerfiles\TEST\[idstructureSur4zeros]\[PAIEMENT]\TEMPLATES\MAILS\mail_supportingdocuments_customer.html" />
		<add key="redoPdf" value="\\Srv-paiement64\emails\TEST\[idstructureSur4zeros]\pdf\" />
		<add key="tempFileRedoPdf" value="\\**************\sites\DEV\Customer\current\tempfiles\" />
		<!--<add key="EmailEnvoyes" value="\\Srv-paiement64\emails\TEST\[idstructureSur4zeros]\inscription" />
  <add key="EmailEnErreurs" value="\\Srv-paiement64\emails\TEST\[idstructureSur4zeros]\envoisko" />
    -->
		<add key="emaildemandedepotventepath" value="\\Srv-paiement64\emails\TEST\[idstructureSur4zeros]\dvInform" />
		<add key="emailinscriptionpath" value="\\Srv-paiement64\emails\TEST\[idstructureSur4zeros]\inscription" />
		<add key="emailpasswordpath" value="\\Srv-paiement64\emails\TEST\[idstructureSur4zeros]\password" />
		<add key="FilesCommentairehaut" value="D:\customerfiles\TEST\[idstructureSur4zeros]\Customer\filesinfos\commentairehaut[page].htm" />
		<add key="FilesCommentairebas" value="D:\customerfiles\TEST\[idstructureSur4zeros]\Customer\filesinfos\commentairebas[page].htm" />
		<add key="TraductionXmlTEST" value="D:\customerfiles\TEST\[idstructureSur4zeros]\Customer\Resources\translate.xml" />
		<add key="TraductionXml" value="D:\customerfiles\DEV\[idstructureSur4zeros]\Customer\Resources\translate.xml" />
		<add key="TemplateCgu" value="D:\customerfiles\TEST\[idstructureSur4zeros]\Customer\TEMPLATES\cgu.htm" />
		<add key="TemplateCgvIndiv" value="D:\customerfiles\TEST\[idstructureSur4zeros]\INDIV\TEMPLATES\conditions.html" />
		<add key="relativePathCGV" value="http://localhost:56846/files/[idstructureSur4zeros]/CustomerV3/FILESINFOS/" />
		<add key="TemplateCgv" value="D:\customerfiles\TEST\[idstructureSur4zeros]\Customer\FILESINFOS\cgv.[lang].htm" />
		<add key="TemplateRGPD" value="D:\customerfiles\TEST\[idstructureSur4zeros]\Customer\TEMPLATES\rgpd.htm" />
		<add key="ResaPayExtraFormTemplate" value="D:\customerfiles\DEV\[idstructureSur4zeros]\Customer\TEMPLATES\resapay-extraform.htm" />
		<add key="SitePaiement" value="https://payment.themisweb.fr/testv2/Payment.aspx" />
		<add key="CryptoKey" value="RodWebShop95" />
		<add key="template_facturetteLocal" value="D:\customerfiles\[idstructureSur4zeros]\Customer\templates\facturette.html" />
		<add key="template_facturette" value="\\**************\customerfiles\PROD\[idstructureSur4zeros]\Customerv3\Templates\facturette.html" />
		<add key="template_facturette_RecapPanierLocal" value="D:\customerfiles\DEV\[idstructureSur4zeros]\CUSTOMER\TEMPLATES\recapcmd.htm" />
		<add key="template_facturette_RecapPanier" value="\\**************\customerfiles\PROD\[idstructureSur4zeros]\Customerv3\Templates\recapcmd.htm" />
		<add key="save_facturette" value="D:\customerfiles\DEV\[idstructureSur4zeros]\CUSTOMER\TEMPLATES\FACTURETTES\" />
		<add key="saveRelativePath_facturette" value="http://localhost:56846/files/[idstructureSur4zeros]/customer/templates/" />
		<add key="physicalPathOfAttachments" value="D:\customerfiles\DEV\[idstructureSur4zeros]\Customer\attachments\[customerId]\" />
		<add key="extensionsOfAttachments" value=".jpg,.jpeg,.png,.gif,.pdf" />
		<add key="physicalPathOfTranslateXmlLocal" value="D:\customerfiles\[idstructureSur4zeros]\Customer\Resources\" />
		<add key="physicalPathOfTranslateXml" value="D:\customerfiles\DEV\[idstructureSur4zeros]\Customer\Resources\" />
		<!-- facebook TEST -->
		<add key="facebook_appid" value="1103870345745420" />
		<add key="facebook_appsecret" value="********************************" />
		<add key="physicalPathOfSettingsJSONProd" value="D:\customerfiles\PROD\[idstructureSur4zeros]\Customer\APPSETINGS\appsettings.json" />
		<add key="physicalPathOfSettingsJSON" value="D:\customerfiles\DEV\[idstructureSur4zeros]\Customer\APPSETINGS\appsettings.json" />
		<add key="physicalPathOfSettingsJSONLocal" value="D:\customerfiles\[idstructureSur4zeros]\Customer\APPSETINGS\appsettings.json" />
		<add key="physicalPathOfSettingsPlateformJSON" value="D:\customerfiles\[idstructureSur4zeros]\[plateformCode]\APPSETINGS\appsettings.json" />
		<add key="physicalPathOfPostalCodeDataJSON" value="D:\customerfiles\[idstructureSur4zeros]\Customer\FILESINFOS\postal-codes.json" />
		<add key="paypalconnect_urltokenservice" value="https://api.sandbox.paypal.com/v1/identity/openidconnect/tokenservice" />
		<add key="paypalconnect_urluserinfo" value="https://api.sandbox.paypal.com/v1/identity/openidconnect/userinfo/?schema=openid" />
		<!--<add key="apiAuthentifUrl" value="http://localhost:50281/api" />-->
		<add key="template_header" value="D:\customerfiles\[idstructureSur4zeros]\Customer\TEMPLATES\header.htm" />
		<add key="template_footer" value="D:\customerfiles\[idstructureSur4zeros]\Customer\TEMPLATES\footer.htm" />
		<!-- API URL -->


		<!--<add key="apiAuthentifUrl" value="http://back-themis-ws/WS/API/AUTHENTICATIONS/v110/api" />
	  <add key="apiAuthentifPartenaire" value="RODRIGUE" />
	  <add key="apiAuthentifSignature" value="2f5*+1*gHafgff8" />-->

		<add key="apiAuthentifUrl" value="http://back-themis-ws/WSDEV/API/AUTHENTICATIONS/v1/api" />
		<add key="apiAuthentifPartenaire" value="RODRIGUE" />
		<add key="apiAuthentifSignature" value="ser5#E6V6Z#Mp-7" />
		<add key="apiDatasUrl" value="http://back-themis-ws/WSDEV/API/DATAS/V1/api" />

		<add key="apiCustomerUrl" value="http://**************/PROD/api_customers/v111/api" />
		<add key="apiCustomerUrlDev" value="http://**************/dev/api_customers/v202/api" />

		<add key="apiExternalUrl" value="https://localhost:7265/api" />

		<!--WIDGET JS Url -->
		<add key="WidgetJsCustomer" value="https://dev2.themisweb.fr/widgets/customers/sandbox_customArea/widget-js" />
		<add key="apiDatasUrlLocal" value="http://localhost:57874/api" />
		<!--<add key="apiCustomerUrl" value="http://localhost:58890/" />-->
		<add key="WidgetPartner" value="RODRIGUE" />
		<add key="Version" value="3.0.0" />
		<add key="logsLevel" value="Normal" />
		<add key="identiteSaltHash" value="RodWebShop95" />
		<add key="path_script_sql" value="\\**************\webservices\DEV\wcfThemis\scriptsSql\[directory\][filename][.structureid].sql" />

	</appSettings>
	<connectionStrings>
		<add name="WSAdminConnectionString" connectionString="Data Source=*************;Initial Catalog=WSAdmin_test;User ID=SphereWebTest;Password=************************************************************************************************************" providerName="System.Data.SqlClient" />

		<add name="WSAdminConnectionStringProd" connectionString="Data Source=************;Initial Catalog=WSAdmin;User ID=SupportRod;Password=****************************************************************************************" providerName="System.Data.SqlClient" />
	</connectionStrings>
	<applicationSettings>
		<customerArea.Properties.Settings>
			<setting name="customerArea_WsThemis_WSThemisAbo" serializeAs="String">
				<value>http://localhost:997/WSThemis.asmx</value>
			</setting>
			<setting name="customerArea_tipiws_PaiementSecuriseService" serializeAs="String">
				<value>http://www.tipi.budget.gouv.fr:80/tpa/services/mas_securite/contrat_paiement_securise/PaiementSecuriseService</value>
			</setting>
		</customerArea.Properties.Settings>
	</applicationSettings>
	<system.serviceModel>
		<bindings>
			<basicHttpBinding>
				<binding name="PaiementSecuriseServicePortBinding" />
				<binding name="BasicHttpBinding_Iwcf_wsThemis" sendTimeout="00:05:00" maxReceivedMessageSize="655360000" />
			</basicHttpBinding>
		</bindings>
		<client>
			<endpoint address="http://www.tipi.budget.gouv.fr:80/tpa/services/mas_securite/contrat_paiement_securise/PaiementSecuriseService" binding="basicHttpBinding" bindingConfiguration="PaiementSecuriseServicePortBinding" contract="Ntipiws.PaiementSecuriseService" name="PaiementSecuriseServicePort" />
			<endpoint address="http://localhost:55660/wcf-wsThemis.svc" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_Iwcf_wsThemis" contract="wcf_WsThemis.Iwcf_wsThemis" name="BasicHttpBinding_Iwcf_wsThemis" />
		</client>
	</system.serviceModel>
	<!--http://localhost:55660/wcf-wsThemis.svc-->
	<system.web.extensions>
		<scripting>
			<webServices>
				<jsonSerialization maxJsonLength="50000000" />
			</webServices>
		</scripting>
	</system.web.extensions>
	<system.webServer>
		<httpProtocol>
			<customHeaders>
				<add name="Access-Control-Allow-Origin" value="*" />
				<add name="Access-Control-Allow-Methods" value="GET, POST, PUT, DELETE" />
				<add name="Access-Control-Request-Methods" value="GET, POST, PUT, DELETE" />
				<add name="Access-Control-Allow-Headers" value="X-Requested-With,Access-Control-Request-Method,Access-Control-Request-Headers, accept, Content-Type" />
				<add name="Access-Control-Request-Headers" value="X-Requested-With,Access-Control-Request-Method,Access-Control-Request-Headers, accept, Content-Type" />
			</customHeaders>
		</httpProtocol>
		<handlers>
			<remove name="ExtensionlessUrlHandler-Integrated-4.0" />
			<remove name="OPTIONSVerbHandler" />
			<remove name="TRACEVerbHandler" />
			<add name="ExtensionlessUrlHandler-Integrated-4.0" path="*." verb="*" type="System.Web.Handlers.TransferRequestHandler" preCondition="integratedMode,runtimeVersionv4.0" />
		</handlers>
	</system.webServer>
	<runtime>
		<assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
			<dependentAssembly>
				<assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-13.0.0.0" newVersion="13.0.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Web.Http" publicKeyToken="31bf3856ad364e35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-5.2.3.0" newVersion="5.2.3.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Net.Http.Formatting" publicKeyToken="31bf3856ad364e35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-5.2.7.0" newVersion="5.2.7.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="log4net" publicKeyToken="669e0ddf0bb1aa2a" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-3.1.0.0" newVersion="3.1.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Web.Infrastructure" publicKeyToken="31bf3856ad364e35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-2.0.0.0" newVersion="2.0.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Antlr3.Runtime" publicKeyToken="eb42632606e9261f" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-3.5.0.2" newVersion="3.5.0.2" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="WebGrease" publicKeyToken="31bf3856ad364e35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-1.6.5135.21930" newVersion="1.6.5135.21930" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.IO.Pipelines" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Text.Encodings.Web" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Text.Json" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
		</assemblyBinding>
	</runtime>
</configuration>