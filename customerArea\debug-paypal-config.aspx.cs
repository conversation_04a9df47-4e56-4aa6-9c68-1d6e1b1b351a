using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Text;
using customerArea.classes;
using utilitaires2010;
using customerArea.App_Code;

namespace customerArea
{
    public partial class DebugPayPalConfig : System.Web.UI.Page
    {
        public string StructureId { get; set; } = "";
        public string ClientId { get; set; } = "";
        public string ClientSecret { get; set; } = "";
        public string ReturnUrl { get; set; } = "";
        public string TokenServiceUrl { get; set; } = "";
        public string UserInfoUrl { get; set; } = "";
        public string DiagnosticHtml { get; set; } = "";

        protected void Page_Load(object sender, EventArgs e)
        {
            try
            {
                // Récupérer l'ID de structure
                StructureId = Request.QueryString["idstructure"] ?? "991";
                
                // Simuler la session pour le test
                if (Session["idstructure"] == null)
                {
                    Session["idstructure"] = int.Parse(StructureId);
                }

                // Récupérer les URLs de configuration
                TokenServiceUrl = System.Web.Configuration.WebConfigurationManager.AppSettings["paypalconnect_urltokenservice"] ?? "Non configuré";
                UserInfoUrl = System.Web.Configuration.WebConfigurationManager.AppSettings["paypalconnect_urluserinfo"] ?? "Non configuré";

                // Récupérer les settings PayPal
                LoadPayPalSettings();

                // Générer le diagnostic
                GenerateDiagnostic();
            }
            catch (Exception ex)
            {
                DiagnosticHtml = $"<div class='alert alert-danger'><strong>Erreur :</strong> {ex.Message}</div>";
            }
        }

        private void LoadPayPalSettings()
        {
            try
            {
                string plateformCode = "Customer";
                string lang = "fr";
                int idpa = 0;
                int resulEvent = 0;

                List<CustomJsonSettings> listJsonSettingsToMerge = new List<CustomJsonSettings>
                {
                    new CustomJsonSettings() { KeyOfJsonPath = "physicalPathOfSettingsJSON", IsDefault = true },
                    new CustomJsonSettings() { KeyOfJsonPath = "physicalPathOfSettingsJSON", IsDefault = false },
                    new CustomJsonSettings() { KeyOfJsonPath = "physicalPathOfSettingsPlateformJSON", IsDefault = false }
                };

                dynamic globalPlateform = utilitaires2010.Initialisations.GetMergeSettings(
                    listJsonSettingsToMerge, 
                    int.Parse(StructureId), 
                    resulEvent, 
                    idpa, 
                    plateformCode, 
                    lang
                );

                // Récupérer Client ID
                try
                {
                    dynamic paypalAppIdDynamic = globalPlateform.login.payPal.appid.Value;
                    if (globalPlateform.customer != null && 
                        globalPlateform.customer.login != null && 
                        globalPlateform.customer.login.payPal != null && 
                        globalPlateform.customer.login.payPal.appid != null)
                    {
                        paypalAppIdDynamic = globalPlateform.customer.login.payPal.appid.Value;
                    }
                    ClientId = paypalAppIdDynamic?.ToString() ?? "Non configuré";
                }
                catch
                {
                    ClientId = "Erreur de récupération";
                }

                // Récupérer Client Secret
                try
                {
                    dynamic paypalSecretDynamic = globalPlateform.login.payPal.secret.Value;
                    if (globalPlateform.customer != null && 
                        globalPlateform.customer.login != null && 
                        globalPlateform.customer.login.payPal != null && 
                        globalPlateform.customer.login.payPal.secret != null)
                    {
                        paypalSecretDynamic = globalPlateform.customer.login.payPal.secret.Value;
                    }
                    ClientSecret = paypalSecretDynamic?.ToString() ?? "Non configuré";
                }
                catch
                {
                    ClientSecret = "Erreur de récupération";
                }

                // Récupérer Return URL
                try
                {
                    dynamic paypalReturnUrlDynamic = globalPlateform.login.payPal.returnurl.Value;
                    if (globalPlateform.customer != null && 
                        globalPlateform.customer.login != null && 
                        globalPlateform.customer.login.payPal != null && 
                        globalPlateform.customer.login.payPal.returnurl != null)
                    {
                        paypalReturnUrlDynamic = globalPlateform.customer.login.payPal.returnurl.Value;
                    }
                    ReturnUrl = paypalReturnUrlDynamic?.ToString()?.Replace("[structureid]", StructureId) ?? "Non configuré";
                }
                catch
                {
                    ReturnUrl = "Erreur de récupération";
                }
            }
            catch (Exception ex)
            {
                ClientId = "Erreur : " + ex.Message;
                ClientSecret = "Erreur : " + ex.Message;
                ReturnUrl = "Erreur : " + ex.Message;
            }
        }

        private void GenerateDiagnostic()
        {
            var diagnostic = new StringBuilder();
            
            diagnostic.Append("<div class='list-group'>");

            // Vérification Client ID
            if (string.IsNullOrEmpty(ClientId) || ClientId == "Non configuré")
            {
                diagnostic.Append("<div class='list-group-item list-group-item-danger'>❌ Client ID PayPal non configuré</div>");
            }
            else if (ClientId.StartsWith("AZ") || ClientId.StartsWith("Ac"))
            {
                if (ClientId.Contains("sandbox") || TokenServiceUrl.Contains("sandbox"))
                {
                    diagnostic.Append("<div class='list-group-item list-group-item-success'>✅ Client ID sandbox détecté</div>");
                }
                else
                {
                    diagnostic.Append("<div class='list-group-item list-group-item-warning'>⚠️ Client ID de production détecté - Utilisez sandbox pour les tests</div>");
                }
            }
            else
            {
                diagnostic.Append("<div class='list-group-item list-group-item-danger'>❌ Format Client ID invalide</div>");
            }

            // Vérification environnement
            if (TokenServiceUrl.Contains("sandbox"))
            {
                diagnostic.Append("<div class='list-group-item list-group-item-success'>✅ Configuration sandbox détectée</div>");
            }
            else if (TokenServiceUrl.Contains("api.paypal.com"))
            {
                diagnostic.Append("<div class='list-group-item list-group-item-warning'>⚠️ Configuration production détectée - Risque de blocage</div>");
            }

            // Vérification Return URL
            if (ReturnUrl.Contains("dev.themisweb.fr") || ReturnUrl.Contains("test.themisweb.fr"))
            {
                diagnostic.Append("<div class='list-group-item list-group-item-info'>ℹ️ URL de retour de développement détectée</div>");
            }

            // Vérification cohérence
            bool isSandboxUrl = TokenServiceUrl.Contains("sandbox");
            bool isProdClientId = !ClientId.Contains("sandbox") && (ClientId.StartsWith("AZ") || ClientId.StartsWith("Ac"));
            
            if (isSandboxUrl && isProdClientId)
            {
                diagnostic.Append("<div class='list-group-item list-group-item-danger'>❌ INCOHÉRENCE : URL sandbox avec Client ID production</div>");
            }
            else if (!isSandboxUrl && !isProdClientId)
            {
                diagnostic.Append("<div class='list-group-item list-group-item-danger'>❌ INCOHÉRENCE : URL production avec Client ID sandbox</div>");
            }

            diagnostic.Append("</div>");

            DiagnosticHtml = diagnostic.ToString();
        }
    }
}
