<%@ Page Language="C#" %>
<!DOCTYPE html>
<html>
<head>
    <title>Test PayPal Simple</title>
</head>
<body>
    <h1>PAGE PAYPAL TEST SIMPLE</h1>
    <p><strong>Code:</strong> <%= Request.QueryString["code"] %></p>
    <p><strong>State:</strong> <%= Request.QueryString["state"] %></p>
    <p><strong>URL complète:</strong> <%= Request.Url %></p>
    <p><strong>Tous les paramètres:</strong></p>
    <ul>
    <% foreach (string key in Request.QueryString.AllKeys) { %>
        <li><strong><%= key %>:</strong> <%= Request.QueryString[key] %></li>
    <% } %>
    </ul>
</body>
</html>
