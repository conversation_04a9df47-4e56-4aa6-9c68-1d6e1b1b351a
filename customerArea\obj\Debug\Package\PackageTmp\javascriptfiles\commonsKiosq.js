var mBackdrop;
// LESSREADY se déclenche lorsque le LESS/CSS a fini de charger, important pour une modification graphique via JS
function lessReady() {
    /******** MIN HEIGHT MAIN CONTENT *******/
    mainContentHeight();
}

$(document).ready(function() {
    /******** TOOLTIPS *************/
    $('[data-toggle="tooltip"]').tooltip()
    /******** VIRTUAL KEYBOARD *******/
    $('#virtualkeyboard').jkeyboard({
        layout: "fr",
    });
    //change focus virtualkeyboard
    $('input,textarea').focus(function() {
        keyboardChangefocus(this)
    })

    $('#modalGeneric').on('show.bs.modal', function(e) {
        mBackdrop = setInterval(haveBackdrop, 10);
    })
})

$(window).resize(function() {
    mainContentHeight()
});

/******** MIN HEIGHT MAIN CONTENT *******/
function mainContentHeight() {
    var windowHeight = $(window).height()
    var mainNavigationHeight = $('#mainNavigation nav').outerHeight() || 0;
    var minHeight = windowHeight - mainNavigationHeight
    $('#mainContent').css('min-height', minHeight + "px").css('margin-top', mainNavigationHeight + "px");
}

/******** VIRTUAL KEYBOARD *******/
// change la destination/focus du keyboard
function keyboardChangefocus(thisfield) {
    $('#virtualkeyboard').jkeyboard('setInput', thisfield);
}

/********* MODAL GENERIQUE *************/
function haveBackdrop() {
    if ($('.modal-backdrop').length > 0) {
        $('.modal-backdrop').addClass('col-9')
        clearInterval(mBackdrop);
    }
}



/******** disable le multi-touch (ie zoom) ****/
window.addEventListener('touchstart', function(event) {
  if (event.targetTouches.length >= 2) {
    console.log('Found and prevented multi-touch gesture on touchstart');
    event.preventDefault();
  }
}, {passive: false});
window.addEventListener('touchmove', function(event) {
  if (event.targetTouches.length >= 2) {
    console.log('Found and prevented multi-touch gesture on touchmove');
    event.preventDefault();
  }
}, {passive: false});

/******** END disable le multi-touch (ie zoom) ****/
