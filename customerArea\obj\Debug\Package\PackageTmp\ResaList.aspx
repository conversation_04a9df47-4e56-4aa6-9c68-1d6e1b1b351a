﻿<%@ Page Title="" Language="C#" MasterPageFile="~/pagemaster.Master" AutoEventWireup="true" CodeBehind="ResaList.aspx.cs" Inherits="customerArea.ResaList" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ctPlaceHold" runat="server">
    <!-- commentaire haut -->
    <div id="commentsPageHaut">
        <asp:Literal ID="litForCommentaireHaut" runat="server"></asp:Literal>
    </div>
    <h2 data-trad="title_my_reservations">My reservations</h2>
    <!-- liste -->
    <div class="resasHistoryWrapper" id="resasHistoryWrapper" style="">
        <div class="boxedBlock">
		    <% if(LstReservations.Count() > 0) { %>
		    	
			    <% foreach(var order in LstReservations){ 
                        %>

	            <!-- one order -->
	            <div class="oneOrder">
	                <div class="row  align-items-center">
	                    <div class="col-12 col-lg-auto text-center orderTypeWrapper">
	                        <span class="orderType" data-trad="lbl_reservation">Réservation</span>
	                        <span class="orderId"><%= order.OrderId.ToString("# ##0") %></span>
	                    </div>
	                    <div class="col text-center text-lg-left">
	                        <span class="orderDate"><span data-trad="lbl_order_placed_on">Effectuée le</span> <span data-datetoformat="<%= order.Date_reservation %>" data-datelocale="year,month,day2"><%= order.Date_reservation %></span></span><br/>
	                        <div class="orderAmount">
	                        	<% if(order.NbArticle > 1) { %>
	                        		<%= order.NbArticle %> <span data-trad="lbl_article_plural">articles</span> : <span data-priceformat="<%= order.AmountReserved %>"><%= order.AmountReserved %></span>
	                        	<% }else { %>
	                        		<%= order.NbArticle %> <span data-trad="lbl_article_singular">article</span> : <span data-priceformat="<%= order.AmountReserved %>"><%= order.AmountReserved %></span>
	                        	<% } %>
	                        	
	                        
	                        </div>
	                        <!--<div class="orderRAP"><span data-trad="lbl_amount_to_pay">À payer :</span> <span data-priceformat="<%= order.AmountToPay %>"><%= order.AmountToPay %></span></div>-->
	                    </div>
                            <%if (!order.IsPartiallyPaid)
                            {
			                    %>
	                    <div class="col-12 col-lg-auto text-center text-lg-right">
	                        <a href="ResaDetails.aspx?idstructure=<%= order.StructureId %>&rid=<%= order.OrderId %>&lang=<%= LangCode %>" class="btn btn-outline-secondary" target="_self"><i class="far fa-eye" aria-hidden="true"></i> <span data-trad="lbl_reservation_details">Détails de la réservation</span></a>
                            <a href="ResaPay.aspx?idstructure=<%= order.StructureId %>&rid=<%= order.OrderId %>&lang=<%= LangCode %>" class="btn btn-outline-primary" target="_self" data-trad="lbl_pay_my_reservation">Payer ma réservation</a>
	                    </div>
                        <% }  
                            %>
	                </div>
	            </div>
	            <!--one order end -->
	            <% 

                       

                    } %>
        	<% } else { %>
        	<div data-trad="lbl_no_reservation">Vous n'avez aucune réservation en cours</div>
        	<% } %>
        </div>
    </div>
    <!-- commentaires bas -->
    <div id="divCommentaireBas" class="small">
        <asp:Literal ID="litForCommentaireBas" runat="server"></asp:Literal>
    </div>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ExtraScripts" runat="server">
</asp:Content>