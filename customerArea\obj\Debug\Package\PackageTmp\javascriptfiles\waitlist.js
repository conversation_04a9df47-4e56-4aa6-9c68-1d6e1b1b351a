﻿
var arrEvents = new Array();

$(document).ready(function () {
    LoadEventsAndSessionsList();

    FillEventsList();

    //   $("#selectManifestations option[value='" + eventidtoselected + "']").prop('selected', 'selected');

    $("#selectManifestations").on('change', function () {

        FillSessionsList();


        if ($("#selectSeances").val() == null) {

        } else {
            if ($("#selectSeances").val().length > 0) {

                $("#selectSeances").empty();
                $("#selectSeances").append(new Option(ReadXmlTranslate('lbl_choose_seance'), 0));
                $("#selectSeances").trigger("chosen:updated");

                //FillCategoriesList();

            }
        }
    });

    /*$("#selectSeances").on('change', function () {
        FillCategoriesList();
    });

    FillCategoriesList();*/



    $("#btnValiderWaitList").on('click', function (e) {
        e.preventDefault();
        e.stopPropagation();
        var curForm = $('#WaitListCreationForm');
        if (validForm(curForm)) {
            InsertWaitList();
        } else {
            showAlertError('', ReadXmlTranslate("msg_error_update"), '')
        }
    });

    //vider le formulaire
    //recharger liste de souhaits
    GetWaitListOfIdentite();
});


function LoadEventsAndSessionsList() {
    $.ajax({
        type: "POST",
        url: 'Commons.asmx/LoadEventsAndSessionsList',
        contentType: 'application/json; charset=utf-8',
        dataType: "json",
        async: false,
        success: function (response) {

            if (response.d != null) {
                $.each(response.d, function () {

                    var evts = {};
                    evts.EventId = this.EventId;
                    evts.EventName = this.EventName;
                    var arrSessions = new Array();

                    $.each(this.ListSessions, function () {

                        var sessions = {};
                        sessions.SessionId = this.SessionId;
                        sessions.sSessionStartDate = this.sSessionStartDate;

                        var arrCategs = new Array();
                        $.each(this.ListCategories, function () {
                            var categ = {};
                            categ.CategId = this.CategId;
                            categ.Category_name = this.Category_name;

                            arrCategs.push(categ);
                        });

                        sessions.LstCategs = arrCategs;
                        arrSessions.push(sessions);
                    });

                    evts.LstSessions = arrSessions;
                    arrEvents.push(evts);
                });
            }
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            // ShowError("error", XMLHttpRequest.responseJSON.Message + "<br />" + XMLHttpRequest.responseJSON.StackTrace + "\r\n", "alert alert-danger alert-dismissable", 5000);
            $('#modalForWaiting').modal('hide');
            console.log(XMLHttpRequest.responseJSON.Message + "\r\n" + XMLHttpRequest.responseJSON.StackTrace);
        }
    });
}

function FillEventsList() {
    var optionsManifs = $("#selectManifestations");
    var eventidtoselected = 0;
    optionsManifs.empty();
    optionsManifs.prepend("<option value='0'>" + ReadXmlTranslate('lbl_choose_event') + "</option>");
    $.each(arrEvents, function () {
        optionsManifs.append(new Option(this.EventName, this.EventId));
    });

    if (getUrlVars()["EventId"] != undefined && getUrlVars()["EventId"] != 0) {
        eventidtoselected = getUrlVars()["EventId"];
        optionsManifs.hide();
        optionsManifs.val(eventidtoselected);
        $("#colmanifestations").text($("#selectManifestations option:selected").text());
        $("#colmanifestations").removeClass('d-none');
        // FOR LATER (catégories)
        /*
        $("#selectCategories").empty();
        $("#selectCategories").chosen({ no_results_text: "Oops, nothing found!", placeholder_text_single: "Choisissez une catégorie" });
        $("#selectCategories").trigger("chosen:updated");
        */
    } else {
        $("#colmanifestations").addClass('d-none');
    }
    FillSessionsList();

}

//reprendre cette fonction l'autre on desactive le chosen et on cache pour ne pas afficher toutes les manifs
//a utiliser lorsque la requête pour les dispos est faite
function FillEventsListOrigin() {
    var optionsManifs = $("#selectManifestations");
    var eventidtoselected = 0;
    optionsManifs.empty();
    optionsManifs.prepend("<option value='0'>" + ReadXmlTranslate('lbl_choose_event') + "</option>");
    $.each(arrEvents, function () {
        optionsManifs.append(new Option(this.EventName, this.EventId));
    });
    if (getUrlVars()["EventId"] != undefined && getUrlVars()["EventId"] != 0) {
        eventidtoselected = getUrlVars()["EventId"];
        optionsManifs.chosen();
        optionsManifs.val(eventidtoselected);
        optionsManifs.trigger("chosen:updated");
    }

    $("#selectCategories").empty();
    $("#selectCategories").chosen({ no_results_text: "Oops, nothing found!", placeholder_text_single: "Choisissez une catégorie" });
    $("#selectCategories").trigger("chosen:updated");

    FillSessionsList();

}

function FillSessionsList() {
    var optionsSeances = $("#selectSeances");
    var manifIdSelected = $("#selectManifestations option:selected").val();
    //séances de la manifs sélectionnée
    var arrSessionOfEventSelect = $.grep(arrEvents, function (a) {
        return a.EventId == manifIdSelected;
    })
    optionsSeances.empty();

    if (arrSessionOfEventSelect.length > 0) {
        $.each(arrSessionOfEventSelect[0].LstSessions, function () {
            optionsSeances.append(new Option(this.sSessionStartDate, this.SessionId));
        });
    }
    optionsSeances.chosen({ no_results_text: ReadXmlTranslate('lbl_no_result_found'), placeholder_text_multiple: ReadXmlTranslate('lbl_choose_seance') });
    optionsSeances.trigger("chosen:updated");
}


function FillCategoriesList() {

    var optionsCategs = $("#selectCategories");

    var manifIdSelected = $("#selectManifestations option:selected").val();
    //séances de la manifs sélectionnée
    var arrSessionOfEventSelect = $.grep(arrEvents,
        function (a) {
            return a.EventId == manifIdSelected;
        });

    var seanceIdSelected = 0;
    if ($("#selectSeances option:selected").val() == undefined)
        seanceIdSelected = $("#selectSeances")[0].options[0].value;
    else
        seanceIdSelected = $("#selectSeances option:selected").val();

    //séances de la manifs sélectionnée
    var arrCategsOfSeanceSelect = $.grep(arrSessionOfEventSelect[0].LstSessions,
        function (a) {
            return a.SessionId == seanceIdSelected;
        });


    //optionsSeances.find('option').remove().end();
    optionsCategs.empty();
    optionsCategs.append(new Option("Choisissez une catégorie", 0));

    if (arrCategsOfSeanceSelect.length > 0) {
        $.each(arrCategsOfSeanceSelect[0].LstCategs, function () {
            optionsCategs.append(new Option(this.Category_name, this.CategId));
        });
    }

    optionsCategs.chosen();
    optionsCategs.trigger("chosen:updated");

}

function InsertWaitList() {

    var ListeAttenteEntity = {};
    ListeAttenteEntity.ManifestationID = $("#selectManifestations option:selected").val();
    ListeAttenteEntity.SeanceID = ($("#selectSeances option:selected").val() == undefined) ? 0 : $("#selectSeances option:selected").val();
    ListeAttenteEntity.Quantite = ($('input[name="nbPlace"]').val() == "") ? 0 : $('input[name="nbPlace"]').val();
    ListeAttenteEntity.CategoryID = $("#selectCategories option:selected").val();
    ListeAttenteEntity.Commentaire = $("#comment").val();
    ListeAttenteEntity.OkStrap = $('input[name="OKstrapontin"]').is(':checked') ? 1 : 0;
    ListeAttenteEntity.OkSepare = $('input[name="OKsepare"]').is(':checked') ? 1 : 0;

    if (ListeAttenteEntity.Quantite > 0 && ListeAttenteEntity.Quantite <= 20) {
        var sData = JSON.stringify({ waitListObject: ListeAttenteEntity });

        $.ajax({
            type: "POST",
            url: 'Commons.asmx/InsertWaitList',
            contentType: 'application/json; charset=utf-8',
            dataType: "json",
            data: sData,
            async: false,
            success: function (data) {
                //ShowModalError('modalMessage', ReadXmlTranslate(data.d.split(':')[1]), ReadXmlTranslate(data.d.split(':')[2]), 'alert alert-' + data.d.split(':')[0], 5000);
                showAlertSuccess(ReadXmlTranslate(data.d.split(':')[1]), ReadXmlTranslate(data.d.split(':')[2]), '')
                GetWaitListOfIdentite();
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                showAlertError('', XMLHttpRequest.responseJSON + "\r\n" + XMLHttpRequest.responseJSON.StackTrace, '')
                // ShowError("error", XMLHttpRequest.responseJSON.Message + "<br />" + XMLHttpRequest.responseJSON.StackTrace + "\r\n", "alert alert-danger alert-dismissable", 5000);
                //console.log(XMLHttpRequest.responseJSON + "\r\n" + XMLHttpRequest.responseJSON.StackTrace);
            }
        });

    } else {
        showAlertError('', ReadXmlTranslate('msg_error_update'), '')
        // ShowModalError('modalMessage', ReadXmlTranslate('title_msg_error_object_waitlist'), ReadXmlTranslate('msg_error_object_waitlist'), 'alert alert-error', 5000);
    }
}


function GetWaitListOfIdentite() {

    $.ajax({
        type: "POST",
        url: 'Commons.asmx/GetWaitListOfIdentite',
        contentType: 'application/json; charset=utf-8',
        dataType: "json",
        success: function (data) {
            fillDataTableWaitList(data.d);
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            // ShowError("error", XMLHttpRequest.responseJSON.Message + "<br />" + XMLHttpRequest.responseJSON.StackTrace + "\r\n", "alert alert-danger alert-dismissable", 5000);
            console.log(XMLHttpRequest.responseJSON + "\r\n" + XMLHttpRequest.responseJSON.StackTrace);
        }
    });


}



function fillDataTableWaitList(data) {
    oTableOrdersList = $('#wait_list_table').DataTable({
        "bDestroy": true,
        "bProcessing": false,
        "responsive": {
            details: {
                type: 'column',
                target: 0
            }
        },
        "aoColumnDefs": [{
            className: 'control align-middle',
            targets: 0,
            orderable: false
        }],
        //"bSort": true,
        //"bFilter": true,
        "bAutoWidth": true,
        "iDisplayLength": -1,
        "aLengthMenu": [[10, 25, 50, 100, -1], [10, 25, 50, 100, ReadXmlTranslate('lbl_choose_all_datatable')]],
        "bDeferRender": true,
        "aaData": data,
        "oLanguage": GetLanguageDataTable(),
        "aaSorting": [[1, "asc"]],
        "aoColumns": [
        {
            //nb de places
            "mRender": function (data, type, full) {
                return "";
            }
        },
        {
            "mDataProp": "EventName"
        },
            /*{
                "mDataProp": "StrStartDate"
            },*/
            { "mDataProp": "Quantite" },
			// { "mDataProp": "OkSepare" },
            //{ "mDataProp": "OkStrap" },
            {
                "mDataProp": "OkSepare",
                "mRender": function (data, type, full) {

                    var html = '';
                    if (data == 0)
                        html = ReadXmlTranslate('lbl_no');
                    else
                        html = ReadXmlTranslate('lbl_yes');

                    return html;
                },
                "fnCreatedCell": function (nTd, sData, oData, iRow, iCol) {
                    if (sData == 0) {
                        $(nTd).addClass('dataNo')
                    } else {
                        $(nTd).addClass('dataYes')
                    }
                }
            },
            {
                "mDataProp": "OkStrap",
                "mRender": function (data, type, full) {

                    var html = '';
                    if (data == 0)
                        html = ReadXmlTranslate('lbl_no');
                    else
                        html = ReadXmlTranslate('lbl_yes');

                    return html;
                },
                "fnCreatedCell": function (nTd, sData, oData, iRow, iCol) {
                    if (sData == 0) {
                        $(nTd).addClass('dataNo')
                    } else {
                        $(nTd).addClass('dataYes')
                    }
                }
            },
            { "mDataProp": "Commentaire" },
            {
                "mDataProp": "ID",
                "sClass": "text-center",
                "mRender": function (url, type, full) {
                    //var html = '<a href="" data-id="' + full.ID + '">Supprimer</a>';
                    var html = '<span class="sp-tooltip-delete"><a class="delete confirm" href="" data-id="' + full.ID + '" data-toggle="tooltip"><i class="fas fa-trash-alt"></i></a></span>';
                    return html;
                }
            },

        ]
    });

    $(".delete.confirm").on('click', function (e) {
        e.preventDefault();
        $(this).addClass('selected');
        var listattenteid = $('.selected').data('id');
        ShowGenericModal(ReadXmlTranslate('title_msg_delete_waiting_list'), ReadXmlTranslate('msg_delete_waiting_list'), ReadXmlTranslate('btn_delete_my_selection'), ReadXmlTranslate('btn_cancel'), 'confirmDeleteSelection', 'cancelDeleteSelection')
        $('#confirmDeleteSelection').on('click', function (e) {
            DeleteWaitingList(listattenteid)
        });
    });
}

function DeleteWaitingList(listattenteid) {
    var sData = JSON.stringify({ listAttenteId: listattenteid });
    $.ajax({
        type: "POST",
        url: 'Commons.asmx/DeleteListeAttente',
        data: sData,
        contentType: 'application/json; charset=utf-8',
        dataType: "json",
        success: function (data) {
            if (data.d.split(':')[0] == "success") {
                showAlertSuccess('', ReadXmlTranslate(data.d.split(':')[1]), '')
                GetWaitListOfIdentite();
            } else {
                showAlertError('', ReadXmlTranslate(data.d.split(':')[1]), '')
            }
        }
    });
}