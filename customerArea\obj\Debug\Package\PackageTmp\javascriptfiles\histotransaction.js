﻿
var sAjaxSourceUrl = "fans.aspx/";
var oTableOrdersUserList;


$(document).ready(function () {

    CheckMyAbo($('head').attr("myidentity"), $('head').attr("ctrlh"));

});



function CheckMyAbo(idi, hash) {


    try {
        $.ajax({
            type: "POST",

            url: sAjaxSourceUrl + 'GetCommandsOfIdentite',
            contentType: 'application/json; charset=utf-8',
            data: JSON.stringify({
                identiteIdChild: idi,
                hash: hash
            }),
            dataType: "json",
            success: function (response) {
                $('#loading').html("");

                //var table = $('#orders_user_list_table').DataTable();
                fillDataTableOrdersUser(response.d);

            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                //ShowModalError("modalMessage", "Erreur pendant le chargement des logos", XMLHttpRequest.responseJSON.Message + "<br />" + XMLHttpRequest.responseJSON.StackTrace, "alert alert-danger alert-dismissable", 5000);
                console.log(XMLHttpRequest.responseJSON.Message + "\r\n" + XMLHttpRequest.responseJSON.StackTrace);
            },
            complete: function () {

                //Simule le changement du nombre de résultat car par défaut on charge tout
                $.fancybox.close();

            }
        });

    } catch (e) {
        console.log(e.Message);
    }

}


///Rempli la datatable des commandes de l'utilisateur connecté
function fillDataTableOrdersUser(data) {

    $('#orders_user_list_wrapper').html(formatDetailCommande(data));
    /* oTableOrdersUserList = $('#orders_user_list_table').DataTable({
         "bDestroy": true,
         "bProcessing": false,
         "responsive": true,
         "bSort": true,
         "bFilter": true,
         "bAutoWidth": true,
         "iDisplayLength": -1,
         "bDeferRender": true,
         "aaData": data,
         "aoColumns": [
             {
                 "mDataProp": "manif_nom"
             },
             { "mDataProp": "sessionDate" },
             { "mDataProp": "manif_nom" }
             //{
             //    "mDataProp": "Identite_id",
             //    "mRender": function (data, type, full) {
             //        var html = '<btn class="checkAbo btn btn-success" data-idt_=' + full.Identite_id + ' id="idt_' + full.Identite_id + '" onclick=\'CheckAbo(' + full.Identite_id + ',"' + full.hashKey + '");\'>' + ReadXmlTranslate('btn_check_abo') + '</btn>';
 
             //        return html;
             //    }
             //}
         ]
     });*/
}











var aboCharges = new Array();
/* Détail du check abo pour une commande */
function formatDetailCommande(d) {
    html = "";


    if (d.length > 0) {
        $.each(d, function (idx, comm) {
            $.each(comm.listAbonnement, function (idx2, abo) {
                aboCharges.push(abo);

                // `d` is the original data object for the row
                var etat = abo.listDossiersEntree[0].etat;
                //  html += '<table cellpadding="4" cellspacing="0" border="0" style="padding-left:50px;">' +
                html += '<table class="table table-striped" border="0" cellpadding="4" cellspacing="0" margin="0px" padding="0px">' +


                    '<tr>' +
                        '<td>Command id: ' + comm.commandeid + '</td>' +

                        '<td>Abo id: ' + abo.aboId + '</td>' +
                        '<td>Formula: ' + abo.formulaName + '</td>' +
                        '<td>Amount:<span class="montant">' + abo.amountTotal + '</span></td>';
                html += '<td>Etat:<span class="alert-danger badge">' + etat + '</span></td>';



                if (etat == "R") {
                    html += '<td><btn class="addBasket btn btn-success" data-idt=' + comm.identiteid + '  data-formula=' + comm.listDossiersEntree[0].formuleAboId + ' data-cmd=' + comm.commandeid
                                + ' onclick=\'addBasket(' + comm.identiteid + ',' + comm.commandeid + ',' + abo.aboId + ',' + abo.formulaId + ',"' + comm.hashKey + '");\'><span class="btn btn-success"><span class="glyphicon glyphicon-shopping-cart"></span></span>' + ReadXmlTranslate("btn_add_basket") + '</btn></td>';
                }
                html += '</tr>';


                html += '<tr><td colspan="6">';
                //html += '<table cellpadding="3" cellspacing="0" border="0" style="padding-left:50px;">';
                html += '<table class="table table-striped" border="0" cellpadding="3" cellspacing="0" margin="0px" padding="0px">';

                html += '<tr>' +

                        '<th>event</th>' +
                        '<th>price name</th>' +
                        '<th>amount</th>' +
                 '</tr>';

                $.each(abo.listDossiersEntree, function (idx2, doss) {
                    $.each(doss.ListSeats, function (idx3, seat) {
                        html += '<tr>' +
                            '<td>' + doss.manif_nom + '</td>' +
                            '<td>' + seat.type_tarif_nom + '</td>' +
                            '<td><span class="montant">' + seat.amountPaidCents + '</span></td>' +
                        '</tr>';
                    });
                });
            });

            html += '</table></td></tr>';
            html += '</table>';
        });
    } else {
        html = ReadXmlTranslate('lbl_aucune_commande');

    }
    return html;
}
