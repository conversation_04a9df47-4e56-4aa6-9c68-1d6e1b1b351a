<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<link rel="shortcut icon" type="image/ico" href="http://www.datatables.net/favicon.ico">
	<meta name="viewport" content="initial-scale=1.0, maximum-scale=2.0">
	<link rel="stylesheet" type="text/css" href="../resources/syntax/shCore.css">
	<link rel="stylesheet" type="text/css" href="../resources/demo.css">
	<script type="text/javascript" language="javascript" src="../../media/js/jquery.js"></script>
	<script type="text/javascript" language="javascript" src="../resources/syntax/shCore.js"></script>
	<script type="text/javascript" language="javascript" src="../resources/demo.js"></script>

	<title>DataTables examples - Advanced initialisation</title>
</head>

<body class="dt-example">
	<div class="container">
		<section>
			<h1>DataTables example <span>Advanced initialisation</span></h1>

			<div class="info">
				<p>The configuration options offered by DataTables extend much further than the options shown in the basic initialisation of this documentation. Through
				combinations of the options available and the use of callbacks, DataTables is completely customisable and will fit into exactly what you need for your table
				display.</p>

				<p>This section shows some more advanced initialisation options. Keep in mind also that each example can be combined with the other examples to get what you
				want!</p>
			</div>
		</section>
	</div>

	<section>
		<div class="footer">
			<div class="gradient"></div>

			<div class="liner">
				<div class="toc">
					<div class="toc-group">
						<h3><a href="./index.html">Advanced initialisation</a></h3>
						<ul class="toc">
							<li><a href="./events_live.html">DOM / jQuery events</a></li>
							<li><a href="./dt_events.html">DataTables events</a></li>
							<li><a href="./column_render.html">Column rendering</a></li>
							<li><a href="./length_menu.html">Page length options</a></li>
							<li><a href="./dom_multiple_elements.html">Multiple table control elements</a></li>
							<li><a href="./complex_header.html">Complex headers (rowspan / colspan)</a></li>
							<li><a href="./object_dom_read.html">Read HTML to data objects</a></li>
							<li><a href="./html5-data-attributes.html">HTML5 data-* attributes - cell data</a></li>
							<li><a href="./html5-data-options.html">HTML5 data-* attributes - table options</a></li>
							<li><a href="./language_file.html">Language file</a></li>
							<li><a href="./defaults.html">Setting defaults</a></li>
							<li><a href="./row_callback.html">Row created callback</a></li>
							<li><a href="./row_grouping.html">Row grouping</a></li>
							<li><a href="./footer_callback.html">Footer callback</a></li>
							<li><a href="./dom_toolbar.html">Custom toolbar elements</a></li>
							<li><a href="./sort_direction_control.html">Order direction sequence control</a></li>
						</ul>
					</div>
				</div>

				<div class="epilogue">
					<p>Please refer to the <a href="http://www.datatables.net">DataTables documentation</a> for full information about its API properties and methods.<br>
					Additionally, there are a wide range of <a href="http://www.datatables.net/extras">extras</a> and <a href="http://www.datatables.net/plug-ins">plug-ins</a>
					which extend the capabilities of DataTables.</p>

					<p class="copyright">DataTables designed and created by <a href="http://www.sprymedia.co.uk">SpryMedia Ltd</a> &#169; 2007-2015<br>
					DataTables is licensed under the <a href="http://www.datatables.net/mit">MIT license</a>.</p>
				</div>
			</div>
		</div>
	</section>
</body>
</html>