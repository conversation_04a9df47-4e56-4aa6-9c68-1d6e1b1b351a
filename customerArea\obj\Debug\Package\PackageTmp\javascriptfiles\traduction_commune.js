﻿var arrTermsTranslateLoaded = [];
var arrTermsTranslateDefault = [];


$(document).ready(function() {
    /*
        $.each($('*[data-trad]'), function (indx, item) {
            $(item).html(ReadXmlTranslate($(item).data('trad')));
        });

        //traduit les titres pour les modals
        $.each($('*[data-title]'), function (indx, item) {
            $(item).data('title', ReadXmlTranslate($(item).data('title')));
            $(item).attr('data-title', ReadXmlTranslate($(item).data('title')));
        });*/
});

var isLoaded = false;

function LaunchTraduction() {

    if (!isLoaded) {
        Translate(false);
        Translate(true);
        isLoaded = true;
        console.log("load Translate in array");
    }

    $.each($('*[data-trad]'), function(indx, item) {
        //ReadXmlTranslate($(item).data('trad'));
        if (ReadXmlTranslate($(item).data('trad')) != '' && ReadXmlTranslate($(item).data('trad')) != undefined) {
            $(item).html(ReadXmlTranslate($(item).data('trad')));
        }

    });

    $.each($('*[data-title]'), function(indx, item) {
        $(item).attr('title', ReadXmlTranslate($(item).data('title')));
       // $(item).data('title', ReadXmlTranslate($(item).data('title')));
       // $(item).attr('data-title', ReadXmlTranslate($(item).data('title')));
    });
    $('[data-pageihm-mandatory="true"]').attr('required', 'required');

    $.each($('*[data-tradplaceholder]'), function(indx, item) {
        var itemRequired = ""
        if ($(item).attr('data-pageihm-mandatory') == 'false' || !$(item).attr('required')) {
            if (ReadXmlTranslate($(item).data('tradplaceholder')) != '' && ReadXmlTranslate($(item).data('tradplaceholder')) != undefined) {
                itemRequired = '(' + ReadXmlTranslate('lbl_optionnal') + ')';
            } else {
                itemRequired = "(Optionnal)"
            }
        } else {
            itemRequired = ""
        }
        if (ReadXmlTranslate($(item).data('tradplaceholder')) != '' && ReadXmlTranslate($(item).data('tradplaceholder')) != undefined) {
            $(item).attr('placeholder', ReadXmlTranslate($(item).data('tradplaceholder')) + ' ' + itemRequired);
        } else {
            $(item).attr('placeholder', $(item).attr('oldplaceholder') + ' ' + itemRequired);
        }


    });
}


function Translate(isDefault) {


    var eventid = 0;
    if ($("head").attr('eventid') != undefined)
        eventid = $("head").attr('eventid');

    var paid = 0;
    if ($("head").attr('paid') != undefined)
        paid = $("head").attr('paid');


    var sData = JSON.stringify({
        structureId: $("head").attr('structureid'),
        langCode: $('html').attr('lang'),
        eventId: eventid,
        profilAcheteurId: paid,
        isDefault: isDefault
    });

    $.ajax({
        type: "POST",
        url: "commons.asmx/Translate",
        async: false,
        contentType: "application/json; charset=utf-8", //ne fonctionne pas en local ?

        // contentType: 'xml',
        /* data: {
            structureId: $("head").attr('structureid'),
            langCode: $('html').attr('lang'),
            eventId: eventid,
            profilAcheteurId: paid,
            isDefault: isDefault
       },*/

        data: sData,
        beforeSend: function() {
            // show loading indicator
        },
        success: function(retour) {

            // if (retour.documentElement.textContent != "") { en local il faut utilisé retour.documentElement.textContent
            if (retour.d != "") {
                var x2js = new X2JS();
                var translateTermsJson = convertXml2JSon(x2js, retour.d);
                if (isDefault) {
                    if (translateTermsJson.Root == undefined)
                        arrTermsTranslateDefault.push(translateTermsJson.root)
                    else
                        arrTermsTranslateDefault.push(translateTermsJson.Root)
                } else {
                    if (translateTermsJson.Root == undefined)
                        arrTermsTranslateLoaded.push(translateTermsJson.root)
                    else
                        arrTermsTranslateLoaded.push(translateTermsJson.Root)
                }
            }
        },
        error: function(XMLHttpRequest, textStatus, errorThrown) {
            alert(textStatus);
        },
        complete: function() {
            //  waitingDialog.hide();
        }
    });

}


function convertXml2JSon(x2js, xml) {
    return JSON.parse(JSON.stringify(x2js.xml_str2json(xml)))
}

function ReadXmlTranslate(searchTerm) {

    var result = "";

    if (arrTermsTranslateLoaded.length > 0) {
        result = $.map(arrTermsTranslateLoaded, function(val) {
            return val[searchTerm];
        });
    }

    if (result.length == 0) {

        if (arrTermsTranslateDefault.length > 0) {
            result = $.map(arrTermsTranslateDefault, function(val) {
                return val[searchTerm];
            });
        }
    }

    if (result.length == 0) {
        console.log("le term trad " + searchTerm + " n'existe pas dans le tableau arrTermsTranslate - fichier chargé : ");

        return "";
    }

    if (result[0].__cdata != undefined) {
        return result[0].__cdata;
    } else {
        return result[0].__text;
    }

}