<%@ Page Language="C#" AutoEventWireup="true" %>

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title>Test des différents styles de boutons PayPal</title>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.1.0/css/bootstrap.min.css">
    <script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
    <script src="https://www.paypalobjects.com/js/external/api.js"></script>
</head>
<body>
    <form id="form1" runat="server">
        <div class="container mt-5">
            <div class="row">
                <div class="col-md-8 offset-md-2">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h3>🎨 Test des styles de boutons PayPal</h3>
                        </div>
                        <div class="card-body">
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <h5>1. Bouton CWP + Rectangle + Medium</h5>
                                    <div id="paypal-btn-1" class="mb-3 border p-2"></div>
                                </div>
                                <div class="col-md-6">
                                    <h5>2. Bouton LWP + Pill + Large</h5>
                                    <div id="paypal-btn-2" class="mb-3 border p-2"></div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <h5>3. Bouton CWP + Pill + Small</h5>
                                    <div id="paypal-btn-3" class="mb-3 border p-2"></div>
                                </div>
                                <div class="col-md-6">
                                    <h5>4. Bouton LWP + Rectangle + Small</h5>
                                    <div id="paypal-btn-4" class="mb-3 border p-2"></div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-12">
                                    <h5>5. Bouton personnalisé (HTML simple)</h5>
                                    <button type="button" class="btn btn-primary btn-block" onclick="loginWithPayPal()">
                                        <i class="fab fa-paypal"></i> Se connecter avec PayPal
                                    </button>
                                </div>
                            </div>

                            <div class="alert alert-info mt-3">
                                <strong>ℹ️ Info :</strong> Cliquez sur les boutons pour voir lequel vous préférez !
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>

    <script>
        var clientId = "AZKOsIycBytFxeUfRdiRDtI6rmqT8m96xaw4KBymF9hk44HjSw_ubrQwdI1QZz_-RijZvaIQKLAq93Jw";
        var returnUrl = "https://dev.themisweb.fr/customer/_loginPayPal.aspx?idstructure=0426";

        $(document).ready(function() {
            // Bouton 1: CWP + Rectangle + Medium
            paypal.use(['login'], function (login) {
                login.render({
                    "appid": clientId,
                    "scopes": "openid",
                    "containerid": "paypal-btn-1",
                    "responseType": "code",
                    "locale": "fr-fr",
                    "buttonType": "CWP",
                    "buttonShape": "rectangle",
                    "buttonSize": "medium",
                    "fullPage": "true",
                    "returnurl": returnUrl,
                    "state": "test=1"
                });
            });

            // Bouton 2: LWP + Pill + Large
            paypal.use(['login'], function (login) {
                login.render({
                    "appid": clientId,
                    "scopes": "openid",
                    "containerid": "paypal-btn-2",
                    "responseType": "code",
                    "locale": "fr-fr",
                    "buttonType": "LWP",
                    "buttonShape": "pill",
                    "buttonSize": "lg",
                    "fullPage": "true",
                    "returnurl": returnUrl,
                    "state": "test=2"
                });
            });

            // Bouton 3: CWP + Pill + Small
            paypal.use(['login'], function (login) {
                login.render({
                    "appid": clientId,
                    "scopes": "openid",
                    "containerid": "paypal-btn-3",
                    "responseType": "code",
                    "locale": "fr-fr",
                    "buttonType": "CWP",
                    "buttonShape": "pill",
                    "buttonSize": "sm",
                    "fullPage": "true",
                    "returnurl": returnUrl,
                    "state": "test=3"
                });
            });

            // Bouton 4: LWP + Rectangle + Small
            paypal.use(['login'], function (login) {
                login.render({
                    "appid": clientId,
                    "scopes": "openid",
                    "containerid": "paypal-btn-4",
                    "responseType": "code",
                    "locale": "fr-fr",
                    "buttonType": "LWP",
                    "buttonShape": "rectangle",
                    "buttonSize": "sm",
                    "fullPage": "true",
                    "returnurl": returnUrl,
                    "state": "test=4"
                });
            });
        });

        // Fonction pour le bouton personnalisé
        function loginWithPayPal() {
            var paypalUrl = "https://www.sandbox.paypal.com/signin/authorize?" +
                "flowEntry=static&" +
                "client_id=" + clientId + "&" +
                "response_type=code&" +
                "scope=openid&" +
                "redirect_uri=" + encodeURIComponent(returnUrl) + "&" +
                "state=custom=true";
            
            window.location.href = paypalUrl;
        }
    </script>
</body>
</html>
