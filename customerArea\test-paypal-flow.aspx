<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="test-paypal-flow.aspx.cs" Inherits="customerArea.test_paypal_flow" %>

<!DOCTYPE html>
<html>
<head>
    <title>Test PayPal Flow</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .button { background: #0070ba; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px 0; }
        .info { background: #e6f3ff; padding: 10px; margin: 10px 0; border-left: 4px solid #0070ba; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Test du flux PayPal</h1>
    
    <div class="info">
        <h3>1. URL PayPal générée :</h3>
        <pre id="paypal-url"></pre>
        <a id="paypal-link" href="#" class="button">🔗 Se connecter avec PayPal</a>
    </div>
    
    <div class="info">
        <h3>2. Configuration actuelle :</h3>
        <pre id="config-info"></pre>
    </div>
    
    <div class="info">
        <h3>3. Test direct de _loginPayPal.aspx :</h3>
        <a href="_loginPayPal.aspx?code=TEST_CODE&state=idstructure=0991" class="button">🧪 Tester _loginPayPal.aspx directement</a>
    </div>
</body>
</html>
