<!DOCTYPE html>
<html>
<head>
<title>Overflow Test</title>
<style>

body {
  text-align: center
}

</style>
<!--[if IE]><script type="text/javascript" src="../excanvas.js"></script><![endif]-->
<script>

window.onload = function() {
  var ctx = document.getElementById('c').getContext('2d');
	
  ctx.fillStyle = 'blue';
  ctx.fillRect(-50, -50, 100, 100);
  
  ctx.fillStyle = 'green';
  ctx.fillRect(50, 50, 100, 100);
  
  ctx.strokeStyle = 'black';
  ctx.strokeRect(0, 0, 100, 100);
};

</script>
</head>
<body>

<canvas id=c width=100 height=100></canvas>

<p>This tests if content gets cropped if painting outside the canvas.</p>

</body>
</html>