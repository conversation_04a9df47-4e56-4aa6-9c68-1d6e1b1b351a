/*!
 *  Font Awesome 3.2.1
 *  the iconic font designed for <PERSON><PERSON><PERSON>
 *  ------------------------------------------------------------------------------
 *  The full suite of pictographic icons, examples, and documentation can be
 *  found at http://fontawesome.io.  Stay up to date on Twitter at
 *  http://twitter.com/fontawesome.
 *
 *  License
 *  ------------------------------------------------------------------------------
 *  - The Font Awesome font is licensed under SIL OFL 1.1 -
 *    http://scripts.sil.org/OFL
 *  - Font Awesome CSS, LESS, and SASS files are licensed under MIT License -
 *    http://opensource.org/licenses/mit-license.html
 *  - Font Awesome documentation licensed under CC BY 3.0 -
 *    http://creativecommons.org/licenses/by/3.0/
 *  - Attribution is no longer required in Font Awesome 3.0, but much appreciated:
 *    "Font Awesome by <PERSON>y - http://fontawesome.io"
 *
 *  Author - Dave Gandy
 *  ------------------------------------------------------------------------------
 *  Email: <EMAIL>
 *  Twitter: http://twitter.com/davegandy
 *  Work: Lead Product Designer @ Kyruus - http://kyruus.com
 */

.icon-large {
  font-size: (4em/3);
  margin-top: -4px;
  padding-top: 3px;
  margin-bottom: -4px;
  padding-bottom: 3px;
  vertical-align: middle;
}

.nav {
  [class^="icon-"],
  [class*=" icon-"] {
    vertical-align: inherit;
    margin-top: -4px;
    padding-top: 3px;
    margin-bottom: -4px;
    padding-bottom: 3px;
    &.icon-large {
      vertical-align: -25%;
    }
  }
}

.nav-pills, .nav-tabs {
  [class^="icon-"],
  [class*=" icon-"] {
    &.icon-large {
      line-height: .75em;
      margin-top: -7px;
      padding-top: 5px;
      margin-bottom: -5px;
      padding-bottom: 4px;
    }
  }
}

.btn {
  [class^="icon-"],
  [class*=" icon-"] {
    &.pull-left, &.pull-right { vertical-align: inherit; }
    &.icon-large {
      margin-top: -.5em;
    }
  }
}

a [class^="icon-"],
a [class*=" icon-"] {
  cursor: pointer;
}

@mixin ie7icon($inner) { *zoom: expression(this.runtimeStyle['zoom'] = '1', this.innerHTML = '#{$inner}'); }


.icon-glass {
  @include ie7icon('&#xf000;');
}


.icon-music {
  @include ie7icon('&#xf001;');
}


.icon-search {
  @include ie7icon('&#xf002;');
}


.icon-envelope-alt {
  @include ie7icon('&#xf003;');
}


.icon-heart {
  @include ie7icon('&#xf004;');
}


.icon-star {
  @include ie7icon('&#xf005;');
}


.icon-star-empty {
  @include ie7icon('&#xf006;');
}


.icon-user {
  @include ie7icon('&#xf007;');
}


.icon-film {
  @include ie7icon('&#xf008;');
}


.icon-th-large {
  @include ie7icon('&#xf009;');
}


.icon-th {
  @include ie7icon('&#xf00a;');
}


.icon-th-list {
  @include ie7icon('&#xf00b;');
}


.icon-ok {
  @include ie7icon('&#xf00c;');
}


.icon-remove {
  @include ie7icon('&#xf00d;');
}


.icon-zoom-in {
  @include ie7icon('&#xf00e;');
}


.icon-zoom-out {
  @include ie7icon('&#xf010;');
}


.icon-off {
  @include ie7icon('&#xf011;');
}

.icon-power-off {
  @include ie7icon('&#xf011;');
}


.icon-signal {
  @include ie7icon('&#xf012;');
}


.icon-cog {
  @include ie7icon('&#xf013;');
}

.icon-gear {
  @include ie7icon('&#xf013;');
}


.icon-trash {
  @include ie7icon('&#xf014;');
}


.icon-home {
  @include ie7icon('&#xf015;');
}


.icon-file-alt {
  @include ie7icon('&#xf016;');
}


.icon-time {
  @include ie7icon('&#xf017;');
}


.icon-road {
  @include ie7icon('&#xf018;');
}


.icon-download-alt {
  @include ie7icon('&#xf019;');
}


.icon-download {
  @include ie7icon('&#xf01a;');
}


.icon-upload {
  @include ie7icon('&#xf01b;');
}


.icon-inbox {
  @include ie7icon('&#xf01c;');
}


.icon-play-circle {
  @include ie7icon('&#xf01d;');
}


.icon-repeat {
  @include ie7icon('&#xf01e;');
}

.icon-rotate-right {
  @include ie7icon('&#xf01e;');
}


.icon-refresh {
  @include ie7icon('&#xf021;');
}


.icon-list-alt {
  @include ie7icon('&#xf022;');
}


.icon-lock {
  @include ie7icon('&#xf023;');
}


.icon-flag {
  @include ie7icon('&#xf024;');
}


.icon-headphones {
  @include ie7icon('&#xf025;');
}


.icon-volume-off {
  @include ie7icon('&#xf026;');
}


.icon-volume-down {
  @include ie7icon('&#xf027;');
}


.icon-volume-up {
  @include ie7icon('&#xf028;');
}


.icon-qrcode {
  @include ie7icon('&#xf029;');
}


.icon-barcode {
  @include ie7icon('&#xf02a;');
}


.icon-tag {
  @include ie7icon('&#xf02b;');
}


.icon-tags {
  @include ie7icon('&#xf02c;');
}


.icon-book {
  @include ie7icon('&#xf02d;');
}


.icon-bookmark {
  @include ie7icon('&#xf02e;');
}


.icon-print {
  @include ie7icon('&#xf02f;');
}


.icon-camera {
  @include ie7icon('&#xf030;');
}


.icon-font {
  @include ie7icon('&#xf031;');
}


.icon-bold {
  @include ie7icon('&#xf032;');
}


.icon-italic {
  @include ie7icon('&#xf033;');
}


.icon-text-height {
  @include ie7icon('&#xf034;');
}


.icon-text-width {
  @include ie7icon('&#xf035;');
}


.icon-align-left {
  @include ie7icon('&#xf036;');
}


.icon-align-center {
  @include ie7icon('&#xf037;');
}


.icon-align-right {
  @include ie7icon('&#xf038;');
}


.icon-align-justify {
  @include ie7icon('&#xf039;');
}


.icon-list {
  @include ie7icon('&#xf03a;');
}


.icon-indent-left {
  @include ie7icon('&#xf03b;');
}


.icon-indent-right {
  @include ie7icon('&#xf03c;');
}


.icon-facetime-video {
  @include ie7icon('&#xf03d;');
}


.icon-picture {
  @include ie7icon('&#xf03e;');
}


.icon-pencil {
  @include ie7icon('&#xf040;');
}


.icon-map-marker {
  @include ie7icon('&#xf041;');
}


.icon-adjust {
  @include ie7icon('&#xf042;');
}


.icon-tint {
  @include ie7icon('&#xf043;');
}


.icon-edit {
  @include ie7icon('&#xf044;');
}


.icon-share {
  @include ie7icon('&#xf045;');
}


.icon-check {
  @include ie7icon('&#xf046;');
}


.icon-move {
  @include ie7icon('&#xf047;');
}


.icon-step-backward {
  @include ie7icon('&#xf048;');
}


.icon-fast-backward {
  @include ie7icon('&#xf049;');
}


.icon-backward {
  @include ie7icon('&#xf04a;');
}


.icon-play {
  @include ie7icon('&#xf04b;');
}


.icon-pause {
  @include ie7icon('&#xf04c;');
}


.icon-stop {
  @include ie7icon('&#xf04d;');
}


.icon-forward {
  @include ie7icon('&#xf04e;');
}


.icon-fast-forward {
  @include ie7icon('&#xf050;');
}


.icon-step-forward {
  @include ie7icon('&#xf051;');
}


.icon-eject {
  @include ie7icon('&#xf052;');
}


.icon-chevron-left {
  @include ie7icon('&#xf053;');
}


.icon-chevron-right {
  @include ie7icon('&#xf054;');
}


.icon-plus-sign {
  @include ie7icon('&#xf055;');
}


.icon-minus-sign {
  @include ie7icon('&#xf056;');
}


.icon-remove-sign {
  @include ie7icon('&#xf057;');
}


.icon-ok-sign {
  @include ie7icon('&#xf058;');
}


.icon-question-sign {
  @include ie7icon('&#xf059;');
}


.icon-info-sign {
  @include ie7icon('&#xf05a;');
}


.icon-screenshot {
  @include ie7icon('&#xf05b;');
}


.icon-remove-circle {
  @include ie7icon('&#xf05c;');
}


.icon-ok-circle {
  @include ie7icon('&#xf05d;');
}


.icon-ban-circle {
  @include ie7icon('&#xf05e;');
}


.icon-arrow-left {
  @include ie7icon('&#xf060;');
}


.icon-arrow-right {
  @include ie7icon('&#xf061;');
}


.icon-arrow-up {
  @include ie7icon('&#xf062;');
}


.icon-arrow-down {
  @include ie7icon('&#xf063;');
}


.icon-share-alt {
  @include ie7icon('&#xf064;');
}

.icon-mail-forward {
  @include ie7icon('&#xf064;');
}


.icon-resize-full {
  @include ie7icon('&#xf065;');
}


.icon-resize-small {
  @include ie7icon('&#xf066;');
}


.icon-plus {
  @include ie7icon('&#xf067;');
}


.icon-minus {
  @include ie7icon('&#xf068;');
}


.icon-asterisk {
  @include ie7icon('&#xf069;');
}


.icon-exclamation-sign {
  @include ie7icon('&#xf06a;');
}


.icon-gift {
  @include ie7icon('&#xf06b;');
}


.icon-leaf {
  @include ie7icon('&#xf06c;');
}


.icon-fire {
  @include ie7icon('&#xf06d;');
}


.icon-eye-open {
  @include ie7icon('&#xf06e;');
}


.icon-eye-close {
  @include ie7icon('&#xf070;');
}


.icon-warning-sign {
  @include ie7icon('&#xf071;');
}


.icon-plane {
  @include ie7icon('&#xf072;');
}


.icon-calendar {
  @include ie7icon('&#xf073;');
}


.icon-random {
  @include ie7icon('&#xf074;');
}


.icon-comment {
  @include ie7icon('&#xf075;');
}


.icon-magnet {
  @include ie7icon('&#xf076;');
}


.icon-chevron-up {
  @include ie7icon('&#xf077;');
}


.icon-chevron-down {
  @include ie7icon('&#xf078;');
}


.icon-retweet {
  @include ie7icon('&#xf079;');
}


.icon-shopping-cart {
  @include ie7icon('&#xf07a;');
}


.icon-folder-close {
  @include ie7icon('&#xf07b;');
}


.icon-folder-open {
  @include ie7icon('&#xf07c;');
}


.icon-resize-vertical {
  @include ie7icon('&#xf07d;');
}


.icon-resize-horizontal {
  @include ie7icon('&#xf07e;');
}


.icon-bar-chart {
  @include ie7icon('&#xf080;');
}


.icon-twitter-sign {
  @include ie7icon('&#xf081;');
}


.icon-facebook-sign {
  @include ie7icon('&#xf082;');
}


.icon-camera-retro {
  @include ie7icon('&#xf083;');
}


.icon-key {
  @include ie7icon('&#xf084;');
}


.icon-cogs {
  @include ie7icon('&#xf085;');
}

.icon-gears {
  @include ie7icon('&#xf085;');
}


.icon-comments {
  @include ie7icon('&#xf086;');
}


.icon-thumbs-up-alt {
  @include ie7icon('&#xf087;');
}


.icon-thumbs-down-alt {
  @include ie7icon('&#xf088;');
}


.icon-star-half {
  @include ie7icon('&#xf089;');
}


.icon-heart-empty {
  @include ie7icon('&#xf08a;');
}


.icon-signout {
  @include ie7icon('&#xf08b;');
}


.icon-linkedin-sign {
  @include ie7icon('&#xf08c;');
}


.icon-pushpin {
  @include ie7icon('&#xf08d;');
}


.icon-external-link {
  @include ie7icon('&#xf08e;');
}


.icon-signin {
  @include ie7icon('&#xf090;');
}


.icon-trophy {
  @include ie7icon('&#xf091;');
}


.icon-github-sign {
  @include ie7icon('&#xf092;');
}


.icon-upload-alt {
  @include ie7icon('&#xf093;');
}


.icon-lemon {
  @include ie7icon('&#xf094;');
}


.icon-phone {
  @include ie7icon('&#xf095;');
}


.icon-check-empty {
  @include ie7icon('&#xf096;');
}

.icon-unchecked {
  @include ie7icon('&#xf096;');
}


.icon-bookmark-empty {
  @include ie7icon('&#xf097;');
}


.icon-phone-sign {
  @include ie7icon('&#xf098;');
}


.icon-twitter {
  @include ie7icon('&#xf099;');
}


.icon-facebook {
  @include ie7icon('&#xf09a;');
}


.icon-github {
  @include ie7icon('&#xf09b;');
}


.icon-unlock {
  @include ie7icon('&#xf09c;');
}


.icon-credit-card {
  @include ie7icon('&#xf09d;');
}


.icon-rss {
  @include ie7icon('&#xf09e;');
}


.icon-hdd {
  @include ie7icon('&#xf0a0;');
}


.icon-bullhorn {
  @include ie7icon('&#xf0a1;');
}


.icon-bell {
  @include ie7icon('&#xf0a2;');
}


.icon-certificate {
  @include ie7icon('&#xf0a3;');
}


.icon-hand-right {
  @include ie7icon('&#xf0a4;');
}


.icon-hand-left {
  @include ie7icon('&#xf0a5;');
}


.icon-hand-up {
  @include ie7icon('&#xf0a6;');
}


.icon-hand-down {
  @include ie7icon('&#xf0a7;');
}


.icon-circle-arrow-left {
  @include ie7icon('&#xf0a8;');
}


.icon-circle-arrow-right {
  @include ie7icon('&#xf0a9;');
}


.icon-circle-arrow-up {
  @include ie7icon('&#xf0aa;');
}


.icon-circle-arrow-down {
  @include ie7icon('&#xf0ab;');
}


.icon-globe {
  @include ie7icon('&#xf0ac;');
}


.icon-wrench {
  @include ie7icon('&#xf0ad;');
}


.icon-tasks {
  @include ie7icon('&#xf0ae;');
}


.icon-filter {
  @include ie7icon('&#xf0b0;');
}


.icon-briefcase {
  @include ie7icon('&#xf0b1;');
}


.icon-fullscreen {
  @include ie7icon('&#xf0b2;');
}


.icon-group {
  @include ie7icon('&#xf0c0;');
}


.icon-link {
  @include ie7icon('&#xf0c1;');
}


.icon-cloud {
  @include ie7icon('&#xf0c2;');
}


.icon-beaker {
  @include ie7icon('&#xf0c3;');
}


.icon-cut {
  @include ie7icon('&#xf0c4;');
}


.icon-copy {
  @include ie7icon('&#xf0c5;');
}


.icon-paper-clip {
  @include ie7icon('&#xf0c6;');
}

.icon-paperclip {
  @include ie7icon('&#xf0c6;');
}


.icon-save {
  @include ie7icon('&#xf0c7;');
}


.icon-sign-blank {
  @include ie7icon('&#xf0c8;');
}


.icon-reorder {
  @include ie7icon('&#xf0c9;');
}


.icon-list-ul {
  @include ie7icon('&#xf0ca;');
}


.icon-list-ol {
  @include ie7icon('&#xf0cb;');
}


.icon-strikethrough {
  @include ie7icon('&#xf0cc;');
}


.icon-underline {
  @include ie7icon('&#xf0cd;');
}


.icon-table {
  @include ie7icon('&#xf0ce;');
}


.icon-magic {
  @include ie7icon('&#xf0d0;');
}


.icon-truck {
  @include ie7icon('&#xf0d1;');
}


.icon-pinterest {
  @include ie7icon('&#xf0d2;');
}


.icon-pinterest-sign {
  @include ie7icon('&#xf0d3;');
}


.icon-google-plus-sign {
  @include ie7icon('&#xf0d4;');
}


.icon-google-plus {
  @include ie7icon('&#xf0d5;');
}


.icon-money {
  @include ie7icon('&#xf0d6;');
}


.icon-caret-down {
  @include ie7icon('&#xf0d7;');
}


.icon-caret-up {
  @include ie7icon('&#xf0d8;');
}


.icon-caret-left {
  @include ie7icon('&#xf0d9;');
}


.icon-caret-right {
  @include ie7icon('&#xf0da;');
}


.icon-columns {
  @include ie7icon('&#xf0db;');
}


.icon-sort {
  @include ie7icon('&#xf0dc;');
}


.icon-sort-down {
  @include ie7icon('&#xf0dd;');
}


.icon-sort-up {
  @include ie7icon('&#xf0de;');
}


.icon-envelope {
  @include ie7icon('&#xf0e0;');
}


.icon-linkedin {
  @include ie7icon('&#xf0e1;');
}


.icon-undo {
  @include ie7icon('&#xf0e2;');
}

.icon-rotate-left {
  @include ie7icon('&#xf0e2;');
}


.icon-legal {
  @include ie7icon('&#xf0e3;');
}


.icon-dashboard {
  @include ie7icon('&#xf0e4;');
}


.icon-comment-alt {
  @include ie7icon('&#xf0e5;');
}


.icon-comments-alt {
  @include ie7icon('&#xf0e6;');
}


.icon-bolt {
  @include ie7icon('&#xf0e7;');
}


.icon-sitemap {
  @include ie7icon('&#xf0e8;');
}


.icon-umbrella {
  @include ie7icon('&#xf0e9;');
}


.icon-paste {
  @include ie7icon('&#xf0ea;');
}


.icon-lightbulb {
  @include ie7icon('&#xf0eb;');
}


.icon-exchange {
  @include ie7icon('&#xf0ec;');
}


.icon-cloud-download {
  @include ie7icon('&#xf0ed;');
}


.icon-cloud-upload {
  @include ie7icon('&#xf0ee;');
}


.icon-user-md {
  @include ie7icon('&#xf0f0;');
}


.icon-stethoscope {
  @include ie7icon('&#xf0f1;');
}


.icon-suitcase {
  @include ie7icon('&#xf0f2;');
}


.icon-bell-alt {
  @include ie7icon('&#xf0f3;');
}


.icon-coffee {
  @include ie7icon('&#xf0f4;');
}


.icon-food {
  @include ie7icon('&#xf0f5;');
}


.icon-file-text-alt {
  @include ie7icon('&#xf0f6;');
}


.icon-building {
  @include ie7icon('&#xf0f7;');
}


.icon-hospital {
  @include ie7icon('&#xf0f8;');
}


.icon-ambulance {
  @include ie7icon('&#xf0f9;');
}


.icon-medkit {
  @include ie7icon('&#xf0fa;');
}


.icon-fighter-jet {
  @include ie7icon('&#xf0fb;');
}


.icon-beer {
  @include ie7icon('&#xf0fc;');
}


.icon-h-sign {
  @include ie7icon('&#xf0fd;');
}


.icon-plus-sign-alt {
  @include ie7icon('&#xf0fe;');
}


.icon-double-angle-left {
  @include ie7icon('&#xf100;');
}


.icon-double-angle-right {
  @include ie7icon('&#xf101;');
}


.icon-double-angle-up {
  @include ie7icon('&#xf102;');
}


.icon-double-angle-down {
  @include ie7icon('&#xf103;');
}


.icon-angle-left {
  @include ie7icon('&#xf104;');
}


.icon-angle-right {
  @include ie7icon('&#xf105;');
}


.icon-angle-up {
  @include ie7icon('&#xf106;');
}


.icon-angle-down {
  @include ie7icon('&#xf107;');
}


.icon-desktop {
  @include ie7icon('&#xf108;');
}


.icon-laptop {
  @include ie7icon('&#xf109;');
}


.icon-tablet {
  @include ie7icon('&#xf10a;');
}


.icon-mobile-phone {
  @include ie7icon('&#xf10b;');
}


.icon-circle-blank {
  @include ie7icon('&#xf10c;');
}


.icon-quote-left {
  @include ie7icon('&#xf10d;');
}


.icon-quote-right {
  @include ie7icon('&#xf10e;');
}


.icon-spinner {
  @include ie7icon('&#xf110;');
}


.icon-circle {
  @include ie7icon('&#xf111;');
}


.icon-reply {
  @include ie7icon('&#xf112;');
}

.icon-mail-reply {
  @include ie7icon('&#xf112;');
}


.icon-github-alt {
  @include ie7icon('&#xf113;');
}


.icon-folder-close-alt {
  @include ie7icon('&#xf114;');
}


.icon-folder-open-alt {
  @include ie7icon('&#xf115;');
}


.icon-expand-alt {
  @include ie7icon('&#xf116;');
}


.icon-collapse-alt {
  @include ie7icon('&#xf117;');
}


.icon-smile {
  @include ie7icon('&#xf118;');
}


.icon-frown {
  @include ie7icon('&#xf119;');
}


.icon-meh {
  @include ie7icon('&#xf11a;');
}


.icon-gamepad {
  @include ie7icon('&#xf11b;');
}


.icon-keyboard {
  @include ie7icon('&#xf11c;');
}


.icon-flag-alt {
  @include ie7icon('&#xf11d;');
}


.icon-flag-checkered {
  @include ie7icon('&#xf11e;');
}


.icon-terminal {
  @include ie7icon('&#xf120;');
}


.icon-code {
  @include ie7icon('&#xf121;');
}


.icon-reply-all {
  @include ie7icon('&#xf122;');
}


.icon-mail-reply-all {
  @include ie7icon('&#xf122;');
}


.icon-star-half-empty {
  @include ie7icon('&#xf123;');
}

.icon-star-half-full {
  @include ie7icon('&#xf123;');
}


.icon-location-arrow {
  @include ie7icon('&#xf124;');
}


.icon-crop {
  @include ie7icon('&#xf125;');
}


.icon-code-fork {
  @include ie7icon('&#xf126;');
}


.icon-unlink {
  @include ie7icon('&#xf127;');
}


.icon-question {
  @include ie7icon('&#xf128;');
}


.icon-info {
  @include ie7icon('&#xf129;');
}


.icon-exclamation {
  @include ie7icon('&#xf12a;');
}


.icon-superscript {
  @include ie7icon('&#xf12b;');
}


.icon-subscript {
  @include ie7icon('&#xf12c;');
}


.icon-eraser {
  @include ie7icon('&#xf12d;');
}


.icon-puzzle-piece {
  @include ie7icon('&#xf12e;');
}


.icon-microphone {
  @include ie7icon('&#xf130;');
}


.icon-microphone-off {
  @include ie7icon('&#xf131;');
}


.icon-shield {
  @include ie7icon('&#xf132;');
}


.icon-calendar-empty {
  @include ie7icon('&#xf133;');
}


.icon-fire-extinguisher {
  @include ie7icon('&#xf134;');
}


.icon-rocket {
  @include ie7icon('&#xf135;');
}


.icon-maxcdn {
  @include ie7icon('&#xf136;');
}


.icon-chevron-sign-left {
  @include ie7icon('&#xf137;');
}


.icon-chevron-sign-right {
  @include ie7icon('&#xf138;');
}


.icon-chevron-sign-up {
  @include ie7icon('&#xf139;');
}


.icon-chevron-sign-down {
  @include ie7icon('&#xf13a;');
}


.icon-html5 {
  @include ie7icon('&#xf13b;');
}


.icon-css3 {
  @include ie7icon('&#xf13c;');
}


.icon-anchor {
  @include ie7icon('&#xf13d;');
}


.icon-unlock-alt {
  @include ie7icon('&#xf13e;');
}


.icon-bullseye {
  @include ie7icon('&#xf140;');
}


.icon-ellipsis-horizontal {
  @include ie7icon('&#xf141;');
}


.icon-ellipsis-vertical {
  @include ie7icon('&#xf142;');
}


.icon-rss-sign {
  @include ie7icon('&#xf143;');
}


.icon-play-sign {
  @include ie7icon('&#xf144;');
}


.icon-ticket {
  @include ie7icon('&#xf145;');
}


.icon-minus-sign-alt {
  @include ie7icon('&#xf146;');
}


.icon-check-minus {
  @include ie7icon('&#xf147;');
}


.icon-level-up {
  @include ie7icon('&#xf148;');
}


.icon-level-down {
  @include ie7icon('&#xf149;');
}


.icon-check-sign {
  @include ie7icon('&#xf14a;');
}


.icon-edit-sign {
  @include ie7icon('&#xf14b;');
}


.icon-external-link-sign {
  @include ie7icon('&#xf14c;');
}


.icon-share-sign {
  @include ie7icon('&#xf14d;');
}


.icon-compass {
  @include ie7icon('&#xf14e;');
}


.icon-collapse {
  @include ie7icon('&#xf150;');
}


.icon-collapse-top {
  @include ie7icon('&#xf151;');
}


.icon-expand {
  @include ie7icon('&#xf152;');
}


.icon-eur {
  @include ie7icon('&#xf153;');
}

.icon-euro {
  @include ie7icon('&#xf153;');
}


.icon-gbp {
  @include ie7icon('&#xf154;');
}


.icon-usd {
  @include ie7icon('&#xf155;');
}

.icon-dollar {
  @include ie7icon('&#xf155;');
}


.icon-inr {
  @include ie7icon('&#xf156;');
}

.icon-rupee {
  @include ie7icon('&#xf156;');
}


.icon-jpy {
  @include ie7icon('&#xf157;');
}

.icon-yen {
  @include ie7icon('&#xf157;');
}


.icon-cny {
  @include ie7icon('&#xf158;');
}

.icon-renminbi {
  @include ie7icon('&#xf158;');
}


.icon-krw {
  @include ie7icon('&#xf159;');
}

.icon-won {
  @include ie7icon('&#xf159;');
}


.icon-btc {
  @include ie7icon('&#xf15a;');
}

.icon-bitcoin {
  @include ie7icon('&#xf15a;');
}


.icon-file {
  @include ie7icon('&#xf15b;');
}


.icon-file-text {
  @include ie7icon('&#xf15c;');
}


.icon-sort-by-alphabet {
  @include ie7icon('&#xf15d;');
}


.icon-sort-by-alphabet-alt {
  @include ie7icon('&#xf15e;');
}


.icon-sort-by-attributes {
  @include ie7icon('&#xf160;');
}


.icon-sort-by-attributes-alt {
  @include ie7icon('&#xf161;');
}


.icon-sort-by-order {
  @include ie7icon('&#xf162;');
}


.icon-sort-by-order-alt {
  @include ie7icon('&#xf163;');
}


.icon-thumbs-up {
  @include ie7icon('&#xf164;');
}


.icon-thumbs-down {
  @include ie7icon('&#xf165;');
}


.icon-youtube-sign {
  @include ie7icon('&#xf166;');
}


.icon-youtube {
  @include ie7icon('&#xf167;');
}


.icon-xing {
  @include ie7icon('&#xf168;');
}


.icon-xing-sign {
  @include ie7icon('&#xf169;');
}


.icon-youtube-play {
  @include ie7icon('&#xf16a;');
}


.icon-dropbox {
  @include ie7icon('&#xf16b;');
}


.icon-stackexchange {
  @include ie7icon('&#xf16c;');
}


.icon-instagram {
  @include ie7icon('&#xf16d;');
}


.icon-flickr {
  @include ie7icon('&#xf16e;');
}


.icon-adn {
  @include ie7icon('&#xf170;');
}


.icon-bitbucket {
  @include ie7icon('&#xf171;');
}


.icon-bitbucket-sign {
  @include ie7icon('&#xf172;');
}


.icon-tumblr {
  @include ie7icon('&#xf173;');
}


.icon-tumblr-sign {
  @include ie7icon('&#xf174;');
}


.icon-long-arrow-down {
  @include ie7icon('&#xf175;');
}


.icon-long-arrow-up {
  @include ie7icon('&#xf176;');
}


.icon-long-arrow-left {
  @include ie7icon('&#xf177;');
}


.icon-long-arrow-right {
  @include ie7icon('&#xf178;');
}


.icon-apple {
  @include ie7icon('&#xf179;');
}


.icon-windows {
  @include ie7icon('&#xf17a;');
}


.icon-android {
  @include ie7icon('&#xf17b;');
}


.icon-linux {
  @include ie7icon('&#xf17c;');
}


.icon-dribbble {
  @include ie7icon('&#xf17d;');
}


.icon-skype {
  @include ie7icon('&#xf17e;');
}


.icon-foursquare {
  @include ie7icon('&#xf180;');
}


.icon-trello {
  @include ie7icon('&#xf181;');
}


.icon-female {
  @include ie7icon('&#xf182;');
}


.icon-male {
  @include ie7icon('&#xf183;');
}


.icon-gittip {
  @include ie7icon('&#xf184;');
}


.icon-sun {
  @include ie7icon('&#xf185;');
}


.icon-moon {
  @include ie7icon('&#xf186;');
}


.icon-archive {
  @include ie7icon('&#xf187;');
}


.icon-bug {
  @include ie7icon('&#xf188;');
}


.icon-vk {
  @include ie7icon('&#xf189;');
}


.icon-weibo {
  @include ie7icon('&#xf18a;');
}


.icon-renren {
  @include ie7icon('&#xf18b;');
}


