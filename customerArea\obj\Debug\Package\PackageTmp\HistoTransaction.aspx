﻿<%@ Page Title="" Language="C#" MasterPageFile="~/pagemaster.Master" AutoEventWireup="true" CodeBehind="HistoTransaction.aspx.cs" Inherits="customerArea.HistoTransaction" %>

<asp:Content ID="Content1" ContentPlaceHolderID="ctPlaceHold" runat="server">


    <div class="container">

        <h1 data-trad="title_first_histo_transactions">
            <center>histotransaction</center>
        </h1>


        <div class="col-lg-12">

            <div id="commentsPageHaut" class="row">
                <asp:Literal ID="litForCommentaireHaut" runat="server"></asp:Literal>
            </div>

        </div>

        <div class="col-lg-12">
            <%--a garder pour avoir les commandes réservées de l'utilisateur connecté--%>
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5><span data-trad="title_my_orders"></span></h5>
                    <div class="ibox-tools">
                        <a class="collapse-link">
                            <i class="fa fa-chevron-up"></i>
                        </a>

                    </div>
                </div>
                <div class="ibox-content">
                    <%--liste des commandes de l'utilisateur connecté--%>
                    <div id="orders_user_list_wrapper">
                    </div>
                </div>
            </div>
        </div>

        <div id="commentsPageBas" class="row">
            <asp:Literal ID="litForCommentaireBas" runat="server"></asp:Literal>
        </div>

    </div>
</asp:Content>
