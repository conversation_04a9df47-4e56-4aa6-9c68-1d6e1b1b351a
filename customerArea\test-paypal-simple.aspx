<%@ Page Language="C#" AutoEventWireup="true" %>

<!DOCTYPE html>
<html>
<head>
    <title>Test PayPal - Bouton Simple</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.1.0/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>
<body>
    <div class="container mt-5">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h3><i class="fab fa-paypal"></i> Test PayPal - Bouton Simple</h3>
            </div>
            <div class="card-body text-center">
                
                <div class="alert alert-info">
                    <h5><i class="fas fa-info-circle"></i> Configuration</h5>
                    <p><strong>Structure :</strong> 0426</p>
                    <p><strong>Client ID :</strong> AZKOslycByfXeUIRdIRDti6rmqT8m96xaw4KBymF9hk44HjSw_ubIQwdITQZz_-RijZvaIGKLAq93Jw</p>
                    <p><strong>URL de redirection :</strong> https://dev.themisweb.fr/customer/_loginPayPal.aspx?idstructure=0426</p>
                    <p><strong>SDK :</strong> Sandbox</p>
                </div>

                <div class="row justify-content-center">
                    <div class="col-md-6">
                        <h4>Bouton PayPal de connexion</h4>
                        <div id="paypal-login-button" class="mt-3"></div>
                        
                        <div class="mt-4">
                            <button type="button" class="btn btn-secondary" onclick="showDebugInfo()">
                                <i class="fas fa-bug"></i> Voir les détails techniques
                            </button>
                        </div>
                        
                        <div id="debug-info" class="mt-3" style="display: none;">
                            <div class="alert alert-secondary">
                                <h6>Détails techniques :</h6>
                                <p><strong>SDK utilisé :</strong> https://www.sandbox.paypal.com/js/external/api.js</p>
                                <p><strong>Type de bouton :</strong> LWP (Log in with PayPal)</p>
                                <p><strong>Forme :</strong> Rectangle</p>
                                <p><strong>Taille :</strong> Large</p>
                                <p><strong>Scopes :</strong> openid email profile</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="alert alert-success mt-4">
                    <h6><i class="fas fa-check-circle"></i> Ce bouton devrait fonctionner car :</h6>
                    <ul class="text-left">
                        <li>✅ Utilise le SDK Sandbox</li>
                        <li>✅ Client ID correct</li>
                        <li>✅ URL de redirection correcte</li>
                        <li>✅ Structure ID correcte (0426)</li>
                    </ul>
                </div>

            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://www.sandbox.paypal.com/js/external/api.js"></script>
    
    <script>
        // Configuration PayPal
        var clientId = "AZKOslycByfXeUIRdIRDti6rmqT8m96xaw4KBymF9hk44HjSw_ubIQwdITQZz_-RijZvaIGKLAq93Jw";
        var returnUrl = "https://dev.themisweb.fr/customer/_loginPayPal.aspx?idstructure=0426";
        var structureId = "0426";

        $(document).ready(function() {
            console.log('Initialisation du bouton PayPal...');
            console.log('Client ID:', clientId.substring(0, 10) + '...');
            console.log('Return URL:', returnUrl);
            
            // Créer le bouton PayPal
            paypal.use(['login'], function (login) {
                login.render({
                    "appid": clientId,
                    "scopes": "openid email profile",
                    "containerid": "paypal-login-button",
                    "responseType": "code",
                    "locale": "fr-fr",
                    "buttonType": "LWP",
                    "buttonShape": "rectangle",
                    "buttonSize": "lg",
                    "fullPage": "true",
                    "returnurl": returnUrl,
                    "state": "idstructure=" + structureId
                });
            });
            
            console.log('Bouton PayPal créé !');
        });

        function showDebugInfo() {
            $('#debug-info').toggle();
        }
    </script>
</body>
</html>
