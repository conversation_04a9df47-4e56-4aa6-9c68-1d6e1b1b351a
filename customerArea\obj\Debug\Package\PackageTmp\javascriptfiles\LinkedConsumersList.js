﻿$(document).ready(function () {


    GetListLinkConsumer();
    // validation add new consumer
    $('#btnValiderConsumer').click(function (e) {
        e.preventDefault();
        e.stopPropagation();
        var curForm = $("#attachConsumerForm");
        if (validForm(curForm)) {
            SaveConsumer();
        } else {
            showAlertError('', ReadXmlTranslate("msg_error_update"), '')
        }


    });
    // validation attach an existing consumer
    $('#btnValiderConsumerExisting').click(function (e) {
        e.preventDefault();
        e.stopPropagation();
        var curForm = $("#attachConsumerExistingForm");
        if (validForm(curForm)) {
            SaveExistingConsumer();
        } else {
            showAlertError('', ReadXmlTranslate("msg_error_update"), '')
        }


    });

    $('#attachInputPhonenumber').intlTelInput("setCountry", getLangForPhones());
    $('#attachInputFaxnumber').intlTelInput("setCountry", getLangForPhones());
    $('#attachInputMobilenumber').intlTelInput("setCountry", getLangForPhones());

    //resize l'iframe parent
    resizeParentIframe();

});


function SaveConsumer() {

    var ctrlh = $('#myhead').attr('ctrlh');

    var CustomerEntity = {};
    CustomerEntity.CiviliteId = ($('#attachSelectCivility').length > 0) ? $('#attachSelectCivility option:selected').val() : "0";

    if ($('#attachInputFirstName').val() == undefined) {
        CustomerEntity.FirstName = "";
    } else {
        if ($('#attachInputFirstName').data('upper') == true) {
            CustomerEntity.FirstName = $('#attachInputFirstName').val().toUpperCase()
        } else {
            CustomerEntity.FirstName = $('#attachInputFirstName').val();
        }
    }

    if ($('#attachInputLastName').val() == undefined) {
        CustomerEntity.SurName = "";
    } else {
        if ($('#attachInputLastName').data('upper') == true) {
            CustomerEntity.SurName = $('#attachInputLastName').val().toUpperCase()
        } else {
            CustomerEntity.SurName = $('#attachInputLastName').val();
        }
    }

    if ($('#attachInputAddress1').val() == undefined) {
        CustomerEntity.Address1 = "";
    } else {
        if ($('#attachInputAddress1').data('upper') == true) {
            CustomerEntity.Address1 = $('#attachInputAddress1').val().toUpperCase()
        } else {
            CustomerEntity.Address1 = $('#attachInputAddress1').val();
        }
    }

    if ($('#attachInputAddress2').val() == undefined) {
        CustomerEntity.Address2 = "";
    } else {
        if ($('#attachInputAddress2').data('upper') == true) {
            CustomerEntity.Address2 = $('#attachInputAddress2').val().toUpperCase()
        } else {
            CustomerEntity.Address2 = $('#attachInputAddress2').val();
        }
    }

    if ($('#attachInputAddress3').val() == undefined) {
        CustomerEntity.Address3 = "";
    } else {
        if ($('#attachInputAddress3').data('upper') == true) {
            CustomerEntity.Address3 = $('#attachInputAddress3').val().toUpperCase()
        } else {
            CustomerEntity.Address3 = $('#attachInputAddress3').val();
        }
    }


    if ($('#attachInputAddress4').val() == undefined) {
        CustomerEntity.Address4 = "";
    } else {
        if ($('#attachInputAddress4').data('upper') == true) {
            CustomerEntity.Address4 = $('#attachInputAddress4').val().toUpperCase()
        } else {
            CustomerEntity.Address4 = $('#attachInputAddress4').val();
        }
    }


    // CustomerEntity.FirstName = ($('#attachInputFirstName').val() == undefined) ? "" : $('#attachInputFirstName').val();
    //CustomerEntity.SurName = ($('#attachInputLastName').val() == undefined) ? "" : $('#attachInputLastName').val();
    CustomerEntity.Email = ($('#attachInputEmail').val() == undefined) ? "" : $('#attachInputEmail').val();
    CustomerEntity.Password = ""
    CustomerEntity.Sex = ""

    //validateDobWithPattern($('#attachInputDob').val(), "true")

    CustomerEntity.DateOfBirthday = ($('#attachInputDob').val() == undefined || $('#attachInputDob').val() == "") ? "01/01/1900" : validateDobWithPattern($('#attachInputDob').val(), "true");


    /*CustomerEntity.Address1 = ($('#attachInputAddress1').val() == undefined) ? "" : $('#attachInputAddress1').val();
    CustomerEntity.Address2 = ($('#attachInputAddress2').val() == undefined) ? "" : $('#attachInputAddress2').val();
    CustomerEntity.Address3 = ($('#attachInputAddress3').val() == undefined) ? "" : $('#attachInputAddress3').val();
    CustomerEntity.Address4 = ($('#attachInputAddress4').val() == undefined) ? "" : $('#attachInputAddress4').val();
   */
    CustomerEntity.PostalCode = ($('#attachInputZipcode').val() == undefined) ? "" : $('#attachInputZipcode').val();
    //CustomerEntity.City = ($('#attachInputCity').val() == undefined) ? "" : $('#attachInputCity').val();
    CustomerEntity.Country = ($('#attachSelectCountry').length == 1) ? $('#attachSelectCountry option:selected').val() : "";
    CustomerEntity.PhoneNumber = ($('#attachInputPhonenumber').val() == undefined) ? "" : $('#attachInputPhonenumber').val();
    CustomerEntity.MobilePhoneNumber = ($('#attachInputMobilenumber').val() == undefined) ? "" : $('#attachInputMobilenumber').val();
    CustomerEntity.FaxPhoneNumber = ($('#attachInputFaxnumber').val() == undefined) ? "" : $('#attachInputFaxnumber').val();
    CustomerEntity.InfosCompChecked = "";
    CustomerEntity.InfosCompNotChecked = "";
    CustomerEntity.Hash = ctrlh;
    //CustomerEntity.Comment = ($('#attachInputComments').val() == undefined) ? "" : $('#attachInputComments').val();
    //CustomerEntity.IdentiteComplement = ($('#attachInputCompany').val() == undefined) ? "" : $('#attachInputCompany').val();


    if ($('#attachInputCity').val() == undefined) {
        CustomerEntity.City = "";
    } else {
        if ($('#attachInputCity').data('upper') == true) {
            CustomerEntity.City = $('#attachInputCity').val().toUpperCase()
        } else {
            CustomerEntity.City = $('#attachInputCity').val();
        }
    }


    if ($('#attachInputComments').val() == undefined) {
        CustomerEntity.Comment = "";
    } else {
        if ($('#attachInputComments').data('upper') == true) {
            CustomerEntity.Comment = $('#attachInputComments').val().toUpperCase()
        } else {
            CustomerEntity.Comment = $('#attachInputComments').val();
        }
    }


    if ($('#attachInputCompany').val() == undefined) {
        CustomerEntity.IdentiteComplement = "";
    } else {
        if ($('#attachInputCompany').data('upper') == true) {
            CustomerEntity.IdentiteComplement = $('#attachInputCompany').val().toUpperCase()
        } else {
            CustomerEntity.IdentiteComplement = $('#attachInputCompany').val();
        }
    }

    var structureid = $('#myhead').attr('structureid');
    var eventid = (!!$("#myhead").attr('eventid')) ? $("#myhead").attr('eventid') : 0;



    var sData = JSON.stringify({
        structureid: structureid,
        eventid: eventid,
        customer: CustomerEntity,
        isexisting: false
    });

    $.ajax({
        type: "POST",
        url: 'Commons.asmx/LinkConsumer',
        data: sData,
        contentType: 'application/json; charset=utf-8',
        dataType: "json",
        success: function (data) {
            if (data.d.split(':')[0] == "error") {
                showAlertError(ReadXmlTranslate(data.d.split(':')[1]), ReadXmlTranslate(data.d.split(':')[2]), '')
            } else {
                showAlertSuccess('', ReadXmlTranslate(data.d.split(':')[2]), '')
            }
            DeleteFormFields("attachConsumerForm");
            GetListLinkConsumer();
            /*$('#modalForMessageBody').html(ReadXmlTranslate(data.d.split(':')[1]));

            $('#modalForMessage').addClass('show').delay(2500).queue(function (next) {
                $(this).removeClass("show").dequeue();
                DeleteFormFields("attachConsumerForm");
                GetListLinkConsumer();
            });*/
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            showAlertError('', XMLHttpRequest.responseJSON + "\r\n" + XMLHttpRequest.responseJSON.StackTrace, '')
        }
    });

}



function SaveExistingConsumer() {


    var ctrlh = $('#myhead').attr('ctrlh');

    var CustomerEntity = {};
    CustomerEntity.Identite_id = ($('input[name="identiteid"]').val() == undefined) ? "" : $('input[name="identiteid"]').val();
    CustomerEntity.Email = ($('input[name="emailexisting"]').val() == undefined) ? "" : $('input[name="emailexisting"]').val();

    var structureid = $('#myhead').attr('structureid');
    var eventid = (!!$("#myhead").attr('eventid')) ? $("#myhead").attr('eventid') : 0;



    var sData = JSON.stringify({
        structureid: structureid,
        eventid: eventid,
        customer: CustomerEntity,
        isexisting: true
    });

    $.ajax({
        type: "POST",
        url: 'Commons.asmx/LinkExistingConsumer',
        data: sData,
        contentType: 'application/json; charset=utf-8',
        dataType: "json",
        success: function (data) {

            if (data.d.split(':')[0] == "error") {
                showAlertError(ReadXmlTranslate(data.d.split(':')[1]), ReadXmlTranslate(data.d.split(':')[2]), '');
            } else {
                showAlertSuccess('', ReadXmlTranslate(data.d.split(':')[2]), '')

            }


            DeleteFormFields("attachConsumerExistingForm");
            GetListLinkConsumer();
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            showAlertError('', XMLHttpRequest.responseJSON + "\r\n" + XMLHttpRequest.responseJSON.StackTrace, '')
        }
    });

}


function GetListLinkConsumer() {

    var structureid = $('#myhead').attr('structureid');
    var lang = getLang();
    var sData = JSON.stringify({
        structureid: structureid,
        langCode: lang
    });


    $.ajax({
        type: "POST",
        url: 'Commons.asmx/GetListLinkConsumer',
        contentType: 'application/json; charset=utf-8',
        dataType: "json",
        data: sData,
        success: function (data) {
            //if(data.d.length > 0) {
            fillDataTableLinkConsumer(data.d);
            //}
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            // ShowError("error", XMLHttpRequest.responseJSON.Message + "<br />" + XMLHttpRequest.responseJSON.StackTrace + "\r\n", "alert alert-danger alert-dismissable", 5000);
            //console.log(XMLHttpRequest.responseJSON + "\r\n" + XMLHttpRequest.responseJSON.StackTrace);
            showAlertError('', XMLHttpRequest.responseJSON + "\r\n" + XMLHttpRequest.responseJSON.StackTrace, '')
        }
    });


}



function fillDataTableLinkConsumer(data) {

    var oTableOrdersList = $('#consumers_list_table').DataTable({
        "bDestroy": true,
        "bProcessing": false,
        "responsive": {
            details: {
                type: 'column',
                target: 0
            }
        },
        "aoColumnDefs": [{
            className: 'control align-middle',
            targets: 0,
            orderable: false
        }],
        //"bSort": true,
        //"bFilter": true,
        "bAutoWidth": true,
        "iDisplayLength": -1,
        "aLengthMenu": [
            [10, 25, 50, 100, -1],
            [10, 25, 50, 100, ReadXmlTranslate('lbl_choose_all_datatable')]
        ],
        "bDeferRender": true,
        "aaData": data,
        "oLanguage": GetLanguageDataTable(),
        "aaSorting": [
            [1, "asc"]
        ],
        "aoColumns": [{
            //nb de places
            "mRender": function (data, type, full) {
                return "";
            }
        }, {
            "mDataProp": "ID",
            "mRender": function (url, type, full) {
                var html = full.Identite_id;
                return html;
            }
        }, {
            "mDataProp": "SurName"
        }, {
            "mDataProp": "FirstName"
        }, {
            "mDataProp": "Email"
        }, {
            "mDataProp": "ID",
            "sClass": "text-center",
            "mRender": function (url, type, full) {
                var html = ""
                if (full.Identite_id == $('head').attr('myidentity')) {
                    html = '<span class="sp-tooltip-delete"><i class="fas fa-lock"></i></span>'
                } else {
                    html = '<span class="sp-tooltip-delete"><a class="delete confirm" href="" data-id="' + full.Identite_id + '" data-toggle="tooltip"><i class="fas fa-trash-alt"></i></a></span>'
                }
                return html;
            }
        }]
    });

    //delete item
    $(".delete.confirm").on('click', function (e) {
        e.preventDefault();
        $("#consumers_list_table .delete.confirm").removeClass('selected');
        $(this).addClass('selected');
        var consumerid = $('.selected').data('id');
        ShowGenericModal(ReadXmlTranslate('title_msg_delete_linkedconsumers'), ReadXmlTranslate('msg_delete_linkedconsumers'), ReadXmlTranslate('btn_delete_my_selection'), ReadXmlTranslate('btn_cancel'), 'confirmDeleteSelection', 'cancelDeleteSelection')
        $('#confirmDeleteSelection').on('click', function (e) {
            DeleteLinkedConsumers(consumerid)
        });
    });
    oTableOrdersList.columns.adjust().draw();
    // adjust collumn responsive when we open the collapse.
    $('#collapseLinkedConsumers3').on('shown.bs.collapse', function () {
        // do something…
        oTableOrdersList.columns.adjust().draw();
    })
}

function DeleteLinkedConsumers(consumerid) {
    var sData = JSON.stringify({
        consumerId: consumerid
    });
    $.ajax({
        type: "POST",
        url: 'Commons.asmx/UnlinkConsumer',
        data: sData,
        contentType: 'application/json; charset=utf-8',
        dataType: "json",
        success: function (data) {
            if (data.d.split(':')[0] == "success") {
                showAlertSuccess('', ReadXmlTranslate(data.d.split(':')[1]), '')
                GetListLinkConsumer()
            } else {
                showAlertError('', ReadXmlTranslate(data.d.split(':')[1]), '')
            }
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            // ShowError("msgNotificationAlert", XMLHttpRequest.responseJSON.Message + "<br />" + XMLHttpRequest.responseJSON.StackTrace, "alert alert-danger alert-dismissable", -1);
            console.log(XMLHttpRequest.responseText + "\r\n" + XMLHttpRequest.responseJSON.StackTrace);

        }
    });
}