<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<link rel="shortcut icon" type="image/ico" href="http://www.datatables.net/favicon.ico">
	<meta name="viewport" content="initial-scale=1.0, maximum-scale=2.0">
	<link rel="stylesheet" type="text/css" href="../../../examples/resources/syntax/shCore.css">
	<link rel="stylesheet" type="text/css" href="../../../examples/resources/demo.css">
	<script type="text/javascript" language="javascript" src="../../../examples/resources/syntax/shCore.js"></script>
	<script type="text/javascript" language="javascript" src="../../../examples/resources/demo.js"></script>

	<title>Responsive examples - Responsive DataTables</title>
</head>

<body class="dt-example">
	<div class="container">
		<section>
			<h1>Responsive example <span>Responsive DataTables</span></h1>

			<div class="info"></div>
		</section>
	</div>

	<section>
		<div class="footer">
			<div class="gradient"></div>

			<div class="liner">
				<div class="toc">
					<div class="toc-group">
						<h3><a href="./initialisation/index.html">Basic initialisation</a></h3>
						<ul class="toc">
							<li><a href="./initialisation/className.html">Class name</a></li>
							<li><a href="./initialisation/option.html">Configuration option</a></li>
							<li><a href="./initialisation/new.html">`new` constructor</a></li>
							<li><a href="./initialisation/ajax.html">Ajax data</a></li>
							<li><a href="./initialisation/default.html">Default initialisation</a></li>
						</ul>
					</div>

					<div class="toc-group">
						<h3><a href="./styling/index.html">Styling</a></h3>
						<ul class="toc">
							<li><a href="./styling/bootstrap.html">Bootstrap styling</a></li>
							<li><a href="./styling/foundation.html">Foundation styling</a></li>
							<li><a href="./styling/scrolling.html">Vertical scrolling</a></li>
							<li><a href="./styling/compact.html">Compact styling</a></li>
						</ul>
					</div>

					<div class="toc-group">
						<h3><a href="./display-control/index.html">Display control</a></h3>
						<ul class="toc">
							<li><a href="./display-control/auto.html">Automatic column hiding</a></li>
							<li><a href="./display-control/classes.html">Class control</a></li>
							<li><a href="./display-control/init-classes.html">Assigned class control</a></li>
							<li><a href="./display-control/fixedHeader.html">With FixedHeader</a></li>
						</ul>
					</div>

					<div class="toc-group">
						<h3><a href="./child-rows/index.html">Child rows</a></h3>
						<ul class="toc">
							<li><a href="./child-rows/disable-child-rows.html">Disable child rows</a></li>
							<li><a href="./child-rows/column-control.html">Column controlled child rows</a></li>
							<li><a href="./child-rows/right-column.html">Column control - right</a></li>
							<li><a href="./child-rows/whole-row-control.html">Whole row child row control</a></li>
							<li><a href="./child-rows/custom-renderer.html">Custom child row renderer</a></li>
						</ul>
					</div>
				</div>

				<div class="epilogue">
					<p>Please refer to the <a href="http://www.datatables.net">DataTables documentation</a> for full information about its API properties and methods.<br>
					Additionally, there are a wide range of <a href="http://www.datatables.net/extras">extras</a> and <a href="http://www.datatables.net/plug-ins">plug-ins</a>
					which extend the capabilities of DataTables.</p>

					<p class="copyright">DataTables designed and created by <a href="http://www.sprymedia.co.uk">SpryMedia Ltd</a> &#169; 2007-2015<br>
					DataTables is licensed under the <a href="http://www.datatables.net/mit">MIT license</a>.</p>
				</div>
			</div>
		</div>
	</section>
</body>
</html>