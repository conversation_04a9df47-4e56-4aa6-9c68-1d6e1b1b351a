{"name": "datetimepicker", "version": "2.4.3", "main": ["jquery.datetimepicker.js", "jquery.datetimepicker.css"], "ignore": ["**/screen", "**/datetimepicker.jquery.json", "**/*.png", "**/*.txt", "**/*.md", "**/*.html", "**/*.tpl", "**/jquery.js"], "keywords": ["calendar", "date", "time", "form", "datetime", "datepicker", "timepicker", "datetimepicker", "validation", "ui", "scroller", "picker", "i18n", "input", "j<PERSON>y", "touch"], "dependencies": {"jquery": ">= 1.7.2"}, "authors": [{"name": "Chupurnov Valeriy", "email": "<EMAIL>", "homepage": "http://xdsoft.net/contacts.html"}], "homepage": "http://xdsoft.net/jqplugins/datetimepicker/", "repository": {"type": "git", "url": "git://github.com:xdan/datetimepicker.git"}}