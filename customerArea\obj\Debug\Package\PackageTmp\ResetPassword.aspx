﻿<%@ Page Title="" Language="C#" MasterPageFile="~/pagemaster.Master" AutoEventWireup="true" CodeBehind="ResetPassword.aspx.cs" Inherits="customerArea.ResetPassword" %>

<asp:Content ID="Content1" ContentPlaceHolderID="ctPlaceHold" runat="server">

    <div class="container">

        <div class="nav-logo"><img src="/files/0224/INDIV/images/header.png" style="width:100%;"></div>

        <div class="row">


            <div class="col-md-12">
                <div class="panel panel-info" id="panelinfoforgotpass">
                    <div class="panel-heading">
                        <h3 class="panel-title">Modification de mot de passe</h3>
                    </div>
                    <div class="panel-body">

                        <div class="form-group">
                            <label id="lblName" runat="server"></label>
                        </div>
                        
                        <div class="form-group">
                            <label id="lblFirstName" runat="server"></label>
                        </div>

                         <div class="form-group">
                            <label id="lblEmailAddress" runat="server"></label>
                        </div>
                       
                    </div>
                   
                </div>
            </div>
        </div>


        <div class="row">
            <div class="col-md-4 col-md-offset-4">
                <div class="panel panel-default">
                    <div class="panel-body">
                        <div class="text-center">
                            <h3><i class="fa fa-lock fa-4x"></i></h3>
                           
                            <div class="panel-body">

                                <div id="resetPasswordForm" role="form" class="form-horizontal">

                                    <div class="form-group">
                                        <div class="input-group">
                                            <span class="input-group-addon"><i class="glyphicon glyphicon-lock color-blue"></i></span>
                                            <input id="password" name="password" placeholder="mot de passe" class="form-control" type="password">
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <div class="input-group">
                                            <span class="input-group-addon"><i class="glyphicon glyphicon-lock color-blue"></i></span>
                                            <input id="confirmpassword" name="confirmpassword" placeholder="confirmer mot de passe" class="form-control" type="password">
                                        </div>
                                    </div>


                                    <div class="form-group">
                                        <%--<input name="recover-submit" class="btn btn-lg btn-primary btn-block" value="Reset Password" type="submit">--%>

                                        <a id="LnkBtnCreate" class="btn btn-lg btn-primary btn-block" data-trad="mettre_a_jour">Update</a>

                                    </div>

                                    <input type="hidden" class="hide" name="token" id="token" value="" runat="server">
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</asp:Content>
