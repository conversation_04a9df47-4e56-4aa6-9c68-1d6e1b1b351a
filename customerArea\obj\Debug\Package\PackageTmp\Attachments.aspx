﻿<%@ Page Title="" Language="C#" MasterPageFile="~/pagemaster.Master" AutoEventWireup="true" CodeBehind="Attachments.aspx.cs" Inherits="customerArea.Attachments" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ctPlaceHold" runat="server">
    <link rel="stylesheet" type="text/css" href="/assets/LIBS/dropzone/basic.min.css" />
    <link rel="stylesheet" type="text/css" href="/assets/LIBS/dropzone/dropzone.min.css" />
    <h2 data-trad="title_my_attachments">My attachments</h2>
    <!-- commentaire haut -->
    <div id="commentsPageHaut">
        <asp:Literal ID="litForCommentaireHaut" runat="server"></asp:Literal>
    </div>
    <div id="myDropzone">
        <div class="dz-message row align-items-center " data-dz-message>
            <div class="col-12 text-center">
            <i class="fas fa-file-upload" id="dropzoneIcon"></i>
            <div data-trad="lbl_drop_files_here">Drop one or more attachments here, or click to upload</div>
            <div class="MaxSizeAttachment"></div>
            </div>
        </div>
    </div>
    <div id="resume" class="mt-2">
    </div>
    <div id="feedback"></div>
    <div id="feedback-error"></div>
    <div id="previews"></div>
    <!-- template fichier téléchargé start -->
    <div class="table table-striped" class="files" style="display: none;">
        <div id="template" class="row oneUpload">
            <div class="col-auto align-self-center ">
                <span class="preview dz-image"><img data-dz-thumbnail /></span>
            </div>
            <div class="col align-self-center">
                <div class="row">
                    <div class="col-12">
                        <span class="name" data-dz-name></span>
                        <strong class="error text-danger" data-dz-errormessage></strong>
                        <span class="size" data-dz-size></span>
                    </div>
                    <div class="col-12">
                        <div class="progress progress-striped active" role="progressbar" aria-valuemin="0" aria-valuemax="100" aria-valuenow="0">
                            <div class="progress-bar progress-bar-success" style="width:0%;" data-dz-uploadprogress></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-auto align-self-center">
                <button data-dz-remove class="btn btn-danger delete">
                    <i class="glyphicon glyphicon-trash"></i>
                    <span data-trad="btn_delete">Delete</span>
                </button>
            </div>
        </div>
    </div>
    <!-- template fichier téléchargé end -->
    <div class="row">
        <div class="col-sm-12 mt-2 text-center">
            <a id="btnValiderAttachments" class="btn btn-primary" data-trad="btn_validate" href="">Validate</a>
        </div>
    </div>
    <!-- histo fichiers téléchargés start -->
    <div class="row" id="getHistoAttachementsWrapper"  style="display: none;" >
        <div class="col-sm-12 mt-2">
            <hr/>
            <div class=" text-center">
                <div class="btn btn-secondary" id="getHistoAttachements"data-trad="btn_see_previous_attachments">Voir mes précédentes pièces jointes</div>
            </div>
            <div class="collapse" id="getHistoAttachementsCollapse">
                <div class="col-12" id="getHistoAttachementsInner">
                    <!-- content histo -->
                </div>
            </div>
            <!-- template un fichié uploadé start -->
            <div id="templateHistoAttachments" style="display: none;">
                <div class="row oneHistoAttachment">
                    <div class="col-12 col-md-auto histoAtatchmentsDate">
                    </div>
                    <div class="col-12 col-md histoAtatchmentsName">
                    </div>
                </div>
            </div>
            <!-- template un fichié uploadé end -->
        </div>
    </div>
    <!-- histo fichiers téléchargés end -->
    <!-- back and close -->
    <div class="row">
        <div class="col-sm-6 mt-2 text-center text-sm-left">
            <a href="" class="btn btn-secondary buttonBack" data-trad="btn_back">Back</a>
        </div>
        <div class="col-sm-6 mt-2 text-center text-sm-right">
            <a href="" id="buttonCancel" class="btn btn-secondary showWhenWidget" data-trad="btn_close">Close</a>
        </div>
    </div>
    <!-- commentaire bas -->
    <div id="commentsPageBas" class="row">
        <asp:Literal ID="litForCommentaireBas" runat="server"></asp:Literal>
    </div>
    <script>
        var MaxSizeAttachment = "<%= MaxSizeAttachment %>";
    </script>
    <script type="text/javascript" src="/assets/LIBS/dropzone/dropzone.min.js"></script>
    <script type="text/javascript" src="/assets/LIBS/dropzone/dropzone-amd-module.min.js"></script>
</asp:Content>