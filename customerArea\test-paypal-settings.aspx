<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="test-paypal-settings.aspx.cs" Inherits="customerArea.test_paypal_settings" %>

<!DOCTYPE html>
<html>
<head>
    <title>Test PayPal Settings</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h1>Test PayPal Settings</h1>
    
    <div id="settings-info"></div>
    
    <span id="settingsPlateform" runat="server" class="d-none"></span>
    <span id="settingsCustomer" runat="server" class="d-none"></span>
    <span id="settingsDefaultCustomer" runat="server" class="d-none"></span>

    <script type="text/javascript">
        var settings = "";
        if (($('#settingsCustomer').text() != "" || $('#settingsPlateform').text() != "") && $('#settingsDefaultCustomer').text() != "") {
            settings = $.extend(true, {}, JSON.parse($('#settingsDefaultCustomer').text()), JSON.parse($('#settingsCustomer').text()), JSON.parse($('#settingsPlateform').text()));
            
            var info = "<h2>Settings PayPal trouvés :</h2>";
            
            if (settings.login && settings.login.payPal) {
                info += "<p><strong>Client ID:</strong> " + settings.login.payPal.appid + "</p>";
                info += "<p><strong>Return URL:</strong> " + settings.login.payPal.returnurl + "</p>";
            } else {
                info += "<p style='color: red;'><strong>ERREUR:</strong> Settings PayPal non trouvés !</p>";
            }
            
            info += "<h3>Settings complets :</h3>";
            info += "<pre>" + JSON.stringify(settings, null, 2) + "</pre>";
            
            $('#settings-info').html(info);
        } else {
            $('#settings-info').html("<p style='color: red;'>Aucun settings trouvé !</p>");
        }
    </script>
</body>
</html>
