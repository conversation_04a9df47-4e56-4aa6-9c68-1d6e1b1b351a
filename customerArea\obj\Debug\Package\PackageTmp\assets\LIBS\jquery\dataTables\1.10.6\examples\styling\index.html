<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<link rel="shortcut icon" type="image/ico" href="http://www.datatables.net/favicon.ico">
	<meta name="viewport" content="initial-scale=1.0, maximum-scale=2.0">
	<link rel="stylesheet" type="text/css" href="../resources/syntax/shCore.css">
	<link rel="stylesheet" type="text/css" href="../resources/demo.css">
	<script type="text/javascript" language="javascript" src="../../media/js/jquery.js"></script>
	<script type="text/javascript" language="javascript" src="../resources/syntax/shCore.js"></script>
	<script type="text/javascript" language="javascript" src="../resources/demo.js"></script>

	<title>DataTables examples - Styling</title>
</head>

<body class="dt-example">
	<div class="container">
		<section>
			<h1>DataTables example <span>Styling</span></h1>

			<div class="info">
				<p>When using DataTables, you want your tables to fit in with your site / app to make the end user experience as seamless as possible. For this reason, DataTables
				provides an easy to customise core stylesheet, which has a number of features that you can enable or disable as you required. Additionally there are a number of
				integration packages which can be used to fit DataTables into a site which uses some of the popular CSS libraries such as Twitter Bootstrap and Foundation. Or of
				course you can craft your own CSS to fit it into your site perfectly!</p>

				<p>This section includes examples of how DataTables can be styled using these methods.</p>
			</div>
		</section>
	</div>

	<section>
		<div class="footer">
			<div class="gradient"></div>

			<div class="liner">
				<div class="toc">
					<div class="toc-group">
						<h3><a href="./index.html">Styling</a></h3>
						<ul class="toc">
							<li><a href="./display.html">Base style</a></li>
							<li><a href="./no-classes.html">Base style - no styling classes</a></li>
							<li><a href="./cell-border.html">Base style - cell borders</a></li>
							<li><a href="./compact.html">Base style - compact</a></li>
							<li><a href="./hover.html">Base style - hover</a></li>
							<li><a href="./order-column.html">Base style - order-column</a></li>
							<li><a href="./row-border.html">Base style - row borders</a></li>
							<li><a href="./stripe.html">Base style - stripe</a></li>
							<li><a href="./bootstrap.html">Bootstrap</a></li>
							<li><a href="./foundation.html">Foundation</a></li>
							<li><a href="./jqueryUI.html">jQuery UI ThemeRoller</a></li>
						</ul>
					</div>
				</div>

				<div class="epilogue">
					<p>Please refer to the <a href="http://www.datatables.net">DataTables documentation</a> for full information about its API properties and methods.<br>
					Additionally, there are a wide range of <a href="http://www.datatables.net/extras">extras</a> and <a href="http://www.datatables.net/plug-ins">plug-ins</a>
					which extend the capabilities of DataTables.</p>

					<p class="copyright">DataTables designed and created by <a href="http://www.sprymedia.co.uk">SpryMedia Ltd</a> &#169; 2007-2015<br>
					DataTables is licensed under the <a href="http://www.datatables.net/mit">MIT license</a>.</p>
				</div>
			</div>
		</div>
	</section>
</body>
</html>