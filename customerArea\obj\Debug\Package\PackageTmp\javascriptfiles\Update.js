var urlparent;
var placeSearch, autocomplete;
var componentForm = {
    street_number: 'short_name',
    route: 'long_name',
    locality: 'long_name',
    postal_code: 'short_name',
    country: 'short_name'
};

$(document).ready(function () {
    $('#tbMotPasse, #tbMotPasseConfirme').attr('pattern', settings.global.passwordRules)

    $.each($('#ctPlaceHold_WctrlLoginUpdate1_cgudiv [data-filename]'), function (i, k) {
        var urlWithLang = $(k).data('filename').replace('[lang]', '.' + getLang().toLowerCase()).replace('[customerDirectory]', customerDirectory);
        var urlWithoutLang = $(k).data('filename').replace('[lang]', '').replace('[customerDirectory]', customerDirectory);
        if (urlWithLang.indexOf('/files') != -1) {
            var finalUrl = urlWithoutLang
            if (fileExists(urlWithLang)) {
                finalUrl = urlWithLang
            }
            $(k).html(ReadXmlTranslate('lbl_cgu').replace('[link]', '<a href="' + finalUrl + '" data-toggle="modal" data-target="#modalGeneric" data-type="iframe">').replace('[/link]', '</a>'))
        }
    })

    urlparent = customerAreaBaseDomain;
    //resize l'iframe parent
    resizeParentIframe();
    //lorsque l'on fait entrer sur trouver mon adresse
    // permet de ne pas recharger la page
    $(document).keypress(function (e) {
        if (e.which == 13) {
            //annule le sumbit sur la touche enter
            e.preventDefault();
        }
    });
    // intlTelInput
    if ($('#tbTelephone').val() == "") {
        $('#tbTelephone').intlTelInput("setCountry", getLangForPhones());
    }

    if ($('#tbFax').val() == "") {
        $('#tbFax').intlTelInput("setCountry", getLangForPhones());
    }

    if ($('#tbPortable').val() == "") {
        $('#tbPortable').intlTelInput("setCountry", getLangForPhones());
    }

    //affiche / cache les input confirm email et mot de passe
    $('#accountInfos').collapse('hide')
    $('#swithUpdateEmailAndPassword').on('click', function (e) {
        if ($('input#swithUpdateEmailAndPassword').prop('checked')) {
            console.log('swithUpdateEmailAndPassword is checked')
            $('#tbEmail').removeAttr('disabled');
            $('#accountInfos').find('.form-group').removeClass('d-none')
            $('#accountInfos').collapse('show')
        } else {
            console.log('swithUpdateEmailAndPassword is NOT checked')
            $('#tbEmail').attr('disabled', 'disabled');
            $('#accountInfos').collapse('hide')
            $('#accountInfos').on('hidden.bs.collapse', function () {
                $(this).find('.form-group').addClass('d-none')
            })
        }
    })

    $('#buttonCreate').on('click', function (e) {
        e.preventDefault();
        e.stopPropagation();

        var curForm = $(this).closest('form');
        if (validForm(curForm)) {
            UpdateCustomer();
        }
    });


    //Select civilite
    $('#ddlNaming').prepend("<option value='0'>" + ReadXmlTranslate('lbl_choose_civility') + "</option>");
    //Select pays
    $('#ddlPays').prepend("<option  value='0'>" + ReadXmlTranslate('lbl_choose_country') + "</option>");

    initializeGoogleMapAutocomplete();

    //DELETE ACCOUNT
    $('#deleteMyAccount').on('click', function (e) {

        e.preventDefault();
        ShowGenericModal(ReadXmlTranslate('title_delete_my_account'), ReadXmlTranslate('msg_delete_my_account'), ReadXmlTranslate('btn_validate'), ReadXmlTranslate('btn_cancel'), 'btn-delete-my-account-confirm', '')

        $('#btn-delete-my-account-confirm').on('click', function () {

            deletemyaccount();

        });

    });


});

function deletemyaccount() {
    // var myidentity = $('head').attr('myidentity')

    // var sData = JSON.stringify({ structureid: structureid, identity: myidentity  });

    $.ajax({
        type: "POST",
        url: 'commons.asmx/DeleteMyAccount',
        //  data: sData,
        contentType: 'application/json; charset=utf-8',
        dataType: "json",
        success: function (data) {
            if (data.d.split(':')[0] == "success") {
                showAlertSuccess('', ReadXmlTranslate(data.d.split(':')[1]), '')

            } else {
                showAlertError('', ReadXmlTranslate(data.d.split(':')[1]), '')
            }

        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            showAlertError('', XMLHttpRequest.responseJSON.Message + "<br />" + XMLHttpRequest.responseJSON.StackTrace, '')
        },
        complete: function (XMLHttpRequest, textStatus, errorThrown) {
            location.reload();
        }
    });
}


function UpdateCustomer() {


    var ICyes = $('#infoCompsdiv input[type=checkbox]:checked').map(function () {
        return $(this).attr("data-ic");
    }).get().join(';')
    var ICnot = $('#infoCompsdiv input[type=checkbox]:not(:checked)').map(function () {
        return $(this).attr("data-ic");
    }).get().join(';');

    var structureid = $('#myhead').attr('structureid');
    var eventid = (!!$("#myhead").attr('eventid')) ? $("#myhead").attr('eventid') : 0;

    var ctrlh = $('#buttonCreate').attr('hash');


    var CustomerEntity = {};
    CustomerEntity.Name = (($('#tbNom').data('upper') == true) ? $('#tbNom').val().toUpperCase() : $('#tbNom').val()) || '';
    CustomerEntity.FirstName = (($('#tbPrenom').data('upper') == true) ? $('#tbPrenom').val().toUpperCase() : $('#tbPrenom').val()) || '';
    CustomerEntity.Email = $('#tbEmail').val();
    CustomerEntity.Password = $('#tbMotPasse').val();
    CustomerEntity.CiviliteId = ($('#ddlNaming option:selected').length > 0) ? $('#ddlNaming option:selected').val() : "0";
    CustomerEntity.Sex = ($('#sexDiv label.active input').length == 1) ? $('#sexDiv label.active input').val() : "";
    CustomerEntity.DateOfBirthday = validateDobWithPattern($("#tbDateNaissance").val(), $("#tbDateNaissance").attr('data-pageihm-mandatory')) || '';
    CustomerEntity.Address1 = (($('#tbAdresse1').data('upper') == true) ? $('#tbAdresse1').val().toUpperCase() : $('#tbAdresse1').val()) || '';
    CustomerEntity.Address2 = (($('#tbAdresse2').data('upper') == true) ? $('#tbAdresse2').val().toUpperCase() : $('#tbAdresse2').val()) || '';
    CustomerEntity.Address3 = (($('#tbAdresse3').data('upper') == true) ? $('#tbAdresse3').val().toUpperCase() : $('#tbAdresse3').val()) || '';
    CustomerEntity.Address4 = (($('#tbAdresse4').data('upper') == true) ? $('#tbAdresse4').val().toUpperCase() : $('#tbAdresse4').val()) || '';
    CustomerEntity.PostalCode = ($('#tbCodePostal').length == 1) ? $('#tbCodePostal').val() : "";
    CustomerEntity.City = (($('#tbVille').data('upper') == true) ? $('#tbVille').val().toUpperCase() : $('#tbVille').val()) || '';
    CustomerEntity.Country = ($('#ddlPays').length == 1) ? $('#ddlPays option:selected').text() : "";
    CustomerEntity.HomeNum = ($('#tbTelephone').length == 1) ? $('input[name="ctl00$ctPlaceHold$WctrlLoginUpdate1$tbTelephone"]').intlTelInput("getNumber") : "";
    CustomerEntity.MobileNum = ($('#tbPortable').length == 1) ? $('input[name="ctl00$ctPlaceHold$WctrlLoginUpdate1$tbFax"]').intlTelInput("getNumber") : "";
    CustomerEntity.FaxNum = ($('#tbFax').length == 1) ? $('input[name="ctl00$ctPlaceHold$WctrlLoginUpdate1$tbPortable"]').intlTelInput("getNumber") : "";
    CustomerEntity.InfosCompChecked = ICyes;
    CustomerEntity.InfosCompNotChecked = ICnot;
    CustomerEntity.Hash = ctrlh;
    CustomerEntity.Comment = (($('#tbComment').data('upper') == true) ? $('#tbComment').val().toUpperCase() : $('#tbComment').val()) || '';
    CustomerEntity.IdentiteComplement = (($('#tbEnterprise').data('upper') == true) ? $('#tbEnterprise').val().toUpperCase() : $('#tbEnterprise').val()) || '';
    CustomerEntity.TitreId = ($('#ddlFunction option:selected').length > 0) ? $('#ddlFunction option:selected').val() : "0";
    CustomerEntity.LangCodeOfUser = ($('#ddlLanguage option:selected').length > 0) ? $('#ddlLanguage option:selected').val() : "0";

    var sData = JSON.stringify({ structureid: structureid, eventid: eventid, customer: CustomerEntity });

    $.ajax({
        type: "POST",
        url: 'update.aspx/UpdateCustomerWM',
        data: sData,
        contentType: 'application/json; charset=utf-8',
        dataType: "json",
        success: function (data) {
            if (data.d.split(':')[0] == "success") {
                showAlertSuccess('', ReadXmlTranslate(data.d.split(':')[1]), '')
            } else {
                showAlertError('', ReadXmlTranslate(data.d.split(':')[1]), '')
            }

            if (iscustomerwidget() && urlparent != undefined)
                parent.postMessage("Update:" + data.d, urlparent);

        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            // ShowError("msgNotificationAlert", XMLHttpRequest.responseJSON.Message + "<br />" + XMLHttpRequest.responseJSON.StackTrace, "alert alert-danger alert-dismissable", -1);
            // console.log(XMLHttpRequest.responseJSON + "\r\n" + XMLHttpRequest.responseJSON.StackTrace);
            showAlertError('', XMLHttpRequest.responseJSON.Message + "<br />" + XMLHttpRequest.responseJSON.StackTrace, '')
        }
    });

}



/*
function UpdateCustomer() {

    var Name = $('#tbNom').val();
    var FistName = $('#tbPrenom').val();
    var Email = $('#tbEmail').val();
    var PassW = $('#tbMotPasse').val();

    var CivId = 1;
    if ($('#ddlNaming option:selected').length > 0)
        CivId = $('#ddlNaming option:selected').val();

    var SexId = ($('#sexDiv label.active input').length == 1) ? $('#sexDiv label.active input').val() : "1";

    var Dob = validateDobWithPattern($("#tbDateNaissance").val()) || ''
    var Adress1 = ($('#tbAdresse1').length == 1) ? $('#tbAdresse1').val() : "";
    var Adress2 = ($('#tbAdresse2').length == 1) ? $('#tbAdresse2').val() : "";
    var Adress3 = ($('#tbAdresse3').length == 1) ? $('#tbAdresse3').val() : "";
    var Adress4 = ($('#tbAdresse4').length == 1) ? $('#tbAdresse4').val() : "";
    var Cp = ($('#tbCodePostal').length == 1) ? $('#tbCodePostal').val() : "";
    var City = ($('#tbVille').length == 1) ? $('#tbVille').val() : "";

    var Country = ($('#ddlPays').length == 1) ? $('#ddlPays option:selected').val() : "";

    //var HomN = ($('#tbTelephone').length == 1) ? $('#tbTelephone').val() : "";
    var HomN = ($('#tbTelephone').length == 1) ? $('input[name="ctl00$ctPlaceHold$WctrlLoginUpdate1$tbTelephone"]').intlTelInput("getNumber") : "";
    var FaxN = ($('#tbFax').length == 1) ? $('input[name="ctl00$ctPlaceHold$WctrlLoginUpdate1$tbFax"]').intlTelInput("getNumber") : "";
    var MobN = ($('#tbPortable').length == 1) ? $('input[name="ctl00$ctPlaceHold$WctrlLoginUpdate1$tbPortable"]').intlTelInput("getNumber") : "";

    var Comment = ($('#tbComment').length == 1) ? $('#tbComment').val() : "";

    $('#infoCompsdiv ul#ICs input[type=checkbox]:checked')

    var ICyes = $('#infoCompsdiv ul#ICs input[type=checkbox]:checked').map(function () {
        return $(this).attr("data-ic");
    }).get().join(';')
    var ICnot = $('#infoCompsdiv ul#ICs input[type=checkbox]:not(:checked').map(function () {
        return $(this).attr("data-ic");
    }).get().join(';');

    var structureid = $('#myhead').attr('structureid');
    var eventid = (!!$("#myhead").attr('eventid')) ? $("#myhead").attr('eventid') : 0;

    var ctrlh = $('#buttonCreate').attr('hash');
    var identite = $('#buttonCreate').attr('identite');


    var pgmeth = PageMethods.BtnUpdateWM(structureid, identite, ctrlh,
        Name, FistName, Email,
        CivId,
        SexId,
        Dob,
        Adress1,
        Adress2,
        Adress3,
        Adress4,
        Cp,
        City,
        Country,
        HomN,
        MobN,
        FaxN,
        PassW,
        ICyes, ICnot, Comment,
        UpdateMethodSuccess, MethodeJS_Error);
}

function UpdateMethodSuccess(resultc, userContext, methodName) {
    var idi = 0;
    var urlparentP = getUrlVars()["parent"];
    if (urlparentP != undefined)
        urlparent = urlparentP;

    if (resultc.indexOf("true:") == 0) {

        if (urlparent != undefined)
            parent.postMessage("Update:" + resultc, urlparent);


        showAlertSuccess('', ReadXmlTranslate('msg_success_update'), '');
        /*$('#modalForMessageBody').html(ReadXmlTranslate('msg_success_update'));
        $('#modalForMessage').delay(2500).fadeOut(function () {
            $('#modalForMessage').modal("hide");

            if (urlparent != undefined)
                parent.postMessage("Closeme:" + resultc, urlparent);
        });*/
/*

    }
    else {   // not authentif
        var txtIsIdentif = parent.$get('ctl00_ContentPlaceHolder_txtIsIdentif');
        if (txtIsIdentif != null) {
            txtIsIdentif.value = "false";
        }
        $('#modalForMessageBody').html(ReadXmlTranslate('msg_error_update'));
        //$('#modalForWaiting').modal('hide');
        //$('#modalForMessage').modal("show");
        $('#modalForMessage').delay(2500).fadeOut(function () {
            $('#modalForMessage').modal("hide");
            if (urlparent != undefined)
                parent.postMessage("Closeme:" + resultc, urlparent);
        });
    }

}
*/
function initializeGoogleMapAutocomplete() {
    // Create the autocomplete object, restricting the search
    // to geographical location types.
    autocomplete = new google.maps.places.Autocomplete(
        /** @type {HTMLInputElement} */
        (document.getElementById('addressgeo')),

        { types: ['geocode'] });
    // When the user selects an address from the dropdown,
    // populate the address fields in the form.
    google.maps.event.addListener(autocomplete, 'place_changed', function () {
        fillInAddress();
    });
}
// [START region_fillform]
function fillInAddress() {
    // Get the place details from the autocomplete object.
    var place = autocomplete.getPlace();

    for (var component in componentForm) {
        //document.getElementById(component).value = '';
        $('input[data-geo-' + component + ']').val("")
        //document.getElementById(component).disabled = false;
    }

    // Get each component of the address from the place details
    // and fill the corresponding field on the form.
    for (var i = 0; i < place.address_components.length; i++) {
        var addressType = place.address_components[i].types[0];
        if (componentForm[addressType]) {

            var val = place.address_components[i][componentForm[addressType]];
            var oldval = $('input[data-geo-' + addressType + ']').val();
            if (oldval != "") {
                if ($('input[data-geo-' + addressType + ']').data('upper') == true) {
                    $('input[data-geo-' + addressType + ']').val(oldval.toUpperCase() + " " + val.toUpperCase()); // pour route + street_number
                } else {
                    $('input[data-geo-' + addressType + ']').val(oldval + " " + val); // pour route + street_number
                }
            }
            else {
                if ($('input[data-geo-' + addressType + ']').data('upper') == true)
                    $('input[data-geo-' + addressType + ']').val(val.toUpperCase());
                else
                    $('input[data-geo-' + addressType + ']').val(val);
            }

            // pays
            if (addressType == "country") {
                //   $('select')                
                $('select[data-geo-country] option[value^=' + val + ']').prop('selected', 'selected');
            }

            //document.getElementById(addressType).value = val;
        }
    }
}
// [END region_fillform]