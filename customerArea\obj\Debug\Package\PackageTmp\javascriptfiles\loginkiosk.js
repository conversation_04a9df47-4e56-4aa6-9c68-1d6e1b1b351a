﻿$(document).ready(function() {

    var languagesList = getLanguesList();
    if(languagesList.length > 1) {
        $.each(languagesList, function(key, value) {
            $('#languagelist').append('<a class="dropdown-item lang" href="" data-lang="' + value.LangCode + '">' + toTitleCase(value.LangName) + '</a>')
        }); 
    }else {
        $('#languagelistWrapper').remove();
    }

    setLanguageClick()


    $('#btnleave').click(function(e){
        e.preventDefault();
        var urlLToLeave = getUrlVars()["quit"];

        if(urlLToLeave != undefined)
            window.location.href = urlLToLeave;
    })

});


// redirige l'utilisateur apres login ou creation de compte
function RedirectHomePage(resultc) {
    var userData = resultc.split(':')
    /*
    userData[0] = connect success
    userData[1] = user id
    userData[2] = hash
    */
    var homepageHref = $('#ctPlaceHold_hlHomePage').attr("href");
    if (homepageHref == "closeme") {
        // TODO close me popUp 
    } else {
        if (getUrlVars()["ref"] != "" && getUrlVars()["ref"] != undefined) {
            var urlGoto =  ""
            if(getUrlVars()["goto"] != undefined && getUrlVars()["goto"] != "") {
                urlGoto =  btoa(getUrlVars()["goto"]+$('#receptVar').data('lang').toLowerCase())
            }
            var linkToConnect = getUrlVars()["ref"] + "/" + userData[1] + "/" + userData[2] + "/" + urlGoto;
            location.replace(linkToConnect);
        }
    }

}

function toTitleCase(str) {
    return str.replace(
        /\w\S*/g,
        function(txt) {
            return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
        }
    );
}
