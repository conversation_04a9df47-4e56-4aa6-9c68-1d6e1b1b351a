<%@ Page Title="" Language="C#" MasterPageFile="~/pagemaster.Master" AutoEventWireup="true" CodeBehind="LinkedConsumersList.aspx.cs" Inherits="customerArea.LinkedConsumersList" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ctPlaceHold" runat="server">
    <h2 data-trad="title_linkedconsumerslist">Manage consumers</h2>
    <!-- commentaire haut -->
    <div id="commentsPageHaut">
        <asp:Literal ID="litForCommentaireHaut" runat="server"></asp:Literal>
    </div>
    <div id="accordion">
        <!-- Attach a consumer already existing START -->
        <div class="card">
            <div class="card-header" id="headingLinkedConsumers1">
                <h5 class="mb-0">
                    <a href="#" class="text-left d-block btn btn-link" data-toggle="collapse" data-target="#collapseLinkedConsumers1" aria-expanded="true" aria-controls="collapseLinkedConsumers1">
                        <div class="row no-gutters align-items-center">
                            <div class="col-auto">
                                <svg class="plusoumoins img-fluid" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 16.4 16.3" style="enable-background:new 0 0 16.4 16.3;" xml:space="preserve">
                                    <g>
                                        <g>
                                            <path class="st0" d="M13.4,9.2H3c-0.6,0-1-0.4-1-1s0.4-1,1-1h10.3c0.6,0,1,0.4,1,1S13.9,9.2,13.4,9.2z"></path>
                                        </g>
                                        <g>
                                            <g>
                                                <path class="st0" d="M9.5,16.3c-0.2,0-0.4-0.2-0.5-0.4c0-0.3,0.1-0.5,0.4-0.6c3.4-0.6,5.9-3.6,5.9-7.1c0-4-3.2-7.2-7.2-7.2 S1,4.2,1,8.2c0,3.5,2.5,6.5,5.9,7.1c0.3,0,0.5,0.3,0.4,0.6c0,0.3-0.3,0.5-0.6,0.4C2.8,15.6,0,12.2,0,8.2C0,3.7,3.7,0,8.2,0 c4.5,0,8.2,3.7,8.2,8.2C16.4,12.2,13.5,15.6,9.5,16.3C9.6,16.2,9.6,16.3,9.5,16.3z"></path>
                                            </g>
                                        </g>
                                    </g>
                                    <g class="verticalbar">
                                        <g>
                                            <path class="st0" d="M8.2,14.4c-0.6,0-1-0.4-1-1V3c0-0.6,0.4-1,1-1s1,0.4,1,1v10.3C9.2,13.9,8.7,14.4,8.2,14.4z"></path>
                                        </g>
                                    </g>
                                </svg>
                            </div>
                            <div class="col card-customertitle">
                                <span class="font-weight-bold" data-trad="lbl_attach_consumer_alreadyexisting">Attach a consumer already existing</span>
                            </div>
                        </div>
                    </a>
                </h5>
            </div>
            <div id="collapseLinkedConsumers1" class="collapse show" aria-labelledby="headingLinkedConsumers1">
                <div class="card-body">
                    <div class="row">
                        <div id="attachConsumerexistingDiv" class="col-12" runat="server">
                            <%--Contenu du formulaire--%>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Attach a consumer already existing END -->
        <!-- Attach a new consumer START -->
        <div class="card">
            <div class="card-header" id="headingLinkedConsumers2">
                <h5 class="mb-0">
                    <a href="#" class="text-left d-block btn btn-link collapsed" data-toggle="collapse" data-target="#collapseLinkedConsumers2" aria-expanded="false" aria-controls="collapseLinkedConsumers2">
                        <div class="row no-gutters align-items-center">
                            <div class="col-auto">
                                <svg class="plusoumoins img-fluid" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 16.4 16.3" style="enable-background:new 0 0 16.4 16.3;" xml:space="preserve">
                                    <g>
                                        <g>
                                            <path class="st0" d="M13.4,9.2H3c-0.6,0-1-0.4-1-1s0.4-1,1-1h10.3c0.6,0,1,0.4,1,1S13.9,9.2,13.4,9.2z"></path>
                                        </g>
                                        <g>
                                            <g>
                                                <path class="st0" d="M9.5,16.3c-0.2,0-0.4-0.2-0.5-0.4c0-0.3,0.1-0.5,0.4-0.6c3.4-0.6,5.9-3.6,5.9-7.1c0-4-3.2-7.2-7.2-7.2 S1,4.2,1,8.2c0,3.5,2.5,6.5,5.9,7.1c0.3,0,0.5,0.3,0.4,0.6c0,0.3-0.3,0.5-0.6,0.4C2.8,15.6,0,12.2,0,8.2C0,3.7,3.7,0,8.2,0 c4.5,0,8.2,3.7,8.2,8.2C16.4,12.2,13.5,15.6,9.5,16.3C9.6,16.2,9.6,16.3,9.5,16.3z"></path>
                                            </g>
                                        </g>
                                    </g>
                                    <g class="verticalbar">
                                        <g>
                                            <path class="st0" d="M8.2,14.4c-0.6,0-1-0.4-1-1V3c0-0.6,0.4-1,1-1s1,0.4,1,1v10.3C9.2,13.9,8.7,14.4,8.2,14.4z"></path>
                                        </g>
                                    </g>
                                </svg>
                            </div>
                            <div class="col card-customertitle">
                                <span class="font-weight-bold" data-trad="lbl_attach_new_consumer">Attach a new consumer</span>
                            </div>
                        </div>
                    </a>
                </h5>
            </div>
            <div id="collapseLinkedConsumers2" class="collapse" aria-labelledby="headingLinkedConsumers2">
                <div class="card-body">
                    <div class="row">
                        <div id="attachConsumerDiv" class="col-12">
                            <!-- Form ajouter nouveau consommateur START -->
                            <div id="attachConsumerForm" class="form-horizontal" role="form">
                                <!-- civilité -->
                                <div class="form-group" id="attachFormGroupCivility" runat="server">
                                    <label for="ddlnaming" class="sr-only" data-trad="lbl_civility">Civilité</label>
                                    <select class="form-control" name="ddlnaming" id="attachSelectCivility" runat="server" data-validator-type="ddlnaming">
                                        <option selected="selected" value="0" data-trad="lbl_choose_civility">Choose your civility</option>
                                    </select>
                                    <div class="invalid-feedback" data-trad="msg_error_civility">Please enter your civility</div>
                                </div>
                                <!-- prenom -->
                                <div class="form-group" id="attachFormGroupFirstName" runat="server">
                                    <label for="firstname" class="sr-only" data-trad="lbl_firstname">Prénom</label>
                                    <input type="text" class="form-control" id="attachInputFirstName" runat="server" data-validator-type="firstname" name="firstname" placeholder="Firstname" data-tradplaceholder="placeholder_firstname" />
                                    <div class="invalid-feedback" data-trad="msg_error_firstname">Please enter your firstname</div>
                                </div>
                                <!-- nom -->
                                <div class="form-group" id="attachFormGroupLastName" runat="server">
                                    <label for="name" class="sr-only" data-trad="lbl_lastname">Nom</label>
                                    <input type="text" class="form-control" id="attachInputLastName" runat="server" data-validator-type="name" name="name" placeholder="Lastname" data-tradplaceholder="placeholder_lastname" />
                                    <div class="invalid-feedback" data-trad="msg_error_lastname">Please enter your lastname</div>
                                </div>
                                <!-- email -->
                                <div class="form-group" id="attachFormGroupEmail" runat="server">
                                    <label for="email" class="sr-only" data-trad="lbl_email">E-mail</label>
                                    <input type="email" class="form-control" id="attachInputEmail" runat="server" data-validator-type="email" name="email" placeholder="E-mail" data-tradplaceholder="placeholder_attachment_email" />
                                    <div class="invalid-feedback" data-trad="msg_error_email">Please enter a valid email</div>
                                </div>
                                <!-- confirm email -->
                                <div class="form-group" id="attachFormGroupConfirmEmail" runat="server">
                                    <label for="confirmemail" class="sr-only" data-trad="lbl_confirm_email">Confirm E-mail</label>
                                    <input type="email" class="form-control" id="attachInputConfirmEmail" runat="server" data-validator-type="confirmemail" name="confirmemail" placeholder="Confirm E-mail" data-tradplaceholder="placeholder_confirm_email" />
                                    <div class="invalid-feedback" data-trad="msg_error_confirm_email">Your e-mail is not identical</div>
                                </div>
                                <!-- date de naissance -->
                                <div class="form-group" id="attachFormGroupDob" runat="server">
                                    <label for="dob" class="sr-only" data-trad="lbl_dob">Date of birth</label>
                                    <input type="text" class="form-control" id="attachInputDob" data-validator-type="datenaissance" runat="server" name="dob" placeholder="Date of birth" data-tradplaceholder="placeholder_dob" />
                                    <div class="invalid-feedback" data-trad="msg_error_dob">Please enter a valid date of birth</div>
                                </div>
                                <!-- mobile  -->
                                <div class="form-group" id="attachFormGroupMobilenumber" runat="server">
                                    <label for="portablenumber" class="sr-only" data-trad="lbl_mobilenumber">Mobile</label>
                                    <input type="text" class="form-control" id="attachInputMobilenumber" runat="server" data-validator-type="portablenumber" name="portablenumber" placeholder="Mobile number" data-tradplaceholder="placeholder_mobilenumber" />
                                    <div class="invalid-feedback" data-trad="msg_error_faxnumber">Please enter your fax number</div>
                                </div>
                                <!-- téléphone -->
                                <div class="form-group" id="attachFormGroupPhonenumber" runat="server">
                                    <label for="phonenumber" class="sr-only" data-trad="lbl_phonenumber">Téléphone</label>
                                    <input type="text" class="form-control" id="attachInputPhonenumber" runat="server" data-validator-type="phonenumber" name="phonenumber" placeholder="Phone number" data-tradplaceholder="placeholder_phonenumber" />
                                    <div class="invalid-feedback" data-trad="msg_error_phonenumber">Please enter your phone number</div>
                                </div>
                                <!-- fax -->
                                <div class="form-group" id="attachFormGroupFaxnumber" runat="server">
                                    <label for="faxnumber" class="sr-only" data-trad="lbl_faxnumber">Fax</label>
                                    <input type="text" class="form-control" id="attachInputFaxnumber" runat="server" data-validator-type="faxnumber" name="faxnumber" placeholder="Fax number" data-tradplaceholder="placeholder_faxnumber" />
                                    <div class="invalid-feedback" data-trad="msg_error_faxnumber">Please enter your fax number</div>
                                </div>
                                <!-- company -->
                                <div class="form-group" id="attachFormGroupCompany" runat="server">
                                    <label for="company" class="sr-only" data-trad="lbl_enterprise">Company</label>
                                    <input type="text" class="form-control" id="attachInputCompany" runat="server" data-validator-type="company" name="company" placeholder="Company" data-tradplaceholder="placeholder_enterprise" />
                                    <div class="invalid-feedback" data-trad="msg_error_enterprise">Please enter your company</div>
                                </div>
                                <!-- address 1 -->
                                <div class="form-group" id="attachFormGroupAddress1" runat="server">
                                    <label for="address1" class="sr-only" data-trad="lbl_address1">Address 1</label>
                                    <input type="text" class="form-control" id="attachInputAddress1" runat="server" data-validator-type="address1" name="address1" placeholder="Address 1" data-tradplaceholder="placeholder_address1" />
                                    <div class="invalid-feedback" data-trad="msg_error_address">Please enter your address</div>
                                </div>
                                <!-- address 2 -->
                                <div class="form-group" id="attachFormGroupAddress2" runat="server">
                                    <label for="address2" class="sr-only" data-trad="lbl_address2">Address 2</label>
                                    <input type="text" class="form-control" id="attachInputAddress2" runat="server" data-validator-type="address2" name="address2" placeholder="Address 2" data-tradplaceholder="placeholder_address2" />
                                    <div class="invalid-feedback" data-trad="msg_error_address">Please enter your address</div>
                                </div>
                                <!-- address 3 -->
                                <div class="form-group" id="attachFormGroupAddress3" runat="server">
                                    <label for="address3" class="sr-only" data-trad="lbl_address3">Address 3</label>
                                    <input type="text" class="form-control" id="attachInputAddress3" runat="server" data-validator-type="address3" name="address3" placeholder="Address 3" data-tradplaceholder="placeholder_address3" />
                                    <div class="invalid-feedback" data-trad="msg_error_address">Please enter your address</div>
                                </div>
                                <!-- address 4 -->
                                <div class="form-group" id="attachFormGroupAddress4" runat="server">
                                    <label for="address4" class="sr-only" data-trad="lbl_address4">Address 4</label>
                                    <input type="text" class="form-control" id="attachInputAddress4" runat="server" data-validator-type="address4" name="address4" placeholder="Address 4" data-tradplaceholder="placeholder_address4" />
                                    <div class="invalid-feedback" data-trad="msg_error_address">Please enter your address</div>
                                </div>
                                <!-- pays -->
                                <div class="form-group" id="attachFormGroupCountry" runat="server">
                                    <label for="ddlcountry" class="sr-only" data-trad="lbl_country">Pays</label>
                                    <select class="form-control" name="ddlcountry" id="attachSelectCountry" runat="server" data-validator-type="ddlcountry">
                                        <option selected="selected" value="0" data-trad="lbl_choose_country">Choose your country</option>
                                    </select>
                                    <div class="invalid-feedback" data-trad="msg_error_country">Please enter your country</div>
                                </div>
                                <!-- ville -->
                                <div class="form-group" id="attachFormGroupCity" runat="server">
                                    <label for="city" class="sr-only" data-trad="lbl_city">City</label>
                                    <input type="text" class="form-control" id="attachInputCity" runat="server" data-validator-type="city" name="city" placeholder="City" data-tradplaceholder="placeholder_city" />
                                    <div class="invalid-feedback" data-trad="msg_error_city">Please enter your city</div>
                                </div>
                                <!-- cp -->
                                <div class="form-group" id="attachFormGroupZipcode" runat="server">
                                    <label for="zipcode" class="sr-only" data-trad="lbl_cp">Zip code</label>
                                    <input type="text" class="form-control" id="attachInputZipcode" runat="server" data-validator-type="zipcode" name="zipcode" placeholder="Zip code" data-tradplaceholder="placeholder_cp" />
                                    <div class="invalid-feedback" data-trad="msg_error_cp">Please enter your zipcode</div>
                                </div>
                                
                                <!-- commentaires -->
                                <div class="form-group" id="attachFormGroupComments" runat="server">
                                    <label for="comment" class="sr-only" data-trad="lbl_comments">Comments</label>
                                    <textarea cols="10" rows="5" class="form-control" id="attachInputComments" runat="server" name="comment" placeholder="Comments" data-tradplaceholder="placeholder_comments"></textarea>
                                    <div class="invalid-feedback" data-trad="msg_error_comments">Please enter a comment</div>
                                </div>
                                <!-- validation -->
                                <div class="row">
                                    <div class="col-12 mt-2 text-center text-sm-right">
                                        <button type="submit" id="btnValiderConsumer" class="btn btn-primary" data-trad="btn_add_this_consumer">Add this consumer</button>
                                    </div>
                                </div>
                            </div>
                            <!-- Form ajouter nouveau consommateur END -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Attach a new consumer END -->
        <!-- Attach a new consumer START -->
        <div class="card">
            <div class="card-header" id="headingLinkedConsumers3">
                <h5 class="mb-0">
                    <a href="#" class="text-left d-block btn btn-link collapsed" data-toggle="collapse" data-target="#collapseLinkedConsumers3" aria-expanded="false" aria-controls="collapseLinkedConsumers3">
                        <div class="row no-gutters align-items-center">
                            <div class="col-auto">
                                <svg class="plusoumoins img-fluid" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 16.4 16.3" style="enable-background:new 0 0 16.4 16.3;" xml:space="preserve">
                                    <g>
                                        <g>
                                            <path class="st0" d="M13.4,9.2H3c-0.6,0-1-0.4-1-1s0.4-1,1-1h10.3c0.6,0,1,0.4,1,1S13.9,9.2,13.4,9.2z"></path>
                                        </g>
                                        <g>
                                            <g>
                                                <path class="st0" d="M9.5,16.3c-0.2,0-0.4-0.2-0.5-0.4c0-0.3,0.1-0.5,0.4-0.6c3.4-0.6,5.9-3.6,5.9-7.1c0-4-3.2-7.2-7.2-7.2 S1,4.2,1,8.2c0,3.5,2.5,6.5,5.9,7.1c0.3,0,0.5,0.3,0.4,0.6c0,0.3-0.3,0.5-0.6,0.4C2.8,15.6,0,12.2,0,8.2C0,3.7,3.7,0,8.2,0 c4.5,0,8.2,3.7,8.2,8.2C16.4,12.2,13.5,15.6,9.5,16.3C9.6,16.2,9.6,16.3,9.5,16.3z"></path>
                                            </g>
                                        </g>
                                    </g>
                                    <g class="verticalbar">
                                        <g>
                                            <path class="st0" d="M8.2,14.4c-0.6,0-1-0.4-1-1V3c0-0.6,0.4-1,1-1s1,0.4,1,1v10.3C9.2,13.9,8.7,14.4,8.2,14.4z"></path>
                                        </g>
                                    </g>
                                </svg>
                            </div>
                            <div class="col card-customertitle">
                                <span class="font-weight-bold" data-trad="lbl_linked_consumers_list">List of consumers who are attached to me</span>
                            </div>
                        </div>
                    </a>
                </h5>
            </div>
            <div id="collapseLinkedConsumers3" class="collapse" aria-labelledby="headingLinkedConsumers3">
                <div class="card-body">
                    <div class="row">
                        <table id="consumers_list_table" class="table table-striped w-100" aria-describedby="orders_list">
                            <thead class="thead-dark">
                                <tr role="row">
                                    <th></th>
                                    <th><span data-trad="lbl_id">ID</span></th>
                                    <th><span data-trad="lbl_lastname">Lastname</span></th>
                                    <th><span data-trad="lbl_firstname">Firstname</span></th>
                                    <th><span data-trad="lbl_email">E-mail</span></th>
                                    <th><span data-trad="lbl_delete">Delete</span></th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <!-- Attach a new consumer END -->
    </div>
    <div class="row">
        <div class="col-sm-6 mt-2 text-center text-sm-left">
            <a href="" class="btn btn-secondary buttonBack" data-trad="btn_back">Back</a>
        </div>
        <div class="col-sm-6 mt-2 text-center text-sm-right">
            <a href="" id="buttonCancel" class="btn btn-secondary showWhenWidget" data-trad="btn_close">Close</a>
        </div>
    </div>
    <!-- commentaires bas -->
    <div id="divCommentaireBas" class="small">
        <asp:Literal ID="litForCommentaireBas" runat="server"></asp:Literal>
    </div>


    <span id="postalCodeData" runat="server" ClientIDMode="static" class="d-none"></span>
    <script type="text/javascript">
    var postalCodeData =""
    if ( $('#postalCodeData').text() != "" ) {
        postalCodeData = JSON.parse($('#postalCodeData').text())
    }
</script>
</asp:Content>