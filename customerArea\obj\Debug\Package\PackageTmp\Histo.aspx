<%@ Page Title="" Language="C#" MasterPageFile="~/pagemaster.Master" AutoEventWireup="true" CodeBehind="Histo.aspx.cs" Inherits="customerArea.Histo" %>
<%@ Register Assembly="System.Web.Extensions, Version=1.0.61025.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35"
Namespace="System.Web.UI" TagPrefix="asp" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ctPlaceHold" runat="server">
    <asp:ScriptManager ID="ScriptManager1" runat="server" EnablePageMethods="True"></asp:ScriptManager>
    <span runat="server" id="dvpflag" ClientIDMode="Static"></span>
    <!-- commentaire haut -->
    <div id="commentsPageHaut">
        <asp:Literal ID="litForCommentaireHaut" runat="server"></asp:Literal>
    </div>
    <h2 data-trad="title_my_orders">My orders</h2>
    <div id="accordion">
        <!-- historique commande START -->
        <div class="card">
            <div class="card-header" id="headingHisto1">
                <h5 class="mb-0">
                    <a href="#" class="text-left d-block btn btn-link" data-toggle="collapse" data-target="#collapseHisto1" aria-expanded="true" aria-controls="collapseHisto1">
                        <div class="row no-gutters align-items-center">
                            <div class="col-auto">
                                <svg class="plusoumoins img-fluid" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 16.4 16.3" style="enable-background:new 0 0 16.4 16.3;" xml:space="preserve">
                                    <g>
                                        <g>
                                            <path class="st0" d="M13.4,9.2H3c-0.6,0-1-0.4-1-1s0.4-1,1-1h10.3c0.6,0,1,0.4,1,1S13.9,9.2,13.4,9.2z"></path>
                                        </g>
                                        <g>
                                            <g>
                                                <path class="st0" d="M9.5,16.3c-0.2,0-0.4-0.2-0.5-0.4c0-0.3,0.1-0.5,0.4-0.6c3.4-0.6,5.9-3.6,5.9-7.1c0-4-3.2-7.2-7.2-7.2 S1,4.2,1,8.2c0,3.5,2.5,6.5,5.9,7.1c0.3,0,0.5,0.3,0.4,0.6c0,0.3-0.3,0.5-0.6,0.4C2.8,15.6,0,12.2,0,8.2C0,3.7,3.7,0,8.2,0 c4.5,0,8.2,3.7,8.2,8.2C16.4,12.2,13.5,15.6,9.5,16.3C9.6,16.2,9.6,16.3,9.5,16.3z"></path>
                                            </g>
                                        </g>
                                    </g>
                                    <g class="verticalbar">
                                        <g>
                                            <path class="st0" d="M8.2,14.4c-0.6,0-1-0.4-1-1V3c0-0.6,0.4-1,1-1s1,0.4,1,1v10.3C9.2,13.9,8.7,14.4,8.2,14.4z"></path>
                                        </g>
                                    </g>
                                </svg>
                            </div>
                            <div class="col card-customertitle">
                                <span class="font-weight-bold" data-trad="lbl_my_order_history">My order history</span>
                            </div>
                        </div>
                    </a>
                </h5>
            </div>
            <div id="collapseHisto1" class="collapse show" aria-labelledby="headingHisto1">
                <div class="card-body">
                    <div class="row">
                        <table id="tblOrderList2" class="table table-striped w-100" aria-describedby="orders_list">
                            <thead class="thead-dark">
                                <tr role="row">
                                    <!--<th></th>-->
                                    <th></th>
                                    <th><span data-trad="lbl_event" data-row="date">Date achat</span></th>
                                    <th><span data-trad="lbl_event">Event</span></th>
                                    <th><span data-trad="lbl_order_number_short">Order number</span></th>
                                    <th><span data-trad="lbl_number_of_place_short">Number of place</span></th>
                                    <th><span data-trad="lbl_amount">Amount</span></th>
                                    <th><span data-trad="lbl_seance">Seance</span></th>
                                    <th><span data-trad="lbl_state">State</span></th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                    <!-- result 
                <table id='tblOrderList2' class="table  table-striped" data-page-size="10" data-filter="#filter">
                    <thead class="thead-light">
                        <tr>
                            <th scope="col" data-toggle="true" data-type="alpha" data-trad="lbl_event">Event</th>
                            <th scope="col" data-type="numeric" data-trad="lbl_order_number">Order number</th>
                            <th scope="col" data-type="numeric" data-trad="lbl_number_of_place">Number of places</th>
                            <th scope="col" data-type="numeric" data-trad="lbl_amount">Amount</th>
                            <th scope="col" data-type="alpha" data-trad="lbl_seance">Seance</th>
                            <th scope="col" data-type="alpha" data-trad="lbl_state">State</th>
                            <th scope="col" data-hide="all"></th>
                        </tr>
                    </thead>

                    <tbody />
                    <tfoot class="hide-if-no-paging">
                        <tr>
                            <td colspan="7" class="text-center">
                                <ul class="pagination pagination-centered">
                                </ul>
                            </td>
                        </tr>
                    </tfoot>

                </table>-->
                </div>
            </div>


        </div>
        <!-- historique commande END -->






    </div>



    
            


    <div class="row">


         <div id="collapseProductsHisto" class="collapse show" aria-labelledby="headingProducts">
                <div class="card-body">
                    <div class="row">
                        <table id="tblProductsList" class="table table-striped w-100" aria-describedby="products_list">
                            <thead class="thead-dark">
                                <tr role="row">
                                    <th><span data-trad="lbl_order_number_short">Amount</span></th>

                                    <th><span data-trad="lbl_products">Product name</span></th>
                                    <th><span data-trad="lbl_amount_of_product">Amount</span></th>
                                    
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
    </div>


    <div class="row">
        <div class="col-sm-6 mt-2 text-center text-sm-left">
            <a href="" class="btn btn-secondary buttonBack" data-trad="btn_back">Back</a>
        </div>
        <div class="col-sm-6 mt-2 text-center text-sm-right">
            <a href="" id="buttonCancel" class="btn btn-secondary showWhenWidget" data-trad="btn_close">Close</a>
        </div>
    </div>
    <!-- commentaires bas -->
    <div id="divCommentaireBas" class="small">
        <asp:Literal ID="litForCommentaireBas" runat="server"></asp:Literal>
    </div>
    <asp:HyperLink ID="hlHomePage" runat="server" class='d-none'>homePage</asp:HyperLink>
    <div class="modal inmodal" id="modalDepot" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content animated flipInY">
                <div class="modal-header" id="dialogForPropositionTitle">
                    <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                </div>
                <div class="modal-body" id="dialogForPropositionDVInPop">
                </div>
                <div class="modal-footer">
                    <input type="hidden" id="montantRepriseFinalValue" />
 
                        <button type="button" class="btn btn-white" data-dismiss="modal" id="btnAnnulerDepot" data-trad="btn_cancel">Cancel</button>
                        <button type="button" class="btn btn-primary" disabled="disabled" id="btnNextDepot" data-trad="btn_next">Next</button>
                        
                        <button type="button" class="btn btn-white" id="btnBackDepot" data-trad="btn_back">Cancel</button>
                        <button type="button" class="btn btn-primary" id="btnValiderDepot" data-trad="btn_validate">Validate</button>

                    <!--<div class="row">
                    <div class="col-lg-6 text-left">
                        <button type="button" class="btn btn-white" data-dismiss="modal" id="btnAnnulerDepot" data-trad="btn_cancel">Cancel</button>
                    </div>
                    <div class="col-lg-6">
                        <button type="button" class="btn btn-primary" disabled="disabled" id="btnValiderDepot"  data-trad="btn_validate">OK</button>
                    </div>

                </div>-->
                </div>
            </div>
        </div>
    </div>
    <iframe id="iframe_forDownloadPdf" class="d-none"></iframe>
    <div class="modal inmodal" id="modalEdition" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content animated flipInY">
                <div class="modal-header" id="dialogForEditionTitle">
                    <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                </div>
                <div class="modal-body" id="dialogForEditionInPop">
                </div>
                <div class="modal-footer">
                    <div class="row">
                        <div class="col-lg-6 text-left">
                            <button type="button" class="btn btn-white" data-dismiss="modal" data-trad="btn_annuler">Cancel</button>
                        </div>
                        <div class="col-lg-6">
                            <button type="button" class="btn btn-primary" id="btnValiderEdition" data-trad="btn_valider">OK</button>
                        </div>
                        <div class="col-lg-6">
                            <button type="button" class="btn btn-primary" id="btnValiderEdition" data-trad="btn_valider">OK</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="./javascriptfiles/touchspin/jquery.bootstrap-touchspin.min.js"></script>
</asp:Content>