﻿<%@ Page Title="" Language="C#" MasterPageFile="~/pagemaster.Master" AutoEventWireup="true" CodeBehind="orders.aspx.cs" Inherits="customerArea.orders" %>

<asp:Content ID="Content1" ContentPlaceHolderID="ctPlaceHold" runat="server">
<asp:ScriptManager ID="ScriptManager1" runat="server" EnablePageMethods="True"></asp:ScriptManager>
<span  runat="server"  id="dvpflag"  ClientIDMode="Static"></span>


<!-- commentaire haut -->
<div id="commentsPageHaut"><asp:Literal ID="litForCommentaireHaut" runat="server"></asp:Literal></div>

<h2 data-trad="title_my_orders">My orders</h2>
<!-- historique commande START -->
<div id="orderListTopWrapper">
    <div id="orderFilterWrapper"></div>
</div>
<div id="orderListWrapper"></div>
<div id="orderListBottomWrapper">
    <div class="orderPaginationWrapper"></div>
</div>
<!-- historique commande END -->
<div class="row">
    <div class="col-sm-6 mt-2 text-center text-sm-left">
        <a href="" class="btn btn-secondary buttonBack" data-trad="btn_back">Back</a>
    </div>

    <div class="col-sm-6 mt-2 text-center text-sm-right">
        <a href="" id="buttonCancel" class="btn btn-secondary showWhenWidget" data-trad="btn_close">Close</a>
    </div>
</div>
<!-- commentaires bas -->
<div id="divCommentaireBas" class="small"><asp:Literal ID="litForCommentaireBas" runat="server" ></asp:Literal></div>

<asp:HyperLink ID="hlHomePage" runat="server" class='d-none'>homePage</asp:HyperLink>
<div class="modal inmodal" id="modalDepot" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content animated flipInY">
            <div class="modal-header" id="dialogForPropositionTitle">
                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>

            </div>
            <div class="modal-body" id="dialogForPropositionDVInPop">


            </div>
            <div class="modal-footer">
                <div class="row">
                    <div class="col-lg-6 text-left">
                        <button type="button" class="btn btn-white" data-dismiss="modal" id="btnAnnulerDepot" data-trad="btn_annuler">Cancel</button>

                    </div>
                    <div class="col-lg-6">
                        <button type="button" class="btn btn-primary" disabled="disabled" id="btnValiderDepot"  data-trad="btn_valider">OK</button>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>
<iframe id="iframe_forDownloadPdf" class="d-none"></iframe>
<div class="modal inmodal" id="modalEdition" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content animated flipInY">
            <div class="modal-header" id="dialogForEditionTitle">
                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>

            </div>
            <div class="modal-body" id="dialogForEditionInPop">


            </div>
            <div class="modal-footer">
                <div class="row">
                    <div class="col-lg-6 text-left">
                        <button type="button" class="btn btn-white" data-dismiss="modal" data-trad="btn_annuler">Cancel</button>
                    </div>
                    <div class="col-lg-6">
                        <button type="button" class="btn btn-primary" id="btnValiderEdition"  data-trad="btn_valider">OK</button>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>

</asp:Content>

