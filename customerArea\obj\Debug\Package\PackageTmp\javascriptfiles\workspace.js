
var customerAreaDomain = ""; //Domaine utilisé lorsque l'on ferme la popup (connexion)
var customerStructureId = ""; 
var customerDirectory = "";
var basUrl = ""
var unidyRedirectUrl = ""
$(document).ready(function() {
	GetWorkspace();
});


function GetWorkspace() {
	var thisWorkspace = settings.workspace;
	var thisStructureId = getStructure().trim();

	switch(thisWorkspace){
		case "dev": 
		customerAreaBaseDomain = "https://dev.themisweb.fr";
		customerStructureId = thisStructureId;
		customerDirectory = "customer";
		basUrl = ""
		unidyRedirectUrl = "https://dev.themisweb.fr/customer/login.aspx?idstructure="+thisStructureId;
		break;

		case "test": 
		customerAreaBaseDomain = "https://test.themisweb.fr";
		customerStructureId = thisStructureId;
		customerDirectory = "customerV3";
		basUrl = "/customerV3"
		unidyRedirectUrl = "https://test.themisweb.fr/customerV3/login.aspx?idstructure="+thisStructureId;
		break;

		case "prod": 
		customerAreaBaseDomain = "https://customer.themisweb.fr";
		customerStructureId = thisStructureId;
		customerDirectory = "customerV3";
		basUrl = "/"+thisStructureId
		break;


		default:
		customerAreaBaseDomain = "https://dev.themisweb.fr"; 
		customerStructureId = thisStructureId;
		customerDirectory = "customer";
		basUrl = "/customer"
		unidyRedirectUrl = "https://customer.themisweb.fr/"+thisStructureId+"/login.aspx?idstructure="+thisStructureId;
		break;

	}

 }

function getStructure() {
    var attstruct = ""
    if ($('#myhead').attr('structureid') != "") {
        attstruct = numToNDigitStr(parseInt($('#myhead').attr('structureid')) , 4)
    }else if($('body').attr('idstructure') != "") {
        attstruct = numToNDigitStr(parseInt($('#body').attr('structureid')) , 4) 
    }else {
        attstruct = "XX"
    }
    return attstruct.toString();
  
}

function getLang() {
    return $('#receptVar').data('lang');
}
