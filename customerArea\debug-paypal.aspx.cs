using System;
using System.Collections.Generic;
using System.Web.UI;
using Newtonsoft.Json;

namespace customerArea
{
    public partial class debug_paypal : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            string structureId = "991";
            string plateformCode = "";
            string lang = "fr";
            int resulEvent = 0;
            string idpa = "0";

            List<utilitaires2010.CustomJsonSettings> listJsonSettingsToMerge = new List<utilitaires2010.CustomJsonSettings>
            {
                new utilitaires2010.CustomJsonSettings() { KeyOfJsonPath = "physicalPathOfSettingsJSON", IsDefault = true },
                new utilitaires2010.CustomJsonSettings() { KeyOfJsonPath = "physicalPathOfSettingsJSON", IsDefault = false },
                new utilitaires2010.CustomJsonSettings() { KeyOfJsonPath = "physicalPathOfSettingsPlateformJSON", IsDefault = false }
            };

            try
            {
                // Affichage des settings dans les spans
                settingsCustomer.InnerText = JsonConvert.SerializeObject(utilitaires2010.Initialisations.LoadSettingsOfCustomer("physicalPathOfSettingsJSON", plateformCode, int.Parse(structureId), resulEvent, int.Parse(idpa), lang, false), Formatting.Indented);
                settingsDefaultCustomer.InnerText = JsonConvert.SerializeObject(utilitaires2010.Initialisations.LoadSettingsOfCustomer("physicalPathOfSettingsJSON", plateformCode, int.Parse(structureId), resulEvent, int.Parse(idpa), lang, true), Formatting.Indented);
            }
            catch (Exception ex)
            {
                Response.Write("Erreur: " + ex.Message);
            }
        }
    }
}
