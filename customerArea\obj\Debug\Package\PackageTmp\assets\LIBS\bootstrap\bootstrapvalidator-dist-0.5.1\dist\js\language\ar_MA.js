(function ($) {
    /**
     * Arabic language package
     * Translated by @Arkni
     */
    $.fn.bootstrapValidator.i18n = $.extend(true, $.fn.bootstrapValidator.i18n, {
        base64: {
            'default': 'الرجاء إدخال قيمة مشفرة طبقا للقاعدة 64.'
        },
        between: {
            'default': 'من فضلك إدخال قيمة بين %s و %s .',
            notInclusive: 'من فضلك إدخال قيمة بين %s و %s بدقة.'
        },
        callback: {
            'default': 'الرجاء إدخال قيمة صالحة.'
        },
        choice: {
            'default': 'الرجاء إدخال قيمة صالحة.',
            less: 'الرجاء اختيار %s خيارات كحد أدنى.',
            more: 'الرجاء اختيار %s خيارات كحد أقصى.',
            between: 'الرجاء إختيار %s-%s خيارات.'
        },
        creditCard: {
            'default': 'الرجاء إدخال رقم بطاقة إئتمان صحيح.'
        },
        cusip: {
            'default': 'الرجاء إدخال رقم CUSIP صالح.'
        },
        cvv: {
            'default': 'الرجاء إدخال رقم CVV صالح.'
        },
        date: {
            'default': 'الرجاء إدخال تاريخ صالح.'
        },
        different: {
            'default': 'الرجاء إدخال قيمة مختلفة.'
        },
        digits: {
            'default': 'الرجاء إدخال الأرقام فقط.'
        },
        ean: {
            'default': 'الرجاء إدخال رقم EAN صالح.'
        },
        emailAddress: {
            'default': 'الرجاء إدخال بريد إلكتروني صحيح.'
        },
        file: {
            'default': 'الرجاء إختيار ملف صالح.'
        },
        greaterThan: {
            'default': 'الرجاء إدخال قيمة أكبر من أو تساوي %s.',
            notInclusive: 'الرجاء إدخال قيمة أكبر من %s.'
        },
        grid: {
            'default': 'الرجاء إدخال رقم GRid صالح.'
        },
        hex: {
            'default': 'الرجاء إدخال رقم ست عشري صالح.'
        },
        hexColor: {
            'default': 'الرجاء إدخال رمز لون صالح.'
        },
        iban: {
            'default': 'الرجاء إدخال رقم IBAN صالح.',
            countryNotSupported: 'البلد ذو الرمز %s غير معتمد.',
            country: 'الرجاء إدخال رقم IBAN صالح في %s.',
            countries: {
                AD: 'أندورا',
                AE: 'الإمارات العربية المتحدة',
                AL: 'ألبانيا',
                AO: 'أنغولا',
                AT: 'النمسا',
                AZ: 'أذربيجان',
                BA: 'البوسنة والهرسك',
                BE: 'بلجيكا',
                BF: 'بوركينا فاسو',
                BG: 'بلغاريا',
                BH: 'البحرين',
                BI: 'بوروندي',
                BJ: 'بنين',
                BR: 'البرازيل',
                CH: 'سويسرا',
                CI: 'ساحل العاج',
                CM: 'الكاميرون',
                CR: 'كوستاريكا',
                CV: 'الرأس الأخضر',
                CY: 'قبرص',
                CZ: 'التشيك',
                DE: 'ألمانيا',
                DK: 'الدنمارك',
                DO: 'جمهورية الدومينيكان',
                DZ: 'الجزائر',
                EE: 'إستونيا',
                ES: 'إسبانيا',
                FI: 'فنلندا',
                FO: 'جزر فارو',
                FR: 'فرنسا',
                GB: 'المملكة المتحدة',
                GE: 'جورجيا',
                GI: 'جبل طارق',
                GL: 'جرينلاند',
                GR: 'اليونان',
                GT: 'غواتيمالا',
                HR: 'كرواتيا',
                HU: 'المجر',
                IE: 'أيرلندا',
                IL: 'إسرائيل',
                IR: 'إيران',
                IS: 'آيسلندا',
                IT: 'إيطاليا',
                JO: 'الأردن',
                KW: 'الكويت',
                KZ: 'كازاخستان',
                LB: 'لبنان',
                LI: 'ليختنشتاين',
                LT: 'ليتوانيا',
                LU: 'لوكسمبورغ',
                LV: 'لاتفيا',
                MC: 'موناكو',
                MD: 'مولدوفا',
                ME: 'الجبل الأسود',
                MG: 'مدغشقر',
                MK: 'جمهورية مقدونيا',
                ML: 'مالي',
                MR: 'موريتانيا',
                MT: 'مالطا',
                MU: 'موريشيوس',
                MZ: 'موزمبيق',
                NL: 'هولندا',
                NO: 'النرويج',
                PK: 'باكستان',
                PL: 'بولندا',
                PS: 'فلسطين',
                PT: 'البرتغال',
                QA: 'قطر',
                RO: 'رومانيا',
                RS: 'صربيا',
                SA: 'المملكة العربية السعودية',
                SE: 'السويد',
                SI: 'سلوفينيا',
                SK: 'سلوفاكيا',
                SM: 'سان مارينو',
                SN: 'السنغال',
                TN: 'تونس',
                TR: 'تركيا',
                VG: 'جزر العذراء البريطانية'
            }
        },
        id: {
            'default': 'الرجاء إدخال رقم هوية صالحة.',
            countryNotSupported: 'البلد ذو الرمز %s غير معتمد.',
            country: 'الرجاء إدخال رقم تعريف صالح في %s.',
            countries: {
                BA: 'البوسنة والهرسك',
                BG: 'بلغاريا',
                BR: 'البرازيل',
                CH: 'سويسرا',
                CL: 'تشيلي',
                CZ: 'التشيك',
                DK: 'الدنمارك',
                EE: 'إستونيا',
                ES: 'إسبانيا',
                FI: 'فنلندا',
                HR: 'كرواتيا',
                IE: 'أيرلندا',
                IS: 'آيسلندا',
                LT: 'ليتوانيا',
                LV: 'لاتفيا',
                ME: 'الجبل الأسود',
                MK: 'جمهورية مقدونيا',
                NL: 'هولندا',
                RO: 'رومانيا',
                RS: 'صربيا',
                SE: 'السويد',
                SI: 'سلوفينيا',
                SK: 'سلوفاكيا',
                SM: 'سان مارينو',
                ZA: 'جنوب أفريقيا'
            }
        },
        identical: {
            'default': 'الرجاء إدخال نفس القيمة.'
        },
        imei: {
            'default': 'الرجاء إدخال رقم IMEI صالح.'
        },
        imo: {
            'default': 'الرجاء إدخال رقم IMO صالح.'
        },
        integer: {
            'default': 'الرجاء إدخال رقم صحيح.'
        },
        ip: {
            'default': 'الرجاء إدخال عنوان IP صالح.',
            ipv4: 'الرجاء إدخال عنوان IPv4 صالح.',
            ipv6: 'الرجاء إدخال عنوان IPv6 صالح.'
        },
        isbn: {
            'default': 'الرجاء إدخال رقم ISBN صالح.'
        },
        isin: {
            'default': 'الرجاء إدخال رقم ISIN صالح.'
        },
        ismn: {
            'default': 'الرجاء إدخال رقم ISMN صالح.'
        },
        issn: {
            'default': 'الرجاء إدخال رقم ISSN صالح.'
        },
        lessThan: {
            'default': 'الرجاء إدخال قيمة أصغر من أو تساوي %s.',
            notInclusive: 'الرجاء إدخال قيمة أصغر من %s.'
        },
        mac: {
            'default': 'يرجى إدخال عنوان MAC صالح.'
        },
        meid: {
            'default': 'الرجاء إدخال رقم MEID صالح.'
        },
        notEmpty: {
            'default': 'الرجاء إدخال قيمة.'
        },
        numeric: {
            'default': 'الرجاء إدخال عدد عشري صالح.'
        },
        phone: {
            'default': 'الرجاء إدخال رقم هاتف صحيح.',
            countryNotSupported: 'البلد ذو الرمز %s غير معتمد.',
            country: 'الرجاء إدخال رقم هاتف صالح في %s.',
            countries: {
                BR: 'البرازيل',
                ES: 'إسبانيا',
                FR: 'فرنسا',
                GB: 'المملكة المتحدة',
                MA: 'المغرب',
                PK: 'باكستان',
                US: 'الولايات المتحدة'
            }
        },
        regexp: {
            'default': 'الرجاء إدخال قيمة مطابقة للنمط.'
        },
        remote: {
            'default': 'الرجاء إدخال قيمة صالحة.'
        },
        rtn: {
            'default': 'الرجاء إدخال رقم RTN صالح.'
        },
        sedol: {
            'default': 'الرجاء إدخال رقم SEDOL صالح.'
        },
        siren: {
            'default': 'الرجاء إدخال رقم SIREN صالح.'
        },
        siret: {
            'default': 'الرجاء إدخال رقم SIRET صالح.'
        },
        step: {
            'default': 'الرجاء إدخال قيمة من مضاعفات %s .'
        },
        stringCase: {
            'default': 'الرجاء إدخال أحرف صغيرة فقط.',
            upper: 'الرجاء إدخال أحرف كبيرة فقط.'
        },
        stringLength: {
            'default': 'الرجاء إدخال قيمة ذات طول صحيح.',
            less: 'الرجاء إدخال أقل من %s حرفا.',
            more: 'الرجاء إدخال أكتر من %s حرفا.',
            between: 'الرجاء إدخال قيمة ذات عدد حروف بين %s و %s حرفا.'
        },
        uri: {
            'default': 'الرجاء إدخال URI صالح.'
        },
        uuid: {
            'default': 'الرجاء إدخال رقم UUID صالح.',
            version: 'الرجاء إدخال رقم UUID صالح إصدار %s.'
        },
        vat: {
            'default': 'الرجاء إدخال رقم VAT صالح.',
            countryNotSupported: 'البلد ذو الرمز %s غير معتمد.',
            country: 'الرجاء إدخال رقم VAT صالح في %s.',
            countries: {
                AT: 'النمسا',
                BE: 'بلجيكا',
                BG: 'بلغاريا',
                BR: 'البرازيل',
                CH: 'سويسرا',
                CY: 'قبرص',
                CZ: 'التشيك',
                DE: 'جورجيا',
                DK: 'الدنمارك',
                EE: 'إستونيا',
                ES: 'إسبانيا',
                FI: 'فنلندا',
                FR: 'فرنسا',
                GB: 'المملكة المتحدة',
                GR: 'اليونان',
                EL: 'اليونان',
                HR: 'كرواتيا',
                HU: 'المجر',
                IE: 'أيرلندا',
                IS: 'آيسلندا',
                IT: 'إيطاليا',
                LT: 'ليتوانيا',
                LU: 'لوكسمبورغ',
                LV: 'لاتفيا',
                MT: 'مالطا',
                NL: 'هولندا',
                NO: 'النرويج',
                PL: 'بولندا',
                PT: 'البرتغال',
                RO: 'رومانيا',
                RU: 'روسيا',
                RS: 'صربيا',
                SE: 'السويد',
                SI: 'سلوفينيا',
                SK: 'سلوفاكيا',
                ZA: 'جنوب أفريقيا'
            }
        },
        vin: {
            'default': 'الرجاء إدخال رقم VIN صالح.'
        },
        zipCode: {
            'default': 'الرجاء إدخال رمز بريدي صالح.',
            countryNotSupported: 'البلد ذو الرمز %s غير معتمد.',
            country: 'الرجاء إدخال رمز بريدي صالح في %s.',
            countries: {
                BR: 'البرازيل',
                CA: 'كندا',
                DK: 'الدنمارك',
                GB: 'المملكة المتحدة',
                IT: 'إيطاليا',
                MA: 'المغرب',
                NL: 'هولندا',
                SE: 'السويد',
                SG: 'سنغافورة',
                US: 'الولايات المتحدة'
            }
        }
    });
}(window.jQuery));
