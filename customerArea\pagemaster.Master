<%@ Master Language="C#" AutoEventWireup="true" CodeBehind="pagemaster.master.cs" Inherits="customerArea.pagemaster" %>

<!doctype html>
<% 
    ws_DTO.IdentiteEntity myI = new ws_DTO.IdentiteEntity();
    if (System.Web.HttpContext.Current.Session["userjs"] != null)
    {
        myI = (ws_DTO.IdentiteEntity)System.Web.HttpContext.Current.Session["userjs"];
        myI.Password = "";
        myI.PasswordCrypt = "";
    }
%>
<html xmlns="http://www.w3.org/1999/xhtml" lang="<%=System.Web.HttpContext.Current.Session["SVarLangue"] %>">
<head runat="server" id="myhead">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <!--<meta name="viewport" content="width=device-width, maximum-scale=1, initial-scale=1, user-scalable=0">-->
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title></title>
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.1.0/css/bootstrap.min.css" integrity="sha384-9gVQ4dYFwwWSjIDZnLEWnxCjeSWFphJiwGPXr1jddIhOegiu1FwO5qRGvFXOdJZ4" crossorigin="anonymous">
    <!-- Fonts CSS -->
    <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700,800" rel="stylesheet">
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.2.0/css/all.css" integrity="sha384-hWVjflwFxL6sNzntih27bfxkr27PmbbK/iSvJ+a4+0owXq79v+lsFkW54bOGbiDQ" crossorigin="anonymous">
    <!-- JQUERY -->
    <script src="https://code.jquery.com/jquery-3.3.1.min.js" integrity="sha256-FgpCb/KJQlLNfOu91ta32o/NMZxltwRo8QtmkMRdAu8=" crossorigin="anonymous"></script>
    <!-- Internationnal Tel input -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/12.1.9/css/intlTelInput.css" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/12.1.9/js/intlTelInput.js"></script>
    <!-- GOOGLEMAPS API 
    <script src="https://maps.googleapis.com/maps/api/js?v=3.exp&libraries=places"></script>-->
    <script src="https://maps.googleapis.com/maps/api/js?sensor=false&libraries=places&key=AIzaSyB3GgRiYX1o93BkSy7DmqTA-bNTqGYQXYE"></script>

    <!-- CAPTCHA INVISIBLE -->
    <script src="https://www.google.com/recaptcha/api.js"></script>
    <!--datatables for BS4 -->
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/v/bs4/dt-1.10.16/datatables.min.css" />
    <script type="text/javascript" src="https://cdn.datatables.net/v/bs4/dt-1.10.16/datatables.min.js"></script>
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/v/bs4/jszip-2.5.0/af-2.2.2/b-1.5.1/b-colvis-1.5.1/b-html5-1.5.1/b-print-1.5.1/cr-1.4.1/fc-3.2.4/fh-3.1.3/kt-2.3.2/r-2.2.1/rr-1.2.3/sc-1.4.4/datatables.min.css" />
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.32/pdfmake.min.js"></script>
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.32/vfs_fonts.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/v/bs4/jszip-2.5.0/af-2.2.2/b-1.5.1/b-colvis-1.5.1/b-html5-1.5.1/b-print-1.5.1/cr-1.4.1/fc-3.2.4/fh-3.1.3/kt-2.3.2/r-2.2.1/rr-1.2.3/sc-1.4.4/datatables.min.js"></script>
    <!-- choosen-->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/chosen/1.8.5/chosen.jquery.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/chosen/1.8.5/chosen.min.css" />
    <!-- MOMENT -->
    <script src="/assets/LIBS/moment-develop/min/moment-with-locales.js" type="text/javascript"></script>
    <script src="/assets/LIBS/bootstrapvalidator/moment/moment-with-locales.js" type="text/javascript"></script>
    <!-- cookie -->
    <script type="text/javascript" src="./assets/libs/jquery/jquery.cookie.js"></script>
    <!-- price format -->
    <script type="text/javascript" src="./assets/libs/jquery/price_format/jquery.price_format.2.0.js"></script>
    <!-- jquery MASK -->
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jquery.mask/1.14.15/jquery.mask.min.js"></script>
    <!-- Price format -->
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jquery-price-format/2.2.0/jquery.priceformat.min.js"></script>

    <script src="./javascriptfiles/workspace.js"></script>
    <!--commons-->
    <script src="./javascriptfiles/commons.js"></script>
    <script src="./javascriptfiles/traduction_commune.js"></script>
    <!-- THEMIS LIBS -->
    <link runat="server" id="StyleLink" rel="stylesheet" type="text/css" />
    <link type="text/css" href="https://www.themisweb.fr/assets/phoca-flags/css/phoca-flags.custom.min.css" rel="stylesheet" />
    <!-- Style CSS generic -->
    <link rel="stylesheet/less" type="text/css" href="css/style.less" />
    <!-- PayPal button fix CSS -->
    <link rel="stylesheet" type="text/css" href="css/paypal-button-fix.css" />
    <script src="//cdnjs.cloudflare.com/ajax/libs/less.js/3.0.0/less.min.js"></script>

</head>

<body class="top-navigation">

    <span id='receptVar' class='hide' data-lang="<%=System.Web.HttpContext.Current.Session["SVarLangue"] %>"
        data-structureid="<%=System.Web.HttpContext.Current.Session["idstructure"] %>"
        data-iid="<%=System.Web.HttpContext.Current.Session["SVarUserIdentityID"] %>"
        data-sessid="<%=System.Web.HttpContext.Current.Session.SessionID %>"
        data-curriduser="<%=System.Web.HttpContext.Current.Session["currIdUser"]%>"
        data-acompte="<%=System.Web.HttpContext.Current.Session["SVarUserAcompteSum"]%>"></span>


    <!-- main content START -->
    <svg version="1.1" id="loadingcircle" class="loadingcircle" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 25.8 25.6" style="enable-background: new 0 0 25.8 25.6;" xml:space="preserve">
        <g id="greycircle">
            <path class="st0" d="M16.4,6.4c0,0,5.4,2.9,4.8,9.2c-0.4,3.1-1.5,4.5-1.5,4.5s1.8-3.8,0.9-7.3C19.8,9,16.4,6.4,16.4,6.4z" />
            <path class="st0" d="M2,15.5c0,0-2.4-4.9,1.5-10.1c5-6.7,12.7-3.4,12.7-3.4S9.3,0,4.7,5.7S2,15.5,2,15.5z" />
            <path class="st0" d="M10.7,4c0,0,4.9-1,8.7,2.5c4.2,3.8,3.5,8.9,3.5,8.9s0.6-5.4-4.3-8.8C14.7,3.4,10.7,4,10.7,4z" />
            <path class="st0" d="M2.6,12.2c0,0,0.1,8,8.7,9.6c7.8,1.2,10.6-3.1,10.6-3.1S18.6,25,9.8,22.9C2.1,20.4,2.6,12.2,2.6,12.2z" />
            <path class="st1" d="M7.2,5.5" />
            <path class="st0" d="M7.1,5.7C7.1,5.7,7.1,5.7,7.1,5.7c-0.3,0.4-2.8,3.2-2.6,6.4c-0.1,5.5,3.3,8.2,3.3,8.2s-2.9-2.8-3-8
                C4.9,8.2,7.1,5.7,7.1,5.7z" />
        </g>
        <g id="colorcircle">
            <path class="st2" d="M4.9,6.3C4.9,6.3,9,0,17.3,3c6,2.5,6.3,8.7,6.3,8.7s-1.3-6.3-7.7-8C8.8,1.8,4.9,6.3,4.9,6.3z" />
            <path class="st2" d="M3.3,11.8c0,0,0.1-2.7,2.2-5.1c2.1-2.4,5.1-2.9,5.1-2.9s-2.9,0.7-4.7,3C3.6,9.2,3.3,11.8,3.3,11.8z" />
            <path class="st2" d="M1.3,14c0,0,0,5.5,4.3,8.5c4.3,3,9.3,1.4,9.3,1.4s-3.3,1.2-8.4-1.9C1.2,18.4,1.3,14,1.3,14z" />
            <path class="st2" d="M23.5,9c0,0,2.8,5.1-1,10.4c-2.1,2.9-4.1,3.5-4.1,3.5s4-2.5,5.1-6.5C24.8,12.2,23.5,9,23.5,9z" />
            <path class="st2" d="M19.9,15.5c0,0,0.7,4-3,6.6c-2,1.4-3.5,1.3-3.5,1.3s3.2-0.7,4.8-3.1C20,17.9,19.9,15.5,19.9,15.5z" />
        </g>
    </svg>

    <div id="wrap" class="container-fluid">
        <div class="row" id="headerTemplate" runat="server">
            
        </div>
        <header class="row hideWhenWidget" id="header">
            <nav class="col-12 navbar navbar-expand-lg">
                <a class="navbar-brand" href="" data-trad="lbl_customer_area">Customer Area</a>
                <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"><i class="fa fa-bars" aria-hidden="true"></i></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav" runat="server">
                    <ul class="navbar-nav mr-auto">
                        <li runat="server" id="liUpdateprofil" class="nav-item" data-pagename="myprofil">
                            <asp:Literal ID="LitupdateProfil" runat="server"></asp:Literal></li>
                        <li runat="server" id="liHistorique" class="nav-item" data-pagename="myorders">
                            <asp:Literal ID="LitHistoProfil" runat="server"></asp:Literal></li>
                        <li runat="server" id="liResalist" class="nav-item" data-pagename="myreservations">
                            <asp:Literal ID="LitResaList" runat="server"></asp:Literal></li>
                        <li runat="server" id="liBankDetail" class="nav-item" data-pagename="mybankdetails">
                            <asp:Literal ID="LiBankDetailProfil" runat="server"></asp:Literal></li>
                        <li runat="server" id="liTransactions" class="nav-item" data-pagename="mytransactions">
                            <asp:Literal ID="liTransactionsProfil" runat="server"></asp:Literal></li>
                        <li runat="server" id="liWaitList" class="nav-item" data-pagename="mywaitinglist">
                            <asp:Literal ID="liWaitListPage" runat="server"></asp:Literal></li>
                        <li runat="server" id="liConsumersList" class="nav-item" data-pagename="myconsumerslist">
                            <asp:Literal ID="liConsumersListPage" runat="server"></asp:Literal></li>
                        <li runat="server" id="liFans" class="nav-item" data-pagename="myreabofans">
                            <asp:Literal ID="LiFansProfil" runat="server"></asp:Literal></li>
                        <li runat="server" id="liAttachments" class="nav-item" data-pagename="myattachments">
                            <asp:Literal ID="liAttachmentsPage" runat="server"></asp:Literal></li>
                         <li runat="server" id="liAdhesions" class="nav-item" data-pagename="myadhesions">
                            <asp:Literal ID="liAdhesionsPage" runat="server"></asp:Literal></li>
                        
                        <li runat="server" id="liOrdersW" class="nav-item" data-pagename="myadhesions">
                            <asp:Literal ID="liOrdersWPage" runat="server"></asp:Literal></li>
                        <li runat="server" id="liTickets" class="nav-item" data-pagename="myadhesions">
                            <asp:Literal ID="liTicketsPage" runat="server"></asp:Literal></li>


                    </ul>
                    <ul class="nav nav-pills justify-content-center" id="navbarRight">
                    </ul>

                    <% 
                        ws_DTO.IdentiteEntity myI = new ws_DTO.IdentiteEntity();
                        if (System.Web.HttpContext.Current.Session["userjs"] != null)
                        {
                            myI = (ws_DTO.IdentiteEntity)System.Web.HttpContext.Current.Session["userjs"];
                            myI.Password = "";
                            myI.PasswordCrypt = ""; %>
                    <ul class="nav nav-pills justify-content-center">
                        <span class="d-none d-md-inline-block">|&nbsp;</span>
                        <span data-trad="lbl_customer_balance_dot">Mon solde client :</span>&nbsp;<strong data-priceformat="<%=myI.Acompte%>"><%=myI.Acompte%></strong>
                        <span class="d-none d-md-inline-block">&nbsp;|</span>
                    </ul>
                    <%
                        }
                    %>
                    <ul class="navbar-nav text-center">
                        <li runat="server" id="liDisconnectprofil">
                            <asp:Literal ID="LitdisconnectProfil" runat="server"></asp:Literal>
                        </li>
                    </ul>
                </div>
            </nav>
        </header>
        <form id="form1" runat="server">
            <div class="container" id="maincontent">
                <div class="row">
                    <div class="col-12">
                        <asp:Image ID="banner" runat="server" ImageUrl="~/App_Themes/default/banner.gif" meta:resourcekey="imgBannerResource1" />
                        <div id="msgNotificationAlert" class="row">
                        </div>
                        <asp:ContentPlaceHolder ID="ctPlaceHold" runat="server"></asp:ContentPlaceHolder>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <footer class="container-fluid hideWhenWidget" id="footer">
        <div class="row">
            <div class="container">
                <div class="row" id="footerTemplate" runat="server">
            
                 </div>
                <div class="row">
                    <div class="col col-md-6">&copy; Rodrigue <%= DateTime.Now.Year.ToString("0000") %></div>
                    <div class="col col-md-6 text-right">v<% =System.Web.Configuration.WebConfigurationManager.AppSettings["Version"]%></div>



                </div>
            </div>
        </div>
    </footer>

    <div class="alert alert-success fade fixed-top" id="alertSuccess" role="alert">
        <h4 class="alert-heading mb-0"></h4>
        <p class="mb-0"></p>
    </div>
    <div class="alert alert-danger fade fixed-top" id="alertError" role="alert">
        <h4 class="alert-heading mb-0"></h4>
        <p class="mb-0"></p>
    </div>

    <!-- MODAL GENERIC-->
    <div class="modal fade" tabindex="-1" role="dialog" id="modalGeneric" aria-labelledby="modalGenericLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalGenericLabel"></h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>

            </div>
        </div>
    </div>
    <!-- Modals -->
    <div class="modal inmodal" id="modalForMessage" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content animated flipInY">
                <div class="modal-header" id="modalForMessageHeader">
                    <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                    <!--<h4 class="modal-title">Modal title</h4>-->
                </div>
                <div class="modal-body" id="modalForMessageBody">
                </div>
            </div>
        </div>
    </div>



    <div class="modal inmodal" id="modalForWaiting" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-sm">
            <div class="modal-content animated fadeIn">
                <div class="modal-header">
                    <!--<button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                        <i class="fa fa-laptop modal-icon"></i>-->
                    <h4 class="modal-title">
                        <label data-trad="lbl_traitement_demande"></label>
                    </h4>
                </div>
                <div class="modal-body">
                    <!-- spinner wait -->
                    <div class="spiner-example">
                        <div class="sk-spinner sk-spinner-wave">
                            <div class="sk-rect1"></div>
                            <div class="sk-rect2"></div>
                            <div class="sk-rect3"></div>
                            <div class="sk-rect4"></div>
                            <div class="sk-rect5"></div>
                        </div>
                    </div>
                </div>


                <div id="myProgressbar" class="progress hide progress-bar-success progress-striped active">
                    <div aria-valuemax="100" aria-valuemin="0" aria-valuenow="0" role="progressbar" class="progress-bar progress-bar-success progress-striped active">
                        <span class="sr-only">40% Complete (success)</span>
                    </div>
                </div>

            </div>
        </div>
    </div>



    <!-- Modal permettant s'afficher les message apres une action delete, insert ... -->
    <div class="modal fade" id="modalMessage" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title" id="myModalLabel"></h4>
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                </div>
                <div class="modal-body">
                </div>
            </div>
        </div>
    </div>

    <span id="settingsPlateform" runat="server" class="d-none"></span>
    <span id="settingsCustomer" runat="server" class="d-none"></span>
    <span id="settingsDefaultCustomer" runat="server" class="d-none"></span>

    <!-- End Modal -->

    <!-- bootstrap -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.0/umd/popper.min.js" integrity="sha384-cs/chFZiN24E4KMATLdqdvsezGxaGsi4hLGOzlXwp5UZB1LY//20VyM2taTB4QvJ" crossorigin="anonymous"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js" integrity="sha384-JZR6Spejh4U02d8jOt6vLEHfe/JQGiRRSQQxSfFWpi1MquVdAyjUar5+76PVCmYl" crossorigin="anonymous"></script>
    <script src="./javascriptfiles/pagination/jquery.bootpag.min.js"></script>
    <!-- font awesome-->
    <script src="https://use.fontawesome.com/releases/v5.2.0/js/all.js" integrity="sha384-4oV5EgaV02iISL2ban6c/RmotsABqE4yZxZLcYMAdG7FAPsyHYAPpywE9PJo+Khy" crossorigin="anonymous"></script>
    <script src="/assets/LIBS/xml2json/xml2json.js"></script>

    <script type="text/javascript">
        var settings = "";
        if (($('#settingsCustomer').text() != "" || $('#settingsPlateform').text() != "") && $('#settingsDefaultCustomer').text() != "") {
            settings = $.extend(true, {}, JSON.parse($('#settingsDefaultCustomer').text()), JSON.parse($('#settingsCustomer').text()), JSON.parse($('#settingsPlateform').text()));

            less.modifyVars({
                '@primaryColor': settings.global.primaryColor,
                '@secondaryColor': settings.global.secondaryColor,
                '@tertiaryColor': settings.global.tertiaryColor,
                '@bannerColor': settings.global.bannerColor
            });
        }
        less.pageLoadFinished.then(function () {
            resizeParentIframe();
        })
        <% if (System.Web.HttpContext.Current.Session["SVarMyResponse"] != null)
        {
            Response.Write("var myIdentityObject = " + Newtonsoft.Json.JsonConvert.SerializeObject(myI));
        }
        %>
</script>
    <asp:ContentPlaceHolder ID="ExtraScripts" runat="server"></asp:ContentPlaceHolder>

</body>
</html>
