var nbSuccess = 0,
    nbError = 0,
    filenameError = [],
    totalsizesent = 0;
//var isSentDocuments = false;
var logsfilesAlreadyLoaded = false
$(document).ready(function () {
    //resize l'iframe parent
    resizeParentIframe();
    $('#myDropzone').addClass("dropzone");
    $('.MaxSizeAttachment').html(ReadXmlTranslate("msg_size_file_max").replace("[MaxSizeFiles]", "<strong>" + FileConvertSize(MaxSizeAttachment) + "</strong>"))

    var previewNode = document.querySelector("#template");
    previewNode.id = "";
    var previewTemplate = previewNode.parentNode.innerHTML;
    // previewNode.parentNode.removeChild(previewNode);

    //Dropzone.autoDiscover = false;
    Dropzone.options.myDropzone = {
        // The camelized version of the ID of the form element
        // The configuration we've talked about above
        url: "attachments.aspx",
        acceptedFiles: ".jpg,.jpeg,.png,.gif,application/pdf",
        autoDiscover: false,
        thumbnailWidth: 60,
        thumbnailHeight: 60,
        previewTemplate: previewTemplate,
        previewsContainer: "#previews", // Define the container to display the previews*/
        autoProcessQueue: false,
        uploadMultiple: true,
        parallelUploads: 1,
        maxFiles: 100,
        dictResponseError: "Server responded with {{statusCode}} code.",
        removedfile: function (file) {
            //console.log('DELETE FILE' + file.name)
            /*var isfiledeleted = deleteImage(file);

            if (isfiledeleted.split(':')[0] == "danger")
                showAlertError('', ReadXmlTranslate(isfiledeleted.split(':')[1]), '')*/
            //delete la ligne
            $(file.previewElement).remove();


            /*totalsizesent -= file.upload.bytesSent;
            nbSuccess--;*/
            if ($.inArray(filenameError, file.name) != -1) {
                filenameError.splice($.inArray(filenameError, file.name), 1)
            }
            resumeFiles();
            isSizeExceeded()
            //console.log("nb " + nb + " total size " + totalsizesent)
            //return (_ref = file.previewElement) != null ? _ref.parentNode.removeChild(file.previewElement) : void 0;

        },
        //addRemoveLinks: true,
        // The setting up of the dropzone
        init: function () {
            var myDropzone = this;
            this.on("success", function (file, responseText) {
                // Handle the responseText here. For example, add the text to the preview element:
                $('#feedback').html('')
                $(file.previewElement).find('.progress-bar').addClass('bg-success');
                totalsizesent += file.upload.bytesSent;
                nbSuccess++;
            });
            this.on("error", function (file, response) {
                // Handle the responseText here. For example, add the text to the preview element:
                //alert(response);
                if (file.xhr.status == 600) {
                    $(file.previewElement.querySelectorAll("[data-dz-errormessage]")[0]).text(ReadXmlTranslate("msg_size_file_exceeded"));
                }

                file.previewElement.classList.add("dz-error");
                nbError++;
                filenameError.push(file.name);
                // file.xhr.response = "test";
                // file.previewElement.querySelectorAll("[data-dz-errormessage]");
            });

            this.on('thumbnail', function (file) {
                /* if (verifFile()) {
                     file.rejectDimensions();
                 }
                 else {*/
                console.log(file)
                console.log(file.acceptDimensions())
                file.acceptDimensions();
                //}
            });
            this.on("complete", function (file) {
                //console.log(file);
                //nsole.log(file.upload.progress);
                //onsole.log(file.upload.bytesSent);
                //if($(file.previewElement).hasClass('dz-success')) {

                if (file.xhr.status == 600)
                    $(file.previewElement).find('.progress-bar').addClass('bg-danger')
                else
                    $(file.previewElement).find('.progress-bar').addClass('bg-success');
                resumeFiles();
                isSizeExceeded()

                //console.log("nb " + nb + " total size " + totalsizesent)
                //}
                //$('#'+ $(file.previewElement).find("[data-dzc-id]").attr('id')).html(file.upload.bytesSent);
            });
        },
        accept: function (file, done) {


            var thumbnail = $('.dropzone .dz-preview.dz-file-preview .dz-image:last');

            file.acceptDimensions = done;
            file.rejectDimensions = function () {
                done('The image must be at least 640 x 480px')
            };

            switch (file.type) {
                case 'application/pdf':
                    thumbnail.css('background', 'url(img/pdf.png');
                    break;
                case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
                    thumbnail.css('background', 'url(img/doc.png');
                    break;
            }

            done();
        },
        complete: function () {
            if (this.getQueuedFiles().length <= 0) {
                SendDocuments();
            } else {
                myDropzone.processQueue()
            }

        }
    }
    var myDropzone = new Dropzone('#myDropzone');



    myDropzone.on('addedfile', function (file) {

        var ext = file.name.split('.').pop();

        if (ext == "pdf") {
            $(file.previewElement).find(".dz-image img").attr("src", "/Content/Images/pdf.png");
        } else if (ext.indexOf("doc") != -1) {
            $(file.previewElement).find(".dz-image img").attr("src", "/Content/Images/word.png");
        } else if (ext.indexOf("xls") != -1) {
            $(file.previewElement).find(".dz-image img").attr("src", "/Content/Images/excel.png");
        }
    });


    //click bouton valider
    $('#btnValiderAttachments').on('click', function (e) {
        e.preventDefault();
        nbSuccess = 0
        nbError = 0
        filenameError = []
        totalsizesent = 0
        myDropzone.processQueue()
        //SendDocuments();
    });
    if (settings.attachments.logsInfoCompId != -1) {
        $('#getHistoAttachementsWrapper').show()
    }
    $('#getHistoAttachements').click(function () {
        if ($("#getHistoAttachementsCollapse").hasClass("show")) {
            $("#getHistoAttachementsCollapse").collapse('hide')
        } else {
            if (!logsfilesAlreadyLoaded) {
                loadLogsFiles(true);
            } else {
                $("#getHistoAttachementsCollapse").collapse('show')
            }
        }
    })
});



function loadLogsFiles(willOpen) {
    $.ajax({
        type: "POST",
        url: 'attachments.aspx/LoadLogsAttachments',
        contentType: 'application/json; charset=utf-8',
        dataType: "json",
        async: false,
        success: function (response) {
            logsfilesAlreadyLoaded = true
            writeLogsFiles(willOpen, response.d)
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            showAlertError('', XMLHttpRequest.responseJSON.Message + "\r\n" + XMLHttpRequest.responseJSON.StackTrace, '')
        }
    });
}

function writeLogsFiles(willOpen, data) {
    //console.log(data)
    var html = ""
    if (data.length > 0) {

        $.each(data, function (i, k) {
            $templateLogs = $("#templateHistoAttachments").clone();
            $($templateLogs).find('.histoAtatchmentsDate').html(k.ValeurParam1.trim())
            $($templateLogs).find('.histoAtatchmentsName').html(k.ValeurParam3.trim())
            html += $templateLogs.html()
        })
    } else {
        html += ReadXmlTranslate('msg_no_previous_attachment')
    }
    $('#getHistoAttachementsInner').html(html)
    if (willOpen != undefined && willOpen == true) {
        $('#getHistoAttachementsCollapse').collapse('show')
    }
    //$('#getHistoAttachementsCollapse')
}


/*function deleteImage(file) {
    var result = "";
    var sdata = JSON.stringify({
        "fileName": file.name
    });
    $.ajax({
        type: "POST",
        url: 'attachments.aspx/DeleteImage',
        contentType: 'application/json; charset=utf-8',
        dataType: "json",
        data: sdata,
        async: false,
        success: function(response) {
            result = response.d;
        },
        error: function(XMLHttpRequest, textStatus, errorThrown) {
            showAlertError('', XMLHttpRequest.responseJSON.Message + "\r\n" + XMLHttpRequest.responseJSON.StackTrace, '')
        }
    });
    return result;
}

//function BEFORE unload the page
$(window).on('beforeunload', function(e) {

    if ($('#previews .row.oneUpload').length > 0 && !isSentDocuments && !tryingtodisconnect) {
        return true;
    }
});

//function WHEN unload the page
$(window).on('unload', function() {
    // c'est ici qu'on vient delete les images

    deleteAllAttachments();

});

function deleteAllAttachments() {

    //supprime le dossier avec tous les fichier uploader
    $.ajax({
        type: "POST",
        url: 'attachments.aspx/DeleteImageFolder',
        contentType: 'application/json; charset=utf-8',
        dataType: "json",
        async: false,
        success: function(response) {
            console.log(response.d);
            //result = response.d;
        },
        error: function(XMLHttpRequest, textStatus, errorThrown) {
            showAlertError('', XMLHttpRequest.responseJSON.Message + "\r\n" + XMLHttpRequest.responseJSON.StackTrace, '')
        }
    });
}*/

//envoi tous les documents uploader a la structure
function SendDocuments() {
    if ($('#previews .row.oneUpload').length > 0) {
        $.ajax({
            type: "POST",
            url: 'attachments.aspx/SendDocuments',
            contentType: 'application/json; charset=utf-8',
            dataType: "json",
            async: false,
            success: function (response) {
                console.log(response.d);
                if (response.d.split(':')[0] == "success") {
                    //isSentDocuments = true;
                    isSizeExceeded()
                    $('#feedback').html('<div class="mt-3 alert alert-success text-center">' + ReadXmlTranslate(response.d.split(':')[1]) + '</div>')
                    $('#previews').html('')
                    loadLogsFiles();
                    //showAlertSuccess('', ReadXmlTranslate(response.d.split(':')[2]), '');
                } else {
                    showAlertError('', ReadXmlTranslate(response.d.split(':')[2]), '');
                }
                //result = response.d;
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                showAlertError('', XMLHttpRequest.responseJSON.Message + "\r\n" + XMLHttpRequest.responseJSON.StackTrace, '')
            }
        });

    }
}

//on affiche le résumé du total des fichiers uploadés
function resumeFiles() {
    html = ""
    if (nbError > 0) {
        html += "<div class='alert alert-danger' role='alert'>"
        html += ReadXmlTranslate("msg_total_files_uploaderror") + " <br/>" + filenameError.join('<br/>')
        html += "</div>"
    }

    if (nbSuccess >= 1) {
        //si plusieurs fichiers, on prend la traduction au pluriel, sinon singulier.
        var isManyFiles = (nbSuccess == 1) ? ReadXmlTranslate('msg_file') : ReadXmlTranslate('msg_files');

        html += "<div class='alert alert-secondary' role='alert'>"
        html += ReadXmlTranslate("msg_total_files_uploaded")
            .replace("[nbFilesUploaded]", "<strong>" + nbSuccess + "</strong>")
            .replace("[file_or_files]", isManyFiles)
            .replace("[sizeFilesUploaded]", "<strong>" + bytesToSize(totalsizesent) + "</strong>")
        html += "</div>"
    }
    $('#resume').html(html)
}

// check dans les fichiers s'il y a au moins une erreur (poids total dépassé)
function isSizeExceeded() {
    if ($('#previews').children().hasClass("dz-error")) {
        $('#feedback-error').html('<div class="alert alert-danger" role="alert">' + ReadXmlTranslate("msg_size_files_exceeded") + '</div>')
    } else {
        $('#feedback-error').html('')
    }
}

// retourne le poids du fichier
function bytesToSize(bytes) {
    var sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    if (bytes == 0) return '0 Byte';
    var i = parseInt(Math.floor(Math.log(bytes) / Math.log(1024)));
    return Math.round(bytes / Math.pow(1024, i), 2) + ' ' + sizes[i];
};

function FileConvertSize(aSize) {
    aSize = Math.abs(parseInt(aSize, 10));
    var def = [[1, 'octets'], [1024, 'ko'], [1024 * 1024, 'Mo'], [1024 * 1024 * 1024, 'Go'], [1024 * 1024 * 1024 * 1024, 'To']];
    for (var i = 0; i < def.length; i++) {
        if (aSize < def[i][0]) return (aSize / def[i - 1][0]).toFixed(2) + ' ' + def[i - 1][1];
    }
}