<!doctype html>
<html>
<head>
<link rel="stylesheet" type="text/css" href="../../../node_modules/bootstrap/dist/css/bootstrap.css">
<link rel="stylesheet" type="text/css" href="../../../build/css/bootstrap-datetimepicker.css">
<script src="../../../node_modules/jquery/dist/jquery.min.js"></script>
<script src="../../../node_modules/bootstrap/dist/js/bootstrap.js"></script>
<script src="../../../node_modules/moment/moment.js"></script>
<script src="../../../src/js/bootstrap-datetimepicker.js"></script>
<style>
.parent{
    position: relative;
    padding: 40px;
}
.helper{
    z-index: -1;
    position: absolute;
    background-color: #bfb;
    top: 0px;
    bottom: 0px;
    right: 0px;
    left: 0px;
    opacity: 0.3;
}
.inner{
    position: absolute;
    background-color: #bbf;
    top: 40px;
    bottom: 40px;
    right: 40px;
    left: 40px;
}
</style>
</head>
<body>
<div class="container">
    <div class="row">
        <div class="col-md-6 col-md-offset-3">
            <h1>{{v}} - {{h}}</h1>
            <br><br><br><br><br><br><br><br><br><br>
            <div class="parent">
                <div class="helper">
                    <div class="inner"></div>
                </div>
                <br><br><br><br><br>
                
                {{{t}}}

                <br><br><br><br><br>
            </div>
            <br><br><br><br><br><br><br><br><br><br><br><br>
        </div>
    </div>
</div>
<script type="text/javascript">
    $('[data-datetimepicker]').datetimepicker({
        widgetPositioning:{
            vertical: '{{v}}',
            horizontal: '{{h}}'
        }
    });
    $('input[data-click-target]').focus();
    $('span[data-click-target]').click();
</script>
</body>