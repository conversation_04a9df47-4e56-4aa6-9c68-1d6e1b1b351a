.jqzoom{
border:1px solid black;
float:left;
position:relative;
padding:0px;
cursor:pointer;
}

.jqzoom img{
float:left;
}

.mire_in_mire_div {
z-index : 10003; 
position                : absolute;
margin-top:0px; left:0px;
 }
.mire_div {
z-index                 : 101;
position                : absolute;
top:300px;
left:300px;
width                   : 200px;
height                  : 200px;
--background: red;
border:1px solid #CCCCCC;
--display:none;
text-align: center;
--overflow: hidden;
 }


div.zoomdiv {
z-index                 : 100;
position                : absolute;
top:0px;
left:0px;
width                   : 200px;
height                  : 200px;
background: #ffffff;
border:1px solid #CCCCCC;
display:none;
text-align: center;
overflow: hidden;


}

div.jqZoomPup {
z-index                 : 10;
visibility              : hidden;
position                : absolute;
top:0px;
left:0px;
width                   : 50px;
height                  : 50px;
border: 1px solid #aaa;
background: #ffffff url(../images/zoom.gif) 50% top  no-repeat;;
opacity: 0.5;
-moz-opacity: 0.5;
-khtml-opacity: 0.5;
filter: alpha(Opacity=50);
}
