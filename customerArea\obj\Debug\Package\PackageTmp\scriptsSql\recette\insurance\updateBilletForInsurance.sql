﻿
--declare @manif_id int = [EVENTID]--9
declare @seance_id int = [SESSIONID]--11
declare @entree_id int = [ENTREEID]--299
declare @dossier_id int = [DOSSIERID]--136
declare @insurance_company varchar(max) = '[INUSRANCECOMPANY]' --'INSURANCE_COMPAGNY'




declare @externe varchar(50) = (select externe from recette where  seance_id = @seance_id and entree_id = @entree_id and dossier_id = @dossier_id)

select numbillet, CONCAT(@insurance_company, @externe) from recette where seance_id = @seance_id and entree_id = @entree_id and dossier_id = @dossier_id

update recette set numbillet = 0, externe= CONCAT(@insurance_company, @externe)  where seance_id = @seance_id and entree_id = @entree_id and dossier_id = @dossier_id

