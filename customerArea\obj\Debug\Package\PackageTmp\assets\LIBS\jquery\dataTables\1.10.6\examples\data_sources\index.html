<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<link rel="shortcut icon" type="image/ico" href="http://www.datatables.net/favicon.ico">
	<meta name="viewport" content="initial-scale=1.0, maximum-scale=2.0">
	<link rel="stylesheet" type="text/css" href="../resources/syntax/shCore.css">
	<link rel="stylesheet" type="text/css" href="../resources/demo.css">
	<script type="text/javascript" language="javascript" src="../../media/js/jquery.js"></script>
	<script type="text/javascript" language="javascript" src="../resources/syntax/shCore.js"></script>
	<script type="text/javascript" language="javascript" src="../resources/demo.js"></script>

	<title>DataTables examples - Data sources</title>
</head>

<body class="dt-example">
	<div class="container">
		<section>
			<h1>DataTables example <span>Data sources</span></h1>

			<div class="info">
				<p>DataTables can obtain data from four different fundamental sources:</p>

				<ul class="markdown">
					<li>HTML document (DOM)</li>
					<li>Javascript (array / objects)</li>
					<li>Ajax sourced data with client-side processing</li>
					<li>Ajax sourced data with server-side processing</li>
				</ul>

				<p>Which of these options is used to populate the table with data depends upon how the table is initialised. The examples in this section show these four different
				data source types.</p>
			</div>
		</section>
	</div>

	<section>
		<div class="footer">
			<div class="gradient"></div>

			<div class="liner">
				<div class="toc">
					<div class="toc-group">
						<h3><a href="./index.html">Data sources</a></h3>
						<ul class="toc">
							<li><a href="./dom.html">HTML (DOM) sourced data</a></li>
							<li><a href="./ajax.html">Ajax sourced data</a></li>
							<li><a href="./js_array.html">Javascript sourced data</a></li>
							<li><a href="./server_side.html">Server-side processing</a></li>
						</ul>
					</div>
				</div>

				<div class="epilogue">
					<p>Please refer to the <a href="http://www.datatables.net">DataTables documentation</a> for full information about its API properties and methods.<br>
					Additionally, there are a wide range of <a href="http://www.datatables.net/extras">extras</a> and <a href="http://www.datatables.net/plug-ins">plug-ins</a>
					which extend the capabilities of DataTables.</p>

					<p class="copyright">DataTables designed and created by <a href="http://www.sprymedia.co.uk">SpryMedia Ltd</a> &#169; 2007-2015<br>
					DataTables is licensed under the <a href="http://www.datatables.net/mit">MIT license</a>.</p>
				</div>
			</div>
		</div>
	</section>
</body>
</html>