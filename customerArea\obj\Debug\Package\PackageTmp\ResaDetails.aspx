﻿<%@ Page Title="" Language="C#" MasterPageFile="~/pagemaster.Master" AutoEventWireup="true"
    CodeBehind="ResaDetails.aspx.cs" Inherits="customerArea.ResaDetails"
    Debug="true" %>

<asp:Content ID="Content1" ContentPlaceHolderID="ctPlaceHold" runat="server">

    <!-- commentaire haut -->
    <div id="commentsPageHaut">
        <asp:Literal ID="litForCommentaireHaut" runat="server"></asp:Literal>
    </div>
    <h2><span data-trad="title_reservation_summary">Récapitulatif de ma réservation N°</span> <%= OrderId.ToString("# ##0") %></h2>
    <%--<pre><%= EventsDump %></pre> --%>
    <%
        var ProductsSubTotalTTC = 0;
        var FeesSubTotalTTC = 0;
        var FeesDeliverySubTotalTTC = 0;
        var lstTauxTva = new List<dynamic>();
        var AmontOrderPaid = 0;
    %>
    <div id="orderDetailsListWrapper">
        <!-- recap content -->
        <div class="boxedBlock nopadding">
            <% 

                if (myOrderDump != null)
                {
                    foreach (var oneEvent in myOrderDump.ListDossiersSeats)
                    { %>


            <!-- one event -->
            <div class="oneSummaryLine oneEvent" data-eventid="<%= oneEvent.Event.EventId %>">
                <span class="eventName"><%= oneEvent.Event.EventName %></span>
                <div class="sessionsWrapper">
                    <%  
                        var oneSession = oneEvent.Session;
                        var oneSummaryLineTotalTTC = 0;
                    %>
                    <!-- session start -->
                    <div class="oneSession" data-sessionid="<%= oneSession.SessionId %>" data-eventtitle="<%= oneEvent.Event.EventName %>" data-eventstart="<%= oneSession.SessionStartDate %>" data-eventend="<%= oneSession.SessionStartDate %>" data-eventlieu="<%= oneSession.LieuName %>">
                        <div class="row">
                            <div class="col">
                                <div class="collapsed" data-toggle="collapse" data-target="#collapse<%= oneSession.SessionId %>" aria-expanded="false" aria-controls="collapse60">
                                    <% if (oneSession.IsShowSessionDate == true)
                                        { %>
                                    <div class="productSessionWrapper">
                                        <% if (oneSession.IsShowSessionHour == true)
                                            { %>
                                        <span class="productIcon"><i class="far fa-calendar-alt" aria-hidden="true"></i></span><span class="productSession" data-datetoformat="<%= oneSession.SessionStartDate %>" data-datelocale="year,monthlong,day,weekday,hour,minutes"><%= oneSession.SessionStartDate %></span>
                                        <% }
                                        else
                                        {%>
                                        <span class="productIcon"><i class="far fa-calendar-alt" aria-hidden="true"></i></span><span class="productSession" data-datetoformat="<%= oneSession.SessionStartDate %>" data-datelocale="year,monthlong,day,weekday"><%= oneSession.SessionStartDate %></span>
                                        <% } %>
                                    </div>
                                    <% } %>


                                    <div class="productLieuWrapper"><span class="productIcon"><i class="fas fa-map-marked-alt" aria-hidden="true"></i></span><span class="productLieu"><%= oneSession.LieuName %></span></div>
                                </div>
                            </div>
                            <%
                                var countSeat = 0;

                                foreach (var oneSeat in oneEvent.ListSeats)
                                {
                                    countSeat += 1;
                                    if (oneEvent.State == "B" || oneEvent.State == "P")
                                    {
                                        AmontOrderPaid += oneSeat.Price.UnitTTCAmount;
                                    }
                                    oneSummaryLineTotalTTC += oneSeat.Price.UnitTTCAmount;
                                    ProductsSubTotalTTC += oneSeat.Price.UnitTTCAmount;
                                }
                                /*
                                foreach (var oneCateg in oneSession.ListCategories ) {
                                    foreach (var onePrice in oneCateg.ListPrices) {
                                        countSeat += onePrice.ListSeats.Count +  onePrice.ListAboSeats.Count;
                                        foreach (var oneSeat in onePrice.ListSeats) {
                                            oneSummaryLineTotalTTC+=onePrice.UnitTTCAmount;
                                            ProductsSubTotalTTC+=onePrice.UnitTTCAmount;
                                            AmontOrderPaid+=onePrice.AmountPaid;
                                        }
                                        foreach (var oneSeat in onePrice.ListAboSeats) {
                                            oneSummaryLineTotalTTC+=onePrice.UnitTTCAmount;
                                            ProductsSubTotalTTC+=onePrice.UnitTTCAmount;
                                            AmontOrderPaid+=onePrice.AmountPaid;
                                        }
                                     }
                                }
                                */
                                var oneSummaryLineTotalHT = (int)(oneSummaryLineTotalTTC / (1 + (oneSession.Tva_taux / 100)));
                                lstTauxTva.Add(new { taux = oneSession.Tva_taux, montant = (oneSummaryLineTotalTTC - oneSummaryLineTotalHT) });
                            %>
                            <div class="col-auto text-right">
                                <div class="priceTotalWrapper">
                                    <span class="priceTotalHT"><span data-priceformat="<%= oneSummaryLineTotalHT %>"><%= oneSummaryLineTotalHT %></span> <span data-trad="lbl_without_taxes">HT</span></span>
                                    <span class="priceTotal" data-pricetocalcul="add" data-priceformat="<%= oneSummaryLineTotalTTC %>"><%= oneSummaryLineTotalTTC %></span>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="collapsed" data-toggle="collapse" data-target="#collapse<%= oneSession.SessionId %>" aria-expanded="false" aria-controls="collapse60">
                                    <div class="collapeIconWrapper">
                                        <span class="productDetailsCollapseBtn btn btn-outline-secondary"><span class="productIcon"><i class="fas fa-ticket-alt" aria-hidden="true"></i></span><span data-trad="btn_my_tickets_details">Détails des tickets</span> <span class="productCount">(<%= countSeat %>)</span></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- details start -->
                        <div class="productSeatsWrapper collapse" id="collapse<%= oneSession.SessionId %>">
                            <div class="row">
                                <div class="col">
                                    <%
                                        foreach (var oneSeat in oneEvent.ListSeats)
                                        {
                                            var onePrice = oneSeat.Price;

                                    %>
                                    <!--one seat start-->
                                    <div class="productDetailsWrapper">
                                        <div class="productDetails row align-items-center">

                                            <div class="col-12 col-lg text-center text-lg-left">
                                                <span class="productSeatDetail">
                                                    <%
                                                        if (oneSeat.isFreePlacement == true)
                                                        {
                                                    %>
                                                    <span data-trad="lbl_free_placement">Placement libre</span>
                                                    <% }
                                                    else
                                                    { %>
                                                    <span><span data-trad="lbl_rank">Rang</span> <%= oneSeat.Rank %>, <span data-trad="lbl_seat">Siège</span> <%=oneSeat.Seat %></span>
                                                    <%
                                                        }
                                                    %>
                                                </span>
                                                <span class="productSeatDetailZESC">
                                                    <span><%=oneSeat.Zone_name%></span><span><%=oneSeat.Floor_name%></span>
                                                    <span><%=oneSeat.Section_name%></span>
                                                </span>
                                            </div>
                                            <div class="col-12 col-lg text-center text-lg-left">
                                                <span class="productSeatDetailCoupleTarifCateg">
                                                    <span><%= onePrice.Price_name %></span><span><%=oneSeat.Category_Name%></span>
                                                </span>
                                            </div>
                                            <div class="col-12 col-lg-1 text-center text-lg-right">
                                                <div class="priceUnitWrapper"><span class="priceUnit" data-priceformat="<%= onePrice.UnitTTCAmount %>"><%= onePrice.UnitTTCAmount %></span></div>
                                            </div>

                                        </div>
                                    </div>
                                    <!-- one seat end -->
                                    <%
                                        }

                                    %>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- details end -->
            </div>
            <!-- session end -->
            <% }

                }%>
        </div>
        <!-- session wrapper end -->
    </div>
    <!-- one event end -->
    <%  
        var containsPRESA = false;

    %>


    <!-- one dossier produit -->
    <% foreach (var oneDProduct in myOrderDump.ListDossiersProduct)
        {

            if (oneDProduct.Product.TypeProduct == "PRESA")
            {
                containsPRESA = true;
            }

            var oneSession = oneDProduct.Product.Product_name;

            if (oneDProduct.State == "B" || oneDProduct.State == "P")
            {
                AmontOrderPaid += oneDProduct.TotalTTCAmount;
            }
            //   AmontOrderPaid+=oneSeat.Price.UnitTTCAmount;
            //oneSummaryLineTotalTTC+=oneDProduct.TotalTTCAmount;
            ProductsSubTotalTTC += oneDProduct.TotalTTCAmount;


    %>
    <div class="oneSummaryLine oneProduct" data-eventid="<%= oneDProduct.Product.ProduitId %>">
        <span class="productName"><%= oneDProduct.Product.Product_name %></span>
        <div class="sessionsWrapper">

            <div class="row">
                <div class="col">
                </div>
            </div>

            <div class="col-auto text-right">
                <div class="priceTotalWrapper">
                    <span class="priceTotal" data-pricetocalcul="add" data-priceformat="<%= oneDProduct.TotalTTCAmount %>"><%= oneDProduct.TotalTTCAmount %></span>
                </div>
            </div>
            <div class="col-12">
            </div>


        </div>
    </div>

    <%
        }
    %>



    <!-- total cmd start -->
    <div id="summaryTotalCmdWrapper">

        <div class="oneSummaryLine  backgroundWrapper bg-primary-12">
            <div class="row align-items-center">
                <div class="col">
                    <div data-trad="lbl_total_order">Total de la commande TTC</div>
                </div>
                <div class="col-4 col-md-3 text-right">
                    <div class="priceTotal" data-priceformat="<%=(ProductsSubTotalTTC + FeesSubTotalTTC + FeesDeliverySubTotalTTC)%>"><%=(ProductsSubTotalTTC + FeesSubTotalTTC + FeesDeliverySubTotalTTC)%></div>
                </div>
            </div>
        </div>
        <% if (AmontOrderPaid > 0)
            { %>
        <div class="oneSummaryLine  backgroundWrapper bg-primary-20">
            <div class="row align-items-center">
                <div class="col">
                    <div data-trad="lbl_already_paid">Déjà payé</div>
                </div>
                <div class="col-4 col-md-3 text-right">
                    <div class="priceTotal">-<span data-priceformat="<%=AmontOrderPaid%>"><%=AmontOrderPaid %></span></div>
                </div>
            </div>
        </div>
        <% } %>
        <div class="oneSummaryLine summaryLineTotalOrder backgroundBigWrapper bg-primary-80">
            <div class="row align-items-center">
                <div class="col">
                    <div data-trad="lbl_total_to_pay">Total à payer</div>
                </div>
                <div class="col-4 col-md-3 text-right">
                    <div class="priceTotal" data-priceformat="<%=(ProductsSubTotalTTC + FeesSubTotalTTC + FeesDeliverySubTotalTTC - AmontOrderPaid)%>"><%=(ProductsSubTotalTTC + FeesSubTotalTTC + FeesDeliverySubTotalTTC - AmontOrderPaid)%></div>
                </div>
            </div>
        </div>
        <div class="bg-primary-5" id="totalTVABigWrapper">
            <div class="row collapse" id="collapseDetailsTVA">
                <div class="col">
                    <%
                        var TauxTvaSubTotalTTC = 0;
                        var lstTauxTvaGrouped = (from oneTaux in lstTauxTva group oneTaux by oneTaux.taux into tauxGroup select new { taux = Convert.ToDecimal(string.Format("{0:0.#####}", tauxGroup.Key)), montant = tauxGroup.Sum(x => x.montant) }).ToList();

                        foreach (var oneTaux in lstTauxTvaGrouped)
                        {
                            if (oneTaux.taux > 0)
                            {
                    %>
                    <div class="oneSummaryLine">
                        <div class="row">
                            <div class="col">
                                <div><%= oneTaux.taux %>%</div>
                            </div>
                            <div class="col-4 col-md-3 text-right">
                                <div class="priceTotal" data-priceformat="<%= oneTaux.montant %>"><%= oneTaux.montant %></div>
                            </div>
                        </div>
                    </div>
                    <% 
                                TauxTvaSubTotalTTC += @oneTaux.montant;
                            }
                        }
                    %>
                </div>
            </div>
            <% if (TauxTvaSubTotalTTC > 0)
                { %>
            <div class="oneSummaryLine">
                <div class="row">
                    <div class="col">
                        <div data-toggle="collapse" data-target="#collapseDetailsTVA" aria-expanded="false" aria-controls="collapseDetailsTVA"><i class="collapseArrow collapseArrowUp fas fa-chevron-down"></i><span data-trad="lbl_including_taxes">dont TVA</span></div>
                    </div>
                    <div class="col-4 col-md-3 text-right">
                        <div class="priceTotal" data-priceformat="<%= TauxTvaSubTotalTTC %>"><%= TauxTvaSubTotalTTC %></div>
                    </div>
                </div>
            </div>
            <% } %>
        </div>
    </div>
    <!-- total cmd end-->
    </div>
    <!-- recap content end -->

    </div>
    <!-- btns start -->
    <div id="callToActionWrapper" class="bigBlockWrapper">
        <div class="row">
            <div class="col">
                <a href="ResaList.aspx?idstructure=<%= StructureId %>&lang=<%= LangCode %>" target="_self" class="btn btn-secondary" data-trad="btn_back_to_reservation_list">Revenir à la liste des réservations</a>
            </div>
            <% if (containsPRESA)
                { %>
            <div class="col text-right">
                <a href="ResaPay.aspx?idstructure=<%= StructureId %>&rid=<%= OrderId %>&lang=<%= LangCode %>" class="btn btn-primary" id="payOrder" data-trad="btn_pay_reservation">Payer cette réservation</a>
            </div>
            <% } %>
        </div>
    </div>
    <!-- btns end -->
    <!-- commentaires bas -->
    <div id="divCommentaireBas" class="small">
        <asp:Literal ID="litForCommentaireBas" runat="server"></asp:Literal>
    </div>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ExtraScripts" runat="server">
</asp:Content>
