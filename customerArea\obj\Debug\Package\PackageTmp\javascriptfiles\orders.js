$(document).ready(function() {

    getOrdersList();

    //resize l'iframe parent
    resizeParentIframe();

});



function getOrdersList() {

    $.ajax({
        type: "POST",
        url: 'Histo.aspx/GetOrdersList',
        contentType: 'application/json; charset=utf-8',
        dataType: "json",
        success: function (response) {
            datajson = JSON.parse(response.d);
            datajson.sort(predicate({
                name: 'Commande_id',
                reverse: true
            }));
            showOrders(datajson);
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            $('#tblOrderList2').show();
            showAlertError('', XMLHttpRequest.statusText, '')
        },
        complete: function () {
            resizeParentIframe();
        }
    });
}



function showOrders(data) {

    console.log(data);
}