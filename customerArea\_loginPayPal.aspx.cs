using App_Code;
using customerArea.classes;
using log4net;
using RestSharp;
using RestSharp.Authenticators;
using rodrigue.utilitaires.crypto.SHA1;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Web;
using System.Web.Configuration;
using System.Web.Script.Serialization;
using System.Web.UI;
using System.Web.UI.WebControls;
using customerArea.App_Code;
using customerArea.classes;
using utilitaires2010;
using ws_DTO.objets_liaisons;

namespace customerArea
{
    public partial class _loginPayPal : System.Web.UI.Page
    {
        private static readonly ILog logger = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        protected void Page_Load(object sender, EventArgs e)
        {
            try
            {
                // LOG CRITIQUE : Vérifier si cette page est appelée
               
                string codePP = "";
                if (Request.QueryString["code"] != null)
                {
                    codePP = Request.QueryString["code"];

                    System.Net.ServicePointManager.SecurityProtocol = System.Net.SecurityProtocolType.Tls12 | System.Net.SecurityProtocolType.Tls11 | System.Net.SecurityProtocolType.Tls11;

                // Utiliser les paramètres de configuration au lieu des valeurs codées en dur
                string urlTokenService = utilitaires2010.Initialisations.GetKeyAppSettings("paypalconnect_urltokenservice");

                var client = new RestClient(urlTokenService);
                var request = new RestRequest(Method.POST);

                string structureId = "0"; 

                // D'abord, essayer de récupérer depuis le paramètre state de PayPal
                string state = Request.QueryString["state"];
                if (!string.IsNullOrEmpty(state))
                {
                    logger.Debug("State PayPal reçu: " + state);
                    if (state.Contains("idstructure="))
                    {
                        var parts = state.Split('=');
                        if (parts.Length > 1)
                        {
                            structureId = parts[1];
                            logger.Debug("Structure ID extrait du state: " + structureId);
                        }
                    }
                }

                // Sinon, essayer depuis l'URL directe
                if (Request.QueryString["idstructure"] != null)
                {
                    structureId = Request.QueryString["idstructure"];
                    logger.Debug("Structure ID depuis URL: " + structureId);
                }

                // Mettre à jour la session avec l'ID de structure
                try
                {
                    System.Web.HttpContext.Current.Session["idstructure"] = int.Parse(structureId);
                    logger.Debug("Structure ID final utilisé: " + structureId);
                }
                catch (Exception ex)
                {
                    logger.Error("Erreur parsing structure ID: " + ex.Message);
                    structureId = "991"; // Fallback
                }
                string plateformCode = "Customer";
                string lang = "fr";
                int idpa = 0;
                int resulEvent = 0;

                // Récupérer les settings avec merge
                List<CustomJsonSettings> listJsonSettingsToMerge = new List<CustomJsonSettings>
                {
                    new CustomJsonSettings() { KeyOfJsonPath = "physicalPathOfSettingsJSON", IsDefault = true },
                    new CustomJsonSettings() { KeyOfJsonPath = "physicalPathOfSettingsJSON", IsDefault = false },
                    new CustomJsonSettings() { KeyOfJsonPath = "physicalPathOfSettingsPlateformJSON", IsDefault = false }
                };
                dynamic globalPlateform = utilitaires2010.Initialisations.GetMergeSettings(listJsonSettingsToMerge, int.Parse(structureId), resulEvent, idpa, plateformCode, lang);

                string userNamePayPal = "notSet-seeConfigIni";
                string passwordPayPal = "notSet-seeConfigIni";

                try
                {
                    // Récupération de l'appid PayPal (utilisé comme username)
                    dynamic paypalAppIdDynamic = globalPlateform.login.payPal.appid.Value;
                    if (globalPlateform.customer != null && globalPlateform.customer.login != null && globalPlateform.customer.login.payPal != null && globalPlateform.customer.login.payPal.appid != null)
                    {
                        paypalAppIdDynamic = globalPlateform.customer.login.payPal.appid.Value;
                    }
                    userNamePayPal = paypalAppIdDynamic;
                    logger.Debug("PayPal AppId récupéré: " + (userNamePayPal.Length > 10 ? userNamePayPal.Substring(0, 10) + "..." : userNamePayPal));
                }
                catch (Exception ex)
                {
                    logger.Error("Erreur récupération PayPal appid: " + ex.Message);
                }

                try
                {
                    // Récupération du secret PayPal (utilisé comme password)
                    dynamic paypalSecretDynamic = globalPlateform.login.payPal.secret.Value;
                    if (globalPlateform.customer != null && globalPlateform.customer.login != null && globalPlateform.customer.login.payPal != null && globalPlateform.customer.login.payPal.secret != null)
                    {
                        paypalSecretDynamic = globalPlateform.customer.login.payPal.secret.Value;
                    }
                    passwordPayPal = paypalSecretDynamic;
                    logger.Debug("PayPal Secret récupéré: " + (passwordPayPal.Length > 10 ? passwordPayPal.Substring(0, 10) + "..." : passwordPayPal));
                }
                catch (Exception ex)
                {
                    logger.Error("Erreur récupération PayPal secret: " + ex.Message);
                }

                // Configuration correcte pour l'API PayPal
                request.AddHeader("Accept", "application/json");
                request.AddHeader("Accept-Language", "en_US");

                // Authentification Basic avec Client ID et Secret
                string credentials = Convert.ToBase64String(System.Text.Encoding.ASCII.GetBytes($"{userNamePayPal}:{passwordPayPal}"));
                request.AddHeader("Authorization", $"Basic {credentials}");

                // Paramètres de la requête
                request.AddParameter("grant_type", "authorization_code");
                request.AddParameter("code", codePP);

                logger.Debug("Requête PayPal configurée avec code: " + codePP.Substring(0, Math.Min(10, codePP.Length)) + "...");
                IRestResponse response = client.Execute(request);

                logger.Debug($"Réponse PayPal Token - Status: {response.StatusCode}, Content: {response.Content}");

                if (response.StatusCode == HttpStatusCode.OK)
                {
                    if (response.Content != "")
                    {
                        string stringResponse = response.Content;
                        JavaScriptSerializer js = new JavaScriptSerializer();
                        PayPalResponseGetToken jsonrespon = js.Deserialize<PayPalResponseGetToken>(stringResponse);

                        string myAccToken = jsonrespon.access_token;
                        string myIdToken = jsonrespon.id_token;

                        string urlUInfo = utilitaires2010.Initialisations.GetKeyAppSettings("paypalconnect_urluserinfo");
                        client = new RestClient(urlUInfo);
                        request = new RestRequest(Method.GET); // UserInfo utilise GET, pas POST
                        request.AddHeader("Authorization", string.Format("Bearer {0}", myAccToken));

                        logger.Debug("Appel PayPal UserInfo avec token: " + myAccToken.Substring(0, Math.Min(20, myAccToken.Length)) + "...");

                        response = client.Execute(request);
                        logger.Debug($"Réponse PayPal UserInfo - Status: {response.StatusCode}, Content: {response.Content}");
                        if (response.StatusCode == HttpStatusCode.OK)
                        {
                            if (response.Content != "")
                            {
                                stringResponse = response.Content;

                                logger.Debug("paypal userinfo ok content=" + stringResponse);
                                js = new JavaScriptSerializer();

                                string eventid = "0";

                                PayPalResponseGetInfo jsonPayPalUser = js.Deserialize<PayPalResponseGetInfo>(stringResponse);

                                string cryptoKey = WebConfigurationManager.AppSettings["CryptoKey"].ToString();

                                Sha1 sha1 = new Sha1(String.Format("{0,4:0000}", int.Parse(structureId)) + "|" + eventid + cryptoKey);
                                string myhash = sha1.getSha1();

                                CustomerEntity customer = new CustomerEntity()
                                {
                                    Email = jsonPayPalUser.email,
                                    Name = jsonPayPalUser.name,
                                    FirstName = jsonPayPalUser.name,
                                    Hash = myhash,
                                    Password = jsonPayPalUser.email,

                                    Address1 = jsonPayPalUser.address.street_address,
                                    City = jsonPayPalUser.address.locality,
                                    PostalCode = jsonPayPalUser.address.postal_code,
                                    Country = jsonPayPalUser.address.country
                                };

                                LoginDefault l = new LoginDefault();
                                int resultLogin = l.GetLogin(structureId, "0", customer);
                                if (resultLogin == 0)
                                {
                                    string result2 = Login.CreateCustomerWM(int.Parse(structureId), "0", customer, false, true);
                                    // Redirection après création de compte
                                    if (result2.StartsWith("true:"))
                                    {
                                        RedirectToHome(structureId);
                                    }
                                    else
                                    {
                                        logger.Error("Erreur création compte PayPal: " + result2);
                                        RedirectToLoginWithError("Erreur lors de la création du compte");
                                    }
                                }
                                else
                                {
                                    customer.Password = "PAYPALCONNECT";
                                    string result = Login.LoginCustomerWM(int.Parse(structureId), customer);
                                    
                                    if (result.StartsWith("true:"))
                                    {
                                        RedirectToHome(structureId);
                                    }
                                    else
                                    {

                                        RedirectToLoginWithError("Erreur lors de la connexion");
                                    }
                                }
                            }
                        }
                        else
                        {
                            RedirectToLoginWithError("Erreur lors de la récupération des informations PayPal: " + response.StatusCode);
                        }
                    }
                }
                else
                {
              
                    RedirectToLoginWithError("Erreur lors de l'authentification PayPal: " + response.StatusCode);
                }
            }
            else
            {

                RedirectToLoginWithError("Aucun code d'autorisation PayPal reçu");
            }
            }
            catch (Exception ex)
            {
                RedirectToLoginWithError("Erreur technique lors de la connexion PayPal");
            }
        }

        /// <summary>
        /// Redirige vers la page d'accueil après connexion réussie
        /// </summary>
        /// <param name="structureId">ID de la structure</param>
        private void RedirectToHome(string structureId)
        {
            try
            {
                // Redirection vers la vraie page d'accueil client
                string redirectUrl = "Home.aspx";
                if (!string.IsNullOrEmpty(structureId) && structureId != "0")
                {
                    redirectUrl += "?idstructure=" + structureId + "&lang=fr&idpa=1";
                }

                Response.Write("<script>setTimeout(function(){ window.location.href = '" + redirectUrl + "'; }, 3000);</script>");
                Response.End();
            }
            catch (Exception ex)
            {
                RedirectToLoginWithError("Erreur technique lors de la redirection");
            }
        }

        /// <summary>
        /// Redirige vers la page de login avec un message d'erreur
        /// </summary>
        /// <param name="errorMessage">Message d'erreur à afficher</param>
        private void RedirectToLoginWithError(string errorMessage)
        {
            try
            {
                string structureId = "";
                if (System.Web.HttpContext.Current.Session["idstructure"] != null)
                {
                    structureId = System.Web.HttpContext.Current.Session["idstructure"].ToString();
                }

                string redirectUrl = "Login.aspx";
                if (!string.IsNullOrEmpty(structureId) && structureId != "0")
                {
                    redirectUrl += "?idstructure=" + structureId;
                }

                // Ajouter le message d'erreur en paramètre
                redirectUrl += (redirectUrl.Contains("?") ? "&" : "?") + "paypal_error=" + HttpUtility.UrlEncode(errorMessage);

                Response.Redirect(redirectUrl, false);
            }
            catch (Exception ex)
            {
                Response.Redirect("Login.aspx", false);
            }
        }

    }

    public class PayPalResponseGetInfo
    {
        public string email { get; set; }

        public string user_id { get; set; }

        public string name { get; set; }

        public PayPalAddress address { get; set; }

        public class PayPalAddress
        {
            public string street_address { get; set; }
            public string locality { get; set; }
            public string region { get; set; }

            public string postal_code { get; set; }
            public string country { get; set; }

        }
    }

    public class PayPalResponseGetToken
    {
        public string token_type { get; set; }

        public string id_token { get; set; }

        public string access_token { get; set; }
    }
}