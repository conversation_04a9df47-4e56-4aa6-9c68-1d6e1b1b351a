/****
* mainColor : 088eb2  8,142,178 <---- couleur principale (override par couleur client)
* secondaryColor : 43474d  <---- couleur secondaire (par défaut gris rodrigue)
* normalGray : 8f949a <---- normal grey
* lightGray : f1f3f5 <---- light grey
****/

/****************** FIX ***********************/
.col, .col-1, .col-10, .col-11, .col-12, .col-2, .col-3, .col-4, .col-5, .col-6, .col-7, .col-8, .col-9, .col-auto, .col-lg, .col-lg-1, .col-lg-10, .col-lg-11, .col-lg-12, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9, .col-lg-auto, .col-md, .col-md-1, .col-md-10, .col-md-11, .col-md-12, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-md-auto, .col-sm, .col-sm-1, .col-sm-10, .col-sm-11, .col-sm-12, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9, .col-sm-auto, .col-xl, .col-xl-1, .col-xl-10, .col-xl-11, .col-xl-12, .col-xl-2, .col-xl-3, .col-xl-4, .col-xl-5, .col-xl-6, .col-xl-7, .col-xl-8, .col-xl-9, .col-xl-auto {
  min-height: 0px;
}

:focus, .custom-file-input:focus~.custom-file-control {
  outline: 0 !important;
  -webkit-box-shadow: none !important;
  box-shadow: none !important;
}

:invalid {
  -o-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}

body, html {
  font-family: 'Open Sans', sans-serif;
  font-size: 13px;
  color: #43474d;
}

a, a:hover, .btn-link, .btn-link:hover {
  color: rgba(8, 142, 178, 1);
  text-decoration: none;
}

ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

table {
  font-size: 1rem;
}

input[type="checkbox"] {
  margin-right: 5px;
  vertical-align: middle;
}

.chosen-container-multi .chosen-choices {
  display: block;
  width: 100%;
  padding: .375rem .75rem;
  font-size: 1rem;
  line-height: 1.5;
  color: #495057;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #ced4da;
  border-radius: .25rem;
  -webkit-transition: border-color .15s ease-in-out, -webkit-box-shadow .15s ease-in-out;
  transition: border-color .15s ease-in-out, -webkit-box-shadow .15s ease-in-out;
  transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out;
  transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out, -webkit-box-shadow .15s ease-in-out;
  -webkit-box-shadow: none;
  box-shadow: none;
  background-image: none;
}



.chosen-container-multi .chosen-choices li.search-field input[type=text] {
  padding: 0;
  margin: 0;
  height: auto;
   color: #6c757d;
  opacity: 1;
}

[data-pageihm-visible="false"] {
  display: none;
}

.intl-tel-input {
  width: 100%;
}

.btn {
  border-radius: 0;
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}

/*.btn {
  margin: 0 5px;
}

.btn:first-child {
  margin-left: 0;
}

.btn:last-child {
  margin-right: 0;
}*/
.btn-primary, .next .btn {
  background-color: rgba(8, 142, 178, 1);
  border-color: rgba(8, 142, 178, 1);
}

.btn-primary.disabled, .btn-primary:disabled {
  color: #fff;
  background-color: #ccc;
  border-color: #ccc;
}

.btn-primary:not(:disabled):not(.disabled):hover, .btn-primary:not(:disabled):not(.disabled):active {
  background-color: rgba(8, 142, 178, 1);
  border-color: rgba(8, 142, 178, 1);
  -webkit-filter: brightness(1.1);
  filter: brightness(1.2);
}

.btn-secondary {
  background-color: #43474d;
  border-color: #43474d;
}

.btn-secondary-light {
  background-color: #8f949a;
  border-color: #8f949a;
}

.bg-info {
  background-color: rgba(8, 142, 178, 0.5) !important;
}

.stepwizard-row {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.stepwizard-step {
  padding: 7px 11px;
  border-color: rgba(8, 142, 178, 1);
  color: rgba(8, 142, 178, 1)
}

.stepwizard-step .circle {
  border: 2px solid rgba(8, 142, 178, 1);
  border-radius: 50%;
  padding: 15px;
  position: relative;
  background-color: #fff;
  margin-bottom: 5px;
}

.stepwizard-step.disabled .circle {
  border-color: #aeb5ba;
}

.stepwizard-step:not(:first-child) .circle:before {
  content: '';
  width: 110%;
  height: 2px;
  display: block;
  right: calc(100% + 2px);
  position: absolute;
  top: calc(50% - 1px);
}

.nav-pills .nav-link.stepwizard-step {
  padding: 0;
}

.nav-pills .nav-link.stepwizard-step.active {
  background-color: transparent;
  border-color: transparent;
  color: rgba(8, 142, 178, 1);
}

.nav-pills .nav-link.stepwizard-step.active .circle {
  background-color: rgba(8, 142, 178, 1);
  border-color: rgba(8, 142, 178, 1);
  color: #fff;
}

.nav-pills .nav-link.stepwizard-step.error .circle {
  border-color: #e72e45;
}

.stepwizard-step .circle:before {
  background-color: rgba(8, 142, 178, 1);
}

.stepwizard-step.active ~ .stepwizard-step {
  border-color: #aeb5ba;
}

.stepwizard-step.active ~ .stepwizard-step .circle:before {
  background-color: #aeb5ba;
}

/*.nav-pills .nav-link.active, .nav-pills .show>.nav-link {
    color: #fff;
    background-color: rgba(8,142,178,1);
}
.nav-pills .nav-link {
    border-radius: .25rem;
}*/
.nav-pills .nav-link.disabled {
  color: #aeb5ba;
}

.nav-pills .nav-link.disabled:hover {
  cursor: default;
}

.tab-content {
  padding-top: 15px;
}

.material-switch > input[type="checkbox"] {
  display: none;
}

.material-switch > label {
  cursor: pointer;
  height: 0px;
  position: relative;
  width: 40px;
}

.material-switch > label::before {
  background: rgb(0, 0, 0);
  -webkit-box-shadow: inset 0px 0px 10px rgba(0, 0, 0, 0.5);
          box-shadow: inset 0px 0px 10px rgba(0, 0, 0, 0.5);
  border-radius: 8px;
  content: '';
  height: 16px;
  margin-top: -8px;
  position: absolute;
  opacity: 0.3;
  -webkit-transition: all 0.4s ease-in-out;
  transition: all 0.4s ease-in-out;
  width: 40px;
}

.material-switch > label::after {
  background: rgb(255, 255, 255);
  border-radius: 16px;
  -webkit-box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.3);
          box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.3);
  content: '';
  height: 24px;
  left: -4px;
  margin-top: -8px;
  position: absolute;
  top: -4px;
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
  width: 24px;
}

.material-switch > input[type="checkbox"]:checked + label::before {
  background: inherit;
  opacity: 0.5;
}

.material-switch > input[type="checkbox"]:checked + label::after {
  background: inherit;
  left: 20px;
}

.card {
  border-radius: 0;
  border: 2px solid #f1f3f5;
}

.card-header {
  border-radius: 0 !important;
  border: 0;
  background-color: #f1f3f5;
  position: relative;
}

.card-header .btn-link .card-customertitle span {
  display: block;
}

.card-header.error:before {
  content: "\f00d";
  color: #e72e45;
  display: block;
  font: normal normal normal 14px/1 FontAwesome;
  font-size: xx-large;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  position: absolute;
  margin: auto;
  right: 0;
  padding: 0 17px;
}

.card-header.error:after {
  content: "";
  display: block;
  width: 9px;
  height: 100%;
  background-color: #e72e45;
  position: absolute;
  left: 0;
  top: 0;
}

.card {
  border-radius: 0;
  border: 2px solid #f1f3f5;
}

.card-header .btn-link {
  font-size: 16px;
  color: #43474d;
  white-space: normal;
  width: 100%;
  text-align: left;
  text-decoration: none;
}

.card-header .btn-link .plusoumoins {
  fill: #43474d;
  max-width: 25px;
  max-width: 100%;
  vertical-align: bottom;
  margin-right: 7px;
  width: 25px;
}

.plusoumoins .verticalbar {
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
  -webkit-transform-origin: 50% 50%;
  transform-origin: 50% 50%;
  -webkit-transform: rotate(90deg);
  transform: rotate(90deg);
}

.plusoumoins .verticalbar {
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
  -webkit-transform-origin: 50% 50%;
  transform-origin: 50% 50%;
  -webkit-transform: rotate(90deg);
  transform: rotate(90deg);
}

.collapsed .plusoumoins .verticalbar {
  -webkit-transform: rotate(0deg);
  transform: rotate(0deg);
}

/************** LOADING **************/
#loadingcircle {
  width: 70px;
  position: relative;
  margin: auto;
  display: block;
}

#loadingcircle #greycircle {
  fill: #666666;
  -webkit-animation: loading 2s  linear infinite;
  animation: loading 2s  linear infinite;
  -webkit-transform-origin: center;
  transform-origin: center;
}

#loadingcircle #colorcircle {
  fill: rgba(8, 142, 178, 1);
  -webkit-animation: loading 2s  linear infinite reverse;
  animation: loading 2s  linear infinite reverse;
  -webkit-transform-origin: center;
  transform-origin: center;
}

@-webkit-keyframes loading {

  /* chrom */
  0% {
    opacity: 1;
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  100% {

    /*opacity: 0.5;*/
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }

}

@keyframes loading {

  /* chrom */
  0% {
    opacity: 1;
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  100% {

    /*opacity: 0.5;*/
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }

}

/********* HEADER ********/
header {
  margin-bottom: 20px;
}

#navbarNav {
}

#navbarNav .svg-inline--fa {
  margin: 0 4px;
}

/**** CREATION LOGIN *********/
#wrap {
  min-height: 100%;
}

ul.pager.wizard {
  list-style: none;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  padding: 0;
}

ul.pager.wizard li:only-child {
  margin-left: auto;
}

.orsepar {
  text-align: center;
  position: relative;
  margin-bottom: 15px;
  margin-top: 15px;
  z-index: 2;
}

.orsepar:before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  width: 100%;
  border-bottom: 1px solid rgba(0, 0, 0, .1);
  z-index: -1;
}

.orsepar:after {
  content: "";
  position: absolute;
  top: 0;
  left: 50%;
  width: 48px;
  height: 100%;
  margin-left: -24px;
  background-color: #fff;
  z-index: -1;
}

#divConnectFacebook {
  margin-bottom: 15px;
}

.btn-facebook {
  background-color: #4467b0;
  color: #fff;
  margin: 0;
}

.btn-facebook:hover {
  color: #fff;
  -webkit-filter: brightness(1.1);
  filter: brightness(1.2);
}

.btn-paypal {
  background-color: #1e88e5 !important;
  border-color: #1e88e5 !important;
  color: #fff !important;
  margin: 0;
}

.btn-paypal:hover {
  background-color: #1e88e5 !important;
  border-color: #1e88e5 !important;
  color: #fff !important;
  -webkit-filter: brightness(1.1);
  filter: brightness(1.2);
}

.btn-paypal:focus, .btn-paypal:active {
  background-color: #1e88e5 !important;
  border-color: #1e88e5 !important;
  color: #fff !important;
}

/* Règle très spécifique pour forcer le style du bouton PayPal */
#manual-paypal-btn, #manual-paypal-btn:hover, #manual-paypal-btn:focus, #manual-paypal-btn:active {
  background-color: #1e88e5 !important;
  border-color: #1e88e5 !important;
  color: #fff !important;
  background-image: none !important;
}

/* Surcharger les styles Bootstrap par défaut */
.btn.btn-paypal, button.btn-paypal {
  background-color: #1e88e5 !important;
  border-color: #1e88e5 !important;
  color: #fff !important;
}

#creationForm .nav-link .svg-inline--fa {
  font-size: 20px;
}

/****** FOOTER *****/
footer {
  background-color: #43474d;
  color: rgba(255, 255, 255, 0.5);
  padding: 30px 0;
}

/********** HOME **********/
#homeList ul {
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

#homeList li.homeItem {
  height: 200px;
  background-color: black;
  min-width: 33.33%;
}

#homeList li.homeItem a {
  height: 100%;
  display: block;
  background-color: rgba(8, 142, 178, 1);
  color: #fff;
  padding: 30px 0;
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}

#homeList li.homeItem:nth-child(2n) a {
  background-color: rgba(8, 142, 178, 0.95);
}

#homeList li.homeItem a .svg-inline--fa {
  font-size: 40px;
  margin-top: 40px;
  margin-bottom: 5px;
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}

#homeList li.homeItem a:hover {
  -webkit-filter: brightness(1.1);
  filter: brightness(1.2);
}

#homeList li.homeItem a:hover  .svg-inline--fa {
  margin-top: 35px;
  margin-bottom: 10px;
}

@media (max-width:767.98px) {
  #homeList li.homeItem {
    min-width: 50%;
  }

}

/******** INSPINIA *************
.ibox {
  clear: both;
  margin-bottom: 25px;
  margin-top: 0;
  padding: 0;
}
.ibox.collapsed .ibox-content {
  display: none;
}
.ibox.collapsed .fa.fa-chevron-up:before {
  content: "078";
}
.ibox.collapsed .fa.fa-chevron-down:before {
  content: "077";
}
.ibox:after,
.ibox:before {
  display: table;
}
.ibox-title {
  -moz-border-bottom-colors: none;
  -moz-border-left-colors: none;
  -moz-border-right-colors: none;
  -moz-border-top-colors: none;
  background-color: #ffffff;
  border-color: #e7eaec;
  border-image: none;
  border-style: solid solid none;
  border-width: 3px 0 0;
  color: inherit;
  margin-bottom: 0;
  padding: 14px 15px 7px;
  min-height: 48px;
}
.ibox-content {
  background-color: #ffffff;
  color: inherit;
  padding: 15px 20px 20px 20px;
  border-color: #e7eaec;
  border-image: none;
  border-style: solid solid none;
  border-width: 1px 0px;
}
.ibox-footer {
  color: inherit;
  border-top: 1px solid #e7eaec;
  font-size: 90%;
  background: #ffffff;
  padding: 10px 15px;
}

.ibox-content {
  clear: both;
}
.ibox-heading {
  background-color: #f3f6fb;
  border-bottom: none;
}
.ibox-heading h3 {
  font-weight: 200;
  font-size: 24px;
}
.ibox-title h5 {
  display: inline-block;
  font-size: 14px;
  margin: 0 0 7px;
  padding: 0;
  text-overflow: ellipsis;
  float: left;
}
.ibox-title .label {
  float: left;
  margin-left: 4px;
}
.ibox-tools {
  display: block;
  float: none;
  margin-top: 0;
  position: relative;
  padding: 0;
  text-align: right;
}
.ibox-tools a {
  cursor: pointer;
  margin-left: 5px;
  color: #c4c4c4;
}
.ibox-tools a.btn-primary {
  color: #fff;
}
.ibox-tools .dropdown-menu > li > a {
  padding: 4px 10px;
  font-size: 12px;
}
.ibox .ibox-tools.open > .dropdown-menu {
  left: auto;
  right: 0;
}*/

/******** INSPINIA **************/