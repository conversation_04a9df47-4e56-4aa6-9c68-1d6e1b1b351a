<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="debug-paypal-config.aspx.cs" Inherits="customerArea.DebugPayPalConfig" %>

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title>Debug Configuration PayPal</title>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.1.0/css/bootstrap.min.css">
    <script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
</head>
<body>
    <form id="form1" runat="server">
        <div class="container mt-5">
            <div class="row">
                <div class="col-md-10 offset-md-1">
                    <div class="card">
                        <div class="card-header bg-danger text-white">
                            <h3><i class="fas fa-bug"></i> Debug Configuration PayPal</h3>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-warning">
                                <strong>⚠️ Attention :</strong> Cette page affiche des informations sensibles. 
                                Ne l'utilisez qu'en développement et supprimez-la en production !
                            </div>

                            <h5>🔧 Configuration actuelle :</h5>
                            <table class="table table-bordered">
                                <tr>
                                    <td><strong>Structure ID :</strong></td>
                                    <td><span class="badge badge-info"><%=StructureId%></span></td>
                                </tr>
                                <tr>
                                    <td><strong>Client ID (PayPal) :</strong></td>
                                    <td><code><%=ClientId%></code></td>
                                </tr>
                                <tr>
                                    <td><strong>Client Secret (PayPal) :</strong></td>
                                    <td><code><%=ClientSecret.Length > 10 ? ClientSecret.Substring(0, 10) + "..." : ClientSecret%></code></td>
                                </tr>
                                <tr>
                                    <td><strong>Return URL :</strong></td>
                                    <td><code><%=ReturnUrl%></code></td>
                                </tr>
                                <tr>
                                    <td><strong>Token Service URL :</strong></td>
                                    <td><code><%=TokenServiceUrl%></code></td>
                                </tr>
                                <tr>
                                    <td><strong>UserInfo URL :</strong></td>
                                    <td><code><%=UserInfoUrl%></code></td>
                                </tr>
                            </table>

                            <h5>🔍 Diagnostic :</h5>
                            <div id="diagnostic">
                                <%=DiagnosticHtml%>
                            </div>

                            <h5>🛠️ Actions recommandées :</h5>
                            <div class="alert alert-info">
                                <h6>Pour résoudre le problème "Vous avez été bloqué" :</h6>
                                <ol>
                                    <li><strong>Vérifiez l'environnement :</strong> Utilisez des clés sandbox avec l'URL sandbox</li>
                                    <li><strong>Configurez les URLs de redirection :</strong> Dans votre app PayPal, ajoutez : <code><%=ReturnUrl%></code></li>
                                    <li><strong>Vérifiez les domaines autorisés :</strong> Ajoutez <code>dev.themisweb.fr</code> dans PayPal</li>
                                    <li><strong>Testez avec un compte sandbox :</strong> Créez un compte de test PayPal</li>
                                </ol>
                            </div>

                            <h5>🧪 Test de connectivité :</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <button type="button" class="btn btn-primary btn-block" onclick="testTokenService()">
                                        Test Token Service
                                    </button>
                                </div>
                                <div class="col-md-6">
                                    <button type="button" class="btn btn-secondary btn-block" onclick="testUserInfoService()">
                                        Test UserInfo Service
                                    </button>
                                </div>
                            </div>

                            <div id="testResults" class="mt-3"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>

    <script>
        function testTokenService() {
            $('#testResults').html('<div class="alert alert-info">Test du Token Service en cours...</div>');
            
            $.ajax({
                url: '<%=TokenServiceUrl%>',
                method: 'GET',
                timeout: 5000,
                success: function(data) {
                    $('#testResults').html('<div class="alert alert-success">✅ Token Service accessible</div>');
                },
                error: function(xhr, status, error) {
                    $('#testResults').html('<div class="alert alert-danger">❌ Token Service inaccessible: ' + error + '</div>');
                }
            });
        }

        function testUserInfoService() {
            $('#testResults').html('<div class="alert alert-info">Test du UserInfo Service en cours...</div>');
            
            $.ajax({
                url: '<%=UserInfoUrl%>',
                method: 'GET',
                timeout: 5000,
                success: function(data) {
                    $('#testResults').html('<div class="alert alert-success">✅ UserInfo Service accessible</div>');
                },
                error: function(xhr, status, error) {
                    $('#testResults').html('<div class="alert alert-danger">❌ UserInfo Service inaccessible: ' + error + '</div>');
                }
            });
        }
    </script>
</body>
</html>
