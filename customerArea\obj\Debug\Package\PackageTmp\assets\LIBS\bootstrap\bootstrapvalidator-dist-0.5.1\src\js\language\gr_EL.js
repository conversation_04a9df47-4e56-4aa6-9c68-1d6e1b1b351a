(function($) {
    /**
     * Greek language package
     * Translated by @pRieStaKos
     */
    $.fn.bootstrapValidator.i18n = $.extend(true, $.fn.bootstrapValidator.i18n, {
        base64: {
            'default': 'Παρα<PERSON><PERSON>λώ εισάγετε μια έγκυρη κωδικοποίηση base 64'
        },
        between: {
            'default': '<PERSON>αρα<PERSON>αλώ εισάγετε μια τιμή μεταξύ %s και %s',
            notInclusive: 'Παρακαλώ εισάγετε μια τιμή μεταξύ %s και %s αυστηρά'
        },
        callback: {
            'default': 'Παρα<PERSON><PERSON>λώ εισάγετε μια έγκυρη τιμή'
        },
        choice: {
            'default': 'Παρακαλώ εισάγετε μια έγκυρη τιμή',
            less: '<PERSON><PERSON><PERSON>α<PERSON><PERSON><PERSON><PERSON> επιλέξτε %s επιλογές στο ελάχιστο',
            more: 'Παρα<PERSON>αλώ επιλέξτε %s επιλογές στο μέγιστο',
            between: 'Παρακαλώ επιλέξτε %s - %s options'
        },
        creditCard: {
            'default': 'Παρακαλώ εισάγετε έναν έγκυρο αριθμό πιστωτικής κάρτας'
        },
        cusip: {
            'default': 'Παρακαλώ εισάγετε έναν έγκυρο αριθμό CUSIP'
        },
        cvv: {
            'default': 'Παρακαλώ εισάγετε έναν έγκυρο αριθμό CVV'
        },
        date: {
            'default': 'Παρακαλώ εισάγετε μία έγκυρη ημερομηνία'
        },
        different: {
            'default': 'Παρακαλώ εισάγετε μια διαφορετική τιμή'
        },
        digits: {
             'default': 'Παρακαλώ εισάγετε μόνο ψηφία'
        },
        ean: {
            'default': 'Παρακαλώ εισάγετε έναν έγκυρο αριθμό EAN'
        },
        emailAddress: {
            'default': 'Παρακαλώ εισάγετε ένα έγκυρο email'
        },
        file: {
            'default': 'Παρακαλώ επιλέξτε ένα έγκυρο αρχείο'
        },
        greaterThan: {
            'default': 'Παρακαλώ εισάγετε μια τιμή μεγαλύτερη ή ίση με %s',
            notInclusive: 'Παρακαλώ εισάγετε μια τιμή μεγαλύτερη από %s'
        },
        grid: {
            'default': 'Παρακαλώ εισάγετε έναν έγκυρο αριθμό GRId'
        },
        hex: {
            'default': 'Παρακαλώ εισάγετε έναν έγκυρο δεκαεξαδικό αριθμό'
        },
        hexColor: {
            'default': 'Παρακαλώ εισάγετε ένα έγκυρο χρώμα hex'
        },
        iban: {
            'default': 'Παρακαλώ εισάγετε έναν έγκυρο αριθμό IBAN',
            countryNotSupported: 'Ο κωδικός χώρας %s δεν υποστηρίζεται',
            country: 'Παρακαλώ εισάγετε έναν έγκυρο αριθμό σε %s',
            countries: {
                AD: 'Ανδόρα',
                AE: 'Ηνωμένα Αραβικά Εμιράτα',
                AL: 'Αλβανία',
                AO: 'Αγκόλα',
                AT: 'Αυστρία',
                AZ: 'Αζερμπαϊτζάν',
                BA: 'Βοσνία και Ερζεγοβίνη',
                BE: 'Βέλγιο',
                BF: 'Μπουρκίνα Φάσο',
                BG: 'Βουλγαρία',
                BH: 'Μπαχρέιν',
                BI: 'Μπουρούντι',
                BJ: 'Μπενίν',
                BR: 'Βραζιλία',
                CH: 'Ελβετία',
                CI: 'Ακτή Ελεφαντοστού',
                CM: 'Καμερούν',
                CR: 'Κόστα Ρίκα',
                CV: 'Πράσινο Ακρωτήρι',
                CY: 'Κύπρος',
                CZ: 'Τσεχία',
                DE: 'Γερμανία',
                DK: 'Δανία',
                DO: 'Δομινικανή Δημοκρατία',
                DZ: 'Αλγερία',
                EE: 'Εσθονία',
                ES: 'Ισπανία',
                FI: 'Φιλανδία',
                FO: 'Νησιά Φερόε',
                FR: 'Γαλλία',
                GB: 'Ηνωμένο Βασίλειο',
                GE: 'Γεωργία',
                GI: 'Γιβραλτάρ',
                GL: 'Γροιλανδία',
                GR: 'Ελλάδα',
                GT: 'Γουατεμάλα',
                HR: 'Κροατία',
                HU: 'Ουγγαρία',
                IE: 'Ιρλανδία',
                IL: 'Ισραήλ',
                IR: 'Ιράν',
                IS: 'Ισλανδία',
                IT: 'Ιταλία',
                JO: 'Ιορδανία',
                KW: 'Κουβέιτ',
                KZ: 'Καζακστάν',
                LB: 'Λίβανος',
                LI: 'Λιχτενστάιν',
                LT: 'Λιθουανία',
                LU: 'Λουξεμβούργο',
                LV: 'Λετονία',
                MC: 'Μονακό',
                MD: 'Μολδαβία',
                ME: 'Μαυροβούνιο',
                MG: 'Μαδαγασκάρη',
                MK: 'πΓΔΜ',
                ML: 'Μάλι',
                MR: 'Μαυριτανία',
                MT: 'Μάλτα',
                MU: 'Μαυρίκιος',
                MZ: 'Μοζαμβίκη',
                NL: 'Ολλανδία',
                NO: 'Νορβηγία',
                PK: 'Πακιστάν',
                PL: 'Πολωνία',
                PS: 'Παλαιστίνη',
                PT: 'Πορτογαλία',
                QA: 'Κατάρ',
                RO: 'Ρουμανία',
                RS: 'Σερβία',
                SA: 'Σαουδική Αραβία',
                SE: 'Σουηδία',
                SI: 'Σλοβενία',
                SK: 'Σλοβακία',
                SM: 'Σαν Μαρίνο',
                SN: 'Σενεγάλη',
                TN: 'Τυνησία',
                TR: 'Τουρκία',
                VG: 'Βρετανικές Παρθένοι Νήσοι'
            }
        },
        id: {
            'default': 'Παρακαλώ εισάγετε ένα έγκυρο αριθμό ταυτότητας',
            countryNotSupported: 'Ο κωδικός χώρας %s δεν υποστηρίζεται',
            country: 'Παρακαλώ εισάγετε ένα έγκυρο αριθμό ταυτότητας %s',
            countries: {
                BA: 'Βοσνίας και Ερζεγοβίνης',
                BG: 'Βουλγαρίας',
                BR: 'Βραζιλίας',
                CH: 'Ελβετίας',
                CL: 'Χιλής',
                CZ: 'Τσεχίας',
                DK: 'Δανίας',
                EE: 'Εσθονίας',
                ES: 'Ισπανίας',
                FI: 'Φιλανδίας',
                HR: 'Κροατίας',
                IE: 'Ιρλανδίας',
                IS: 'Ισλανδίας',
                LT: 'Λιθουανίας',
                LV: 'Λετονίας',
                ME: 'Μαυροβούνιου',
                MK: 'πΓΔΜ',
                NL: 'Ολλανδίας',
                RO: 'Ρουμανίας',
                RS: 'Σερβίας',
                SE: 'Σουηδίας',
                SI: 'Σλοβενίας',
                SK: 'Σλοβακίας',
                SM: 'Σαν Μαρίνο',
                ZA: 'Νότιας Αφρικής'
            }
        },
        identical: {
            'default': 'Παρακαλώ εισάγετε την ίδια τιμή'
        },
        imei: {
            'default': 'Παρακαλώ εισάγετε ένα έγκυρο αριθμό IMEI'
        },
        imo: {
            'default': 'Παρακαλώ εισάγετε ένα έγκυρο αριθμό IMO'
        },
        integer: {
            'default': 'Παρακαλώ εισάγετε ένα έγκυρο αριθμό'
        },
        ip: {
            'default': 'Παρακαλώ εισάγετε μία έγκυρη IP διεύθυνση',
            ipv4: 'Παρακαλώ εισάγετε μία έγκυρη διεύθυνση IPv4',
            ipv6: 'Παρακαλώ εισάγετε μία έγκυρη διεύθυνση IPv6'
        },
        isbn: {
            'default': 'Παρακαλώ εισάγετε ένα έγκυρο αριθμό ISBN'
        },
        isin: {
            'default': 'Παρακαλώ εισάγετε ένα έγκυρο αριθμό ISIN'
        },
        ismn: {
            'default': 'Παρακαλώ εισάγετε ένα έγκυρο αριθμό ISMN'
        },
        issn: {
            'default': 'Παρακαλώ εισάγετε ένα έγκυρο αριθμό ISSN'
        },
        lessThan: {
            'default': 'Παρακαλώ εισάγετε μια τιμή μικρότερη ή ίση με %s',
            notInclusive: 'Παρακαλώ εισάγετε μια τιμή μικρότερη από %s'
        },
        mac: {
            'default': 'Παρακαλώ εισάγετε μία έγκυρη MAC διεύθυνση'
        },
        meid: {
            'default': 'Παρακαλώ εισάγετε ένα έγκυρο αριθμό MEID'
        },
        notEmpty: {
            'default': 'Παρακαλώ εισάγετε μια τιμή'
        },
        numeric: {
            'default': 'Παρακαλώ εισάγετε ένα έγκυρο δεκαδικό αριθμό'
        },
        phone: {
            'default': 'Παρακαλώ εισάγετε ένα έγκυρο αριθμό τηλεφώνου',
            countryNotSupported: 'Ο κωδικός χώρας %s δεν υποστηρίζεται',
            country: 'Παρακαλώ εισάγετε ένα έγκυρο αριθμό τηλεφώνου στην %s',
            countries: {
                BR: 'Βραζιλία',
                ES: 'Ισπανία',
                FR: 'Γαλλία',
                GB: 'Ηνωμένο Βασίλειο',
                MA: 'Μαρόκο',
                PK: 'Πακιστάν',
                US: 'ΗΠΑ'
            }
        },
        regexp: {
            'default': 'Παρακαλώ εισάγετε μια τιμή όπου ταιριάζει στο υπόδειγμα'
        },
        remote: {
            'default': 'Παρακαλώ εισάγετε ένα έγκυρο αριθμό'
        },
        rtn: {
            'default': 'Παρακαλώ εισάγετε ένα έγκυρο αριθμό RTN'
        },
        sedol: {
            'default': 'Παρακαλώ εισάγετε ένα έγκυρο αριθμό SEDOL'
        },
        siren: {
            'default': 'Παρακαλώ εισάγετε ένα έγκυρο αριθμό SIREN'
        },
        siret: {
            'default': 'Παρακαλώ εισάγετε ένα έγκυρο αριθμό SIRET'
        },
        step: {
            'default': 'Παρακαλώ εισάγετε ένα έγκυρο βήμα από %s'
        },
        stringCase: {
            'default': 'Παρακαλώ εισάγετε μόνο πεζούς χαρακτήρες',
            upper: 'Παρακαλώ εισάγετε μόνο κεφαλαία γράμματα'
        },
        stringLength: {
            'default': 'Παρακαλώ εισάγετε μια τιμή με έγκυρο μήκος',
            less: 'Παρακαλούμε εισάγετε λιγότερο από %s χαρακτήρες',
            more: 'Παρακαλούμε εισάγετε περισσότερο από %s χαρακτήρες',
            between: 'Παρακαλούμε, εισάγετε τιμή μεταξύ %s και %s μήκος χαρακτήρων'
        },
        uri: {
            'default': 'Παρακαλώ εισάγετε ένα έγκυρο URI'
        },
        uuid: {
            'default': 'Παρακαλώ εισάγετε ένα έγκυρο αριθμό UUID',
            version: 'Παρακαλώ εισάγετε ένα έγκυρο αριθμό έκδοσης %s'
        },
        vat: {
            'default': 'Παρακαλώ εισάγετε ένα έγκυρο αριθμό ΦΠΑ',
            countryNotSupported: 'Ο κωδικός χώρας %s δεν υποστηρίζεται',
            country: 'Παρακαλώ εισάγετε ένα έγκυρο αριθμό ΦΠΑ %s',
            countries: {
                AT: 'Αυστρίας',
                BE: 'Βελγίου',
                BG: 'Βουλγαρίας',
                BR: 'Βραζιλίας',
                CH: 'Ελβετίας',
                CY: 'Κύπρου',
                CZ: 'Τσεχίας',
                DE: 'Γερμανίας',
                DK: 'Δανίας',
                EE: 'Εσθονίας',
                ES: 'Ισπανίας',
                FI: 'Φιλανδίας',
                FR: 'Γαλλίας',
                GB: 'Ηνωμένου Βασιλείου',
                GR: 'Ελλάδας',
                EL: 'Ελλάδας',
                HU: 'Ουγγαρίας',
                HR: 'Κροατίας',
                IE: 'Ιρλανδίας',
                IS: 'Ισλανδία',
                IT: 'Ιταλίας',
                LT: 'Λιθουανίας',
                LU: 'Λουξεμβούργου',
                LV: 'Λετονίας',
                MT: 'Μάλτας',
                NL: 'Ολλανδίας',
                NO: 'Νορβηγίας',
                PL: 'Πολωνίας',
                PT: 'Πορτογαλλίας',
                RO: 'Ρουμανίας',
                RU: 'Ρωσίας',
                RS: 'Σερβίας',
                SE: 'Σουηδίας',
                SI: 'Σλοβενίας',
                SK: 'Σλοβακίας',
                ZA: 'Νότιας Αφρικής'
            }
        },
        vin: {
            'default': 'Παρακαλώ εισάγετε ένα έγκυρο αριθμό VIN'
        },
        zipCode: {
            'default': 'Παρακαλώ εισάγετε ένα έγκυρο ταχυδρομικό κώδικα',
            countryNotSupported: 'Ο κωδικός χώρας %s δεν υποστηρίζεται',
            country: 'Παρακαλώ εισάγετε ένα έγκυρο %s',
            countries: {
                BR: 'ταχυδρομικό κώδικα Βραζιλίας',
                CA: 'ταχυδρομικό κώδικα Καναδά',
                DK: 'ταχυδρομικό κώδικα Δανίας',
                GB: 'ταχυδρομικό κώδικα Ηνωμένου Βασιλείου',
                IT: 'ταχυδρομικό κώδικα Ιταλίας',
                MA: 'ταχυδρομικό κώδικα Μαρόκο',
                NL: 'ταχυδρομικό κώδικα Ολλανδίας',
                SE: 'ταχυδρομικό κώδικα Ελβετίας',
                SG: 'ταχυδρομικό κώδικα Σιγκαπούρης',
                US: 'ταχυδρομικό κώδικα ΗΠΑ'
            }
        }
    });
}(window.jQuery));
