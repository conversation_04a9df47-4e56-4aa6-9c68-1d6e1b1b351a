﻿$(document).ready(function () {
    //on supprime la navbar
    $('#page-wrapper .navbar').remove();



    if ($('#ctPlaceHold_token').data('hash') == "bad_token") {
        $('#resetPasswordForm').html('<label class="label label-danger">Fehlerhaft<PERSON> Zeichen</label>');

        $('#panelinfoforgotpass').hide();
        swal("", "Fehlerhaftes Zeichen", "error");

    } else {
        LoadPageResetPassword();

    }



});




function LoadPageResetPassword() {
    // $('#resetPasswordForm').data('bootstrapValidator');

    $('#LnkBtnCreate').on('click', function () {
        $('#resetPasswordForm').data('bootstrapValidator').validate()

    });



    $('#resetPasswordForm').on('init.form.bv', function (e, data) {
        data.bv.disableSubmitButtons(true);
    }).bootstrapValidator({
        excluded: ':disabled',
        feedbackIcons: {
            valid: 'glyphicon glyphicon-ok',
            invalid: 'glyphicon glyphicon-remove',
            validating: 'glyphicon glyphicon-refresh'
        },
        fields: {
            password: {
                // enabled: false,
                //trigger: 'blur',
                validators: {
                    notEmpty: {
                        message: ReadXmlTranslate('password_ismandatory')
                    },
                    regexp: {
                        regexp: ReadXmlTranslate('password_regex'),
                        message: ReadXmlTranslate('password_isinvalid')
                    }
                }
            },
            confirmpassword: {
                // enabled: false,
                //trigger: 'blur',
                validators: {
                    notEmpty: {
                        message: ReadXmlTranslate('password_ismandatory')
                    },
                    identical: {
                        field: 'password',
                        message: ReadXmlTranslate('password_comparenotmatch')
                    }
                }
            },
        }
    }).on('success.form.bv', function (e) {
        // Prevent submit form
        // e.preventDefault();
       
        ChangePassWord();

        // $("#resetPasswordForm").submit();
    });
}

/// envoi du mot de passe
function ChangePassWord() {

    var structId = $('#ctPlaceHold_token').data("structureid");
    var identiteId = $('#ctPlaceHold_token').data("identiteid");
    var hash = $('#ctPlaceHold_token').data("hash");
    var newPass = $('#password').val();

    $.ajax({
        type: "POST",
        url: 'resetpassword.aspx/ChangePassWord',
        contentType: 'application/json; charset=utf-8',
        data: JSON.stringify({
            idStructure: structId,
            _identiteId: identiteId,
            _hash: hash,
            _newpass: newPass
        }),

        success: function (result) {
            if (result.d.split(':')[1] == "danger")
            {
                swal("", ReadXmlTranslate(result.d.split(':')[1]), "error");
            } else {
                swal("", ReadXmlTranslate(result.d.split(':')[1]), "success");
            }


        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
           
        }
    });


}