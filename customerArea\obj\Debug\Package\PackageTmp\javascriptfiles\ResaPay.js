$(document).ready(function() {
	//MOmanager()
	PayBasket()
	$('#lbl_conditions_vente').html(ReadXmlTranslate('lbl_cgv').replace("[link]", " <a class='seeConditionsDeVentes' data-toggle='modal' data-target='#modalGeneric' data-type='iframe' href='" + $('#lbl_conditions_vente').attr('data-fileurl') + "'>").replace("[/link]", "</a>"))

	$('#checkboxExtraFormWrapper input, #checkboxExtraFormWrapper textarea').on('change input', function() {
		PayButtonShowManager()
	});

});

function PayBasket() {
	$('#payOrder').on('click', function(e) {
		e.preventDefault()
		$.ajax({
			type: "POST",
			url: 'ResaPay.aspx/PayBasket',
			contentType: 'application/json; charset=utf-8',
			dataType: "json",
			data: '{structureId: "' + structureId + '", basketId: "' + basketId + '", obtainingModeId: "' + $('#ddlObtentionAll').find('option:selected').attr('idmo') + '", formuleId : "' + formuleId + '", langCode: "' + langCode + '"}',
			success: function(response) {
				console.log(response)
				window.top.location = response.d
			},
			error: function(XMLHttpRequest, textStatus, errorThrown) {

			},
			complete: function() {

			}
		});
	})
}

function MOmanager() {
	var totaltopay = parseInt($('.summaryLineTotalOrder .priceTotal').attr('data-priceformat'))
	$('.summaryLineTotalOrder .priceTotal').attr('data-originalcmdprice', totaltopay)

	$('#ddlObtentionAll').on('change', function() {
		var moval = $(this).val()
		var moid = parseInt($(this).find('option:selected').attr('idmo'))
		var motext = $(this).find('option:selected').text()
		var moamnt = parseInt($(this).find('option:selected').attr("amnt"))
		if (moid > 0) {
			$('#summaryLineMO').show()
			$('#summaryLineMO #MoLabel').html(motext)
			$('#summaryLineMO #MoAmount').attr("data-priceformat", moamnt)
			$('#summaryLineMO #MoAmount').html(moamnt)
			$('#summaryLineMO #MoAmount').priceFormat({
				prefix: '',
				suffix: ' ' + deviseCode,
				thousandsSeparator: ' '
			})
			$('.summaryLineTotalOrder .priceTotal').html(totaltopay + moamnt)
			$('.summaryLineTotalOrder .priceTotal').priceFormat({
				prefix: '',
				suffix: ' ' + deviseCode,
				thousandsSeparator: ' '
			})

		} else {
			$('#summaryLineMO').hide()
			$('.summaryLineTotalOrder .priceTotal').html($('.summaryLineTotalOrder .priceTotal').attr('data-originalcmdprice'))
			$('.summaryLineTotalOrder .priceTotal').priceFormat({
				prefix: '',
				suffix: ' ' + 'EUR',
				thousandsSeparator: ' '
			})
		}
		addMoTauxTVA()
		PayButtonShowManager()
	})
	$('#ddlObtentionAll option').prop('selected', false)
	$('#ddlObtentionAll option').first().prop('selected', true).trigger('change')
}

function PayButtonShowManager() {
	var moid = parseInt($("#ddlObtentionAll").find('option:selected').attr('idmo'))
	// verification que l'extraform rempli les conditions
	var AllFormIsOk = true;
	$.each($('#checkboxExtraFormWrapper input[required], #checkboxExtraFormWrapper textarea[required]'), function(i,k) {
		switch(k.tagName.toLowerCase()) {
			case 'input':
				switch($(k).attr('type')) {
					case 'checkbox':
						if(!$(k).is(':checked')) {
							AllFormIsOk = false;
						}
						break;
					case 'text':
						if($(k).val().trim() == "") {
							AllFormIsOk = false;
						}
						break;
				}
			break;
			case 'textarea':
				if($(k).val().trim() == "") {
					AllFormIsOk = false;
				}
			break;
		}
			
	})

	if (moid > 0 && AllFormIsOk) {
		$('#payOrder').removeClass('disabled').prop('disabled', false)
	} else {
		$('#payOrder').addClass('disabled').prop('disabled', true)
	}
}

function addMoTauxTVA() {
	var MoTauxTVA = parseFloat($('#ddlObtentionAll option:selected').attr('data-tvataux')) || 0
	var MoAmountTTC = parseFloat($('#ddlObtentionAll option:selected').attr('amnt'))
	var lstTauxTvaGrouped = []
	// clone l'objet des taux TVA original 
	lstTauxTvaGrouped = JSON.parse(JSON.stringify(lstTauxTvaGroupedOriginal));

	if (MoTauxTVA > 0) {
		var indexTaux = lstTauxTvaGrouped.findIndex(x => x.taux === MoTauxTVA);

		var MoHt = parseInt(MoAmountTTC / (1 + (MoTauxTVA / 100)))
		var MoAmountTva = MoAmountTTC - MoHt

		if (indexTaux < 0) {
			//ne trouve pas ce taux dans le tableau 


			var tauxObj = {};
			tauxObj["taux"] = MoTauxTVA;
			tauxObj["montant"] = MoAmountTva;
			lstTauxTvaGrouped.push(tauxObj)
		} else {
			//update le montant pour ce taux
			lstTauxTvaGrouped[indexTaux]["montant"] = lstTauxTvaGrouped[indexTaux]["montant"] + MoAmountTva
		}
	}

	lstTauxTvaGrouped.sort(predicate({
		name: 'taux',
		reverse: false
	}));

	console.log("lstTauxTvaGrouped")
	console.log(lstTauxTvaGrouped)
	console.log("lstTauxTvaGroupedOriginal")
	console.log(lstTauxTvaGroupedOriginal)
	var html = ""
	var totalTVAamount = 0
	$.each(lstTauxTvaGrouped, function(i, k) {
		if(k.taux > 0) {
			html += '<div class="oneSummaryLine">'
			html += '<div class="row">'
			html += '<div class="col">'
			html += '<div>' + k.taux + '%</div>'
			html += '</div>'
			html += '<div class="col-4 col-md-3 text-right">'
			html += '<div class="priceTotal" data-priceformat="' + k.montant + '">' + k.montant + '</div>'
			html += '</div>'
			html += '</div>'
			html += '</div>'
			totalTVAamount += k.montant
		}
	})
	$('#detailsTVAWrapper').html(html)
	$('#detailsTVAWrapper [data-priceformat]').priceFormat({
		prefix: '',
		suffix: ' ' + deviseCode,
		thousandsSeparator: ' '
	})
	$('.totalTVAWrapper .priceTotal').html(totalTVAamount)
	$('.totalTVAWrapper .priceTotal').priceFormat({
		prefix: '',
		suffix: ' ' + deviseCode,
		thousandsSeparator: ' '
	})

}