﻿<%@ Page Title="" Language="C#" MasterPageFile="~/pagemaster.Master" AutoEventWireup="true" CodeBehind="Fans.aspx.cs" Inherits="customerArea.Fans" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ctPlaceHold" runat="server">

<h2 data-trad="title_reabo_fans">Réabo / fans</h2>
<!-- commentaire haut -->
<div id="commentsPageHaut"><asp:Literal ID="litForCommentaireHaut" runat="server"></asp:Literal></div>

<div id="accordion">
    <!-- ORDERS START -->
    <div class="card">
        <div class="card-header" id="headingFans1">
            <h5 class="mb-0">
                <a href="#"  class="text-left d-block btn btn-link" data-toggle="collapse" data-target="#collapseFan1" aria-expanded="true" aria-controls="collapseFan1">
                    <div class="row no-gutters align-items-center">
                        <div class="col-auto">
                            <svg class="plusoumoins img-fluid" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 16.4 16.3" style="enable-background:new 0 0 16.4 16.3;" xml:space="preserve"><g><g><path class="st0" d="M13.4,9.2H3c-0.6,0-1-0.4-1-1s0.4-1,1-1h10.3c0.6,0,1,0.4,1,1S13.9,9.2,13.4,9.2z"></path></g><g><g><path class="st0" d="M9.5,16.3c-0.2,0-0.4-0.2-0.5-0.4c0-0.3,0.1-0.5,0.4-0.6c3.4-0.6,5.9-3.6,5.9-7.1c0-4-3.2-7.2-7.2-7.2 S1,4.2,1,8.2c0,3.5,2.5,6.5,5.9,7.1c0.3,0,0.5,0.3,0.4,0.6c0,0.3-0.3,0.5-0.6,0.4C2.8,15.6,0,12.2,0,8.2C0,3.7,3.7,0,8.2,0 c4.5,0,8.2,3.7,8.2,8.2C16.4,12.2,13.5,15.6,9.5,16.3C9.6,16.2,9.6,16.3,9.5,16.3z"></path></g></g></g><g class="verticalbar"><g><path class="st0" d="M8.2,14.4c-0.6,0-1-0.4-1-1V3c0-0.6,0.4-1,1-1s1,0.4,1,1v10.3C9.2,13.9,8.7,14.4,8.2,14.4z"></path></g></g></svg>
                        </div>
                        <div class="col card-customertitle">
                            <span class="font-weight-bold" data-trad="title_my_orders">My orders</span>
                        </div>
                    </div>
                </a>
            </h5>
        </div>

        <div id="collapseFan1" class="collapse show" aria-labelledby="headingFans1" >
            <div class="card-body">
                <!--  commandes sélectionées start-->
                <div class="row">
                    <div class="col-12">
                        <div class="card border-0 rounded text-white bg-primary mb-2  h-100">
                            <div class="card-body">
                                <h5 class="card-title" data-trad="title_no_order_selected">No order selected</h5>
                                <div class="row">
                                    <div class="col-md-8">
                                        <div id="amountDeposit">Deposit : 0.00 &euro;</div>
                                        <div id="montantSolde">Balance : 0.00 &euro;</div>
                                        <div id="montantSeuil">Threshold : 0.00 &euro;</div>
                                        <div id="montantBtbasketValid"></div>
                                    </div>
                                    <div class="col-md-4">
                                        <button type="button" id="showSelectedOrders" disabled class="btn btn-block btn-secondary" data-toggle="modal" data-target="#myModal2">
                                            <i class="fas fa-link"></i> <span data-trad="btn_view_selected_orders">View selected orders</span>
                                        </button>
                                        <button type="button" id="removeAllBasket" disabled class="btn btn-block btn-danger">
                                            <i class="fas fa-times"></i> <span data-trad="btn_delete_all_basket">Delete all the basket</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!--  commandes sélectionées end-->

                <hr />

                <!--  filtres start-->
                <div class="row mt-3">
                    <div class="col-md-6">
                        <div class="card border-0 rounded text-white bg-secondary mb-2">
                            <div class="card-body">
                                <div class="row vertical-align">
                                    <div class="col-auto">
                                        <div class="form-check">
                                            <input type="checkbox" id="nb_cmd_payees" name="nb_cmd_payees" class="form-check-input" />
                                            <label class="form-check-label m-l-xs" for="nb_cmd_payees" data-trad="lbl_paid_orders">Paid orders</label>
                                        </div>
                                    </div>
                                    <div class="col text-right">
                                        <span class="font-weight-bold" id="nb_cmd_payees">0</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card border-0 rounded text-white bg-secondary mb-2">
                            <div class="card-body">
                                <div class="row vertical-align">
                                    <div class="col-auto">
                                        <div class="form-check">
                                            <input type="checkbox" id="nb_cmd_reservees" name="nb_cmd_reservees" class="form-check-input" />
                                            <label class="form-check-label m-l-xs" for="nb_cmd_reservees" data-trad="lbl_reserved_orders">Reserved orders</label>
                                        </div>
                                    </div>
                                    <div class="col text-right">
                                        <span class="font-weight-bold" id="nb_cmd_reservees">0</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!--  filtres end-->

                <!-- tableau liste des commandes reliées à l'utilisateur -->
                <div class="row">
                    <div id="orders_list_wrapper" class="table-responsive" role="grid">
                        <table id="orders_list_table" class="table table-striped w-100" aria-describedby="orders_list_info">
                            <thead class="thead-dark">
                                <tr role="row">
                                    <th><input type="checkbox" class="chkAllRowsOrdersAttached" name="chkAllRowsOrdersAttached" /></th>
                                    <th><span data-trad="lbl_identite_id">Id</span></th>
                                    <th><span data-trad="lbl_lastname">Lastname</span></th>
                                    <th><span data-trad="lbl_firstname">Firstname</span></th>
                                    <th><span data-trad="lbl_state">Order state</span></th>
                                    <th></th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
                <!-- Fin tableau liste des commandes -->

            </div>
        </div>
    </div>
    <!-- ORDERS END -->
    <!-- MANAGE FAN START -->
    <div class="card">
        <div class="card-header" id="headingFans2">
            <h5 class="mb-0">
                <a href="#"  class="text-left d-block btn btn-link collapsed" data-toggle="collapse" data-target="#collapseFan2" aria-expanded="false" aria-controls="collapseFan2">
                    <div class="row no-gutters align-items-center">
                        <div class="col-auto">
                            <svg class="plusoumoins img-fluid" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 16.4 16.3" style="enable-background:new 0 0 16.4 16.3;" xml:space="preserve"><g><g><path class="st0" d="M13.4,9.2H3c-0.6,0-1-0.4-1-1s0.4-1,1-1h10.3c0.6,0,1,0.4,1,1S13.9,9.2,13.4,9.2z"></path></g><g><g><path class="st0" d="M9.5,16.3c-0.2,0-0.4-0.2-0.5-0.4c0-0.3,0.1-0.5,0.4-0.6c3.4-0.6,5.9-3.6,5.9-7.1c0-4-3.2-7.2-7.2-7.2 S1,4.2,1,8.2c0,3.5,2.5,6.5,5.9,7.1c0.3,0,0.5,0.3,0.4,0.6c0,0.3-0.3,0.5-0.6,0.4C2.8,15.6,0,12.2,0,8.2C0,3.7,3.7,0,8.2,0 c4.5,0,8.2,3.7,8.2,8.2C16.4,12.2,13.5,15.6,9.5,16.3C9.6,16.2,9.6,16.3,9.5,16.3z"></path></g></g></g><g class="verticalbar"><g><path class="st0" d="M8.2,14.4c-0.6,0-1-0.4-1-1V3c0-0.6,0.4-1,1-1s1,0.4,1,1v10.3C9.2,13.9,8.7,14.4,8.2,14.4z"></path></g></g></svg>
                        </div>
                        <div class="col card-customertitle">
                            <span class="font-weight-bold" data-trad="title_mamange_fans">Manage fans</span>
                        </div>
                    </div>
                </a>
            </h5>
        </div>
        <div id="collapseFan2" class="collapse " aria-labelledby="headingFans2" >
            <div class="card-body">
                <h4 data-trad="title_list_of_people_attached">Liste des personnes attachées</h4>
                <button class="btn btn-primary" data-trad="btn_add_relation_fan" id="btn_add_relation">Add a new relation</button>
                <!-- tableau liste des personnes reliées à l'utilisateur (manager)-->
                <div class="row">
                    <div id="childs_list_wrapper" class="table-responsive " role="grid">
                        <table id="childs_list_table" class="table table-striped w-100" aria-describedby="childs_list_info">
                            <thead class="thead-dark">
                                <tr role="row">
                                    <th>#</th>
                                    <th><span data-trad="lbl_lastname">Lastname</span></th>
                                    <th><span data-trad="lbl_firstname">Firstname</span></th>
                                    <th></th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
                <!-- Fin tableau liste des personnes reliées à l'utilisateur (manager) -->

            </div>
        </div>
    </div>
    <!-- MANAGE FAN END -->

</div>


<div class="container">


    <div class="tabs-container">
        <ul class="nav nav-tabs">
            <li class="active"><a data-toggle="tab" href="#tab-1" data-trad="lbl_tab_orders">Orders</a></li>
            <li class=""><a data-toggle="tab" href="#tab-2" data-trad="lbl_tab_manage_fans">Manage fans</a></li>
        </ul>
        <div class="tab-content">
            <div id="tab-1" class="tab-pane active">
                <div class="panel-body">




                    <div class="col-lg-12 " id="div_orders_">

                        <div class="ibox float-e-margins">
                            <div class="ibox-title">
                                <h5><span data-trad="title_export_identite">Export des données</span></h5>
                                <div class="ibox-tools">
                                    <a class="collapse-link">
                                        <i class="fa fa-chevron-up"></i>
                                    </a>

                                </div>
                            </div>
                            <div class="ibox-content">


                                <div class="row">
                                    <div class="col-md-6 col-sm-6 col-xs-12">

                                        <div class="form-group" id="div_chkTypes">
                                            <label class="control-label col-sm-4" for="chkTypes" data-trad="basket_state">
                                                Etat panier
                                            </label>

                                            <div class="col-sm-8 ">
                                                <select id="selectBasketState" name="selectBasketState" class="form-control" multiple="multiple">
                                                </select>
                                            </div>
                                        </div>
                                    </div>



                                    <div class="col-md-6 col-sm-6 col-xs-12">

                                      <div class="form-group" id="div_chkPlacesValides">
                                         <div class="col-sm-8 ">
                                             <label class="checkbox-inline">
                                                <input name="chkValidPlaces" type="checkbox"/>
                                                <span data-trad="lbl_show_valid_place">Ne voir que les places valides</span>
                                            </label>
                                        </div>

                                    </div>
                                </div>
                            </div>

                            <label data-trad="lbl_export_datas_text">Lister les données pour l'export. Attention cette méthode peut être assez longue.</label>
                            <input type="button" class="btn btn-success" id="launchDatasToExport" value="GO" />


                            <!-- tableau liste des commandes reliées à l'utilisateur -->
                            <div id="orders_export_wrapper" class=" hide table-responsive col-lg-12" role="grid">
                                <table id="orders_export_list_table" class="table table-striped table-bordered dataTable"
                                aria-describedby="orders_export_list_info" width="100%">
                                <thead>
                                    <tr role="row">
                                        <th><span data-trad="lbl_identite_table">identite</span></th>
                                        <th><span data-trad="lbl_name_table">Nom</span></th>
                                        <th><span data-trad="lbl_firstname_table">Prénom</span></th>
                                        <th><span data-trad="lbl_postalcode_table">PostalCode</span></th>
                                        <th><span data-trad="lbl_phone1_table">PhoneNumber1</span></th>
                                        <th><span data-trad="lbl_phone2_table">PhoneNumber2</span></th>
                                        <th><span data-trad="lbl_phone3_table">PhoneNumber3</span></th>
                                        <th><span data-trad="lbl_phone4_table">PhoneNumber4</span></th>
                                        <th><span data-trad="lbl_phone5_table">PhoneNumber5</span></th>
                                        <th><span data-trad="lbl_phone6_table">PhoneNumber6</span></th>
                                        <th><span data-trad="lbl_phone7_table">PhoneNumber7</span></th>
                                        <th><span data-trad="lbl_address1_table">Address1</span></th>
                                        <th><span data-trad="lbl_address2_table">Address2</span></th>
                                        <th><span data-trad="lbl_address3_table">Address3</span></th>
                                        <th><span data-trad="lbl_address4_table">Address4</span></th>
                                        <th><span data-trad="lbl_city_table">City</span></th>
                                        <th><span data-trad="lbl_country_table">Country</span></th>
                                        <th><span data-trad="lbl_identite_table">identite_id</span></th>
                                        <th><span data-trad="lbl_aboid_table">aboId</span></th>
                                        <th><span data-trad="lbl_cmdid_table">commande_id</span></th>
                                        <th><span data-trad="lbl_cmdetat_table">etat</span></th>
                                        <th><span data-trad="lbl_formuleabo_table">formuleAboName</span></th>
                                        <th><span data-trad="lbl_amount">montant</span></th>
                                        <th><span data-trad="detail_command_seat">Seat</span></th>
                                        <th><span data-trad="detail_command_rank">Rank</span></th>
                                        <th><span data-trad="lbl_categname_table">Categ_name</span></th>
                                        <th><span data-trad="lbl_typetarif_table">Type_tarif_name</span></th>
                                        <th><span data-trad="detail_command_zone">Zone_name</span></th>
                                        <th><span data-trad="detail_command_section">Section_name</span></th>
                                        <th><span data-trad="lbl_numbillet_table">numbillet</span></th>
                                        <th><span data-trad="lbl_codebarre_table">codeBarre</span></th>
                                    </tr>
                                </thead>
                                <tfoot>
                                    <tr>
                                        <th><span data-trad="lbl_identite_table">identite</span></th>
                                        <th><span data-trad="lbl_name_table">Nom</span></th>
                                        <th><span data-trad="lbl_firstname_table">Prénom</span></th>
                                        <th><span data-trad="lbl_postalcode_table">PostalCode</span></th>
                                        <th><span data-trad="lbl_phone1_table">PhoneNumber1</span></th>
                                        <th><span data-trad="lbl_phone2_table">PhoneNumber2</span></th>
                                        <th><span data-trad="lbl_phone3_table">PhoneNumber3</span></th>
                                        <th><span data-trad="lbl_phone4_table">PhoneNumber4</span></th>
                                        <th><span data-trad="lbl_phone5_table">PhoneNumber5</span></th>
                                        <th><span data-trad="lbl_phone6_table">PhoneNumber6</span></th>
                                        <th><span data-trad="lbl_phone7_table">PhoneNumber7</span></th>
                                        <th><span data-trad="lbl_address1_table">Address1</span></th>
                                        <th><span data-trad="lbl_address2_table">Address2</span></th>
                                        <th><span data-trad="lbl_address3_table">Address3</span></th>
                                        <th><span data-trad="lbl_address4_table">Address4</span></th>
                                        <th><span data-trad="lbl_city_table">City</span></th>
                                        <th><span data-trad="lbl_country_table">Country</span></th>
                                        <th><span data-trad="lbl_identite_table">identite_id</span></th>
                                        <th><span data-trad="lbl_aboid_table">aboId</span></th>
                                        <th><span data-trad="lbl_cmdid_table">commande_id</span></th>
                                        <th><span data-trad="lbl_cmdetat_table">etat</span></th>
                                        <th><span data-trad="lbl_formuleabo_table">formuleAboName</span></th>
                                        <th><span data-trad="lbl_amount">montant</span></th>
                                        <th><span data-trad="detail_command_seat">Seat</span></th>
                                        <th><span data-trad="detail_command_rank">Rank</span></th>
                                        <th><span data-trad="lbl_categname_table">Categ_name</span></th>
                                        <th><span data-trad="lbl_typetarif_table">Type_tarif_name</span></th>
                                        <th><span data-trad="detail_command_zone">Zone_name</span></th>
                                        <th><span data-trad="detail_command_section">Section_name</span></th>
                                        <th><span data-trad="lbl_numbillet_table">numbillet</span></th>
                                        <th><span data-trad="lbl_codebarre_table">codeBarre</span></th>

                                    </tr>
                                </tfoot>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                        <!-- Fin tableau liste des commandes -->


                    </div>

                </div>
            </div>
        </div>
    </div>
    <div id="tab-2" class="tab-pane">
        <div class="panel-body">


            <div class="col-lg-6">
                <h3 data-trad="title_lbl_persons_attached">Liste des personnes rattachées</h3>
            </div>


            <div class="col-lg-12" id="div_persons_attached">

                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <h5><span data-trad="title_persons_attached"></span></h5>
                        <div class="ibox-tools">
                            <a class="collapse-link">
                                <i class="fa fa-chevron-up"></i>
                            </a>

                        </div>
                    </div>
                    <div class="ibox-content">


                    </div>
                </div>
            </div>




        </div>
    </div>
</div>


</div>




<div class="modal inmodal" id="myModal2" tabindex="-1" role="dialog" aria-hidden="true" data-keyboard="false" data-backdrop="false">
    <div class="modal-dialog modal-lg">
        <div class="modal-content animated flipInY">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                <h4 class="modal-title" data-trad="basket_tite">Panier</h4>

            </div>
            <div class="modal-body" id="basket">


                <div class="ibox">
                    <div class="ibox-title">
                        <span class="pull-right" data-trad="nb_items_in_basket">(<strong>5</strong>) éléments</span>
                        <h5 data-trad="title_items_basket">Items in your cart</h5>
                    </div>

                    <div class="ibox-content">
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-white" data-dismiss="modal"><span data-trad="btn_close_basket"></span></button>
                <button id="basketValid" type="button" class="btn btn-success" onclick="sendToBasket();"><span data-trad="btn_valid_basket">Valid basket</span>  </button>
            </div>
        </div>
    </div>
</div>





<!-- Modal -->
<div class="modal fade" id="modalAddChildFan" tabindex="-1" role="dialog" aria-labelledby="ChildFan" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title"></h4>
            </div>
            <div class="modal-body">
            </div>
        </div>
    </div>
</div>



<div class="modal fade" id="modalUpdateChildFan" tabindex="-1" role="dialog" aria-labelledby="ChildFan" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title"></h4>
            </div>
            <div class="modal-body">

            </div>
        </div>
    </div>
</div>



<div id="commentsPageBas" class="row">
    <asp:Literal ID="litForCommentaireBas" runat="server"></asp:Literal>
</div>

</div>

<script src="/assets/LIBS/formvalidation/0.6.3/dist/js/formValidation.js"></script>
<script src="/assets/LIBS/formvalidation/0.6.3/dist/js/framework/bootstrap.min.js"></script>
</asp:Content>
