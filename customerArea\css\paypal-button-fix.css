/* 
 * Styles pour forcer le bouton PayPal officiel à avoir la même taille que le bouton Facebook
 * Ce fichier corrige les styles par défaut de l'API PayPal pour qu'ils correspondent à votre design
 */

/* Conteneur principal du bouton PayPal */
#manual-paypal-btn {
    width: 100% !important;
    display: block !important;
    box-sizing: border-box !important;
    margin: 0 !important;
}

/* Tous les éléments enfants du bouton PayPal */
#manual-paypal-btn * {
    width: 100% !important;
    box-sizing: border-box !important;
}

/* Styles spécifiques pour les iframes PayPal */
#manual-paypal-btn iframe {
    width: 100% !important;
    height: auto !important;
    min-height: 40px !important;
    border: none !important;
}

/* Styles pour les boutons générés par PayPal */
#manual-paypal-btn button,
#manual-paypal-btn .paypal-button,
#manual-paypal-btn [role="button"],
#manual-paypal-btn a {
    width: 100% !important;
    display: block !important;
    padding: 10px 15px !important;
    font-size: 14px !important;
    line-height: 1.42857143 !important;
    border-radius: 4px !important;
    border: 1px solid #0070ba !important;
    background-color: #0070ba !important;
    color: white !important;
    text-align: center !important;
    text-decoration: none !important;
    cursor: pointer !important;
    box-sizing: border-box !important;
    font-family: inherit !important;
    font-weight: normal !important;
}

/* Hover states pour les boutons PayPal */
#manual-paypal-btn button:hover,
#manual-paypal-btn .paypal-button:hover,
#manual-paypal-btn [role="button"]:hover,
#manual-paypal-btn a:hover {
    background-color: #005ea6 !important;
    border-color: #005ea6 !important;
    color: white !important;
    text-decoration: none !important;
    filter: brightness(1.1) !important;
}

/* Focus states pour les boutons PayPal */
#manual-paypal-btn button:focus,
#manual-paypal-btn .paypal-button:focus,
#manual-paypal-btn [role="button"]:focus,
#manual-paypal-btn a:focus {
    background-color: #005ea6 !important;
    border-color: #005ea6 !important;
    color: white !important;
    text-decoration: none !important;
    outline: none !important;
    box-shadow: 0 0 0 2px rgba(0, 112, 186, 0.25) !important;
}

/* Active states pour les boutons PayPal */
#manual-paypal-btn button:active,
#manual-paypal-btn .paypal-button:active,
#manual-paypal-btn [role="button"]:active,
#manual-paypal-btn a:active {
    background-color: #004c8a !important;
    border-color: #004c8a !important;
    color: white !important;
    text-decoration: none !important;
}

/* Styles pour les conteneurs PayPal spécifiques */
#manual-paypal-btn .paypal-login-container,
#manual-paypal-btn .paypal-login-button-container {
    width: 100% !important;
    display: block !important;
}

/* Styles pour les images/logos PayPal */
#manual-paypal-btn img {
    max-height: 20px !important;
    vertical-align: middle !important;
    margin-right: 8px !important;
}

/* Styles pour le texte du bouton */
#manual-paypal-btn .paypal-button-text,
#manual-paypal-btn span {
    color: white !important;
    font-size: 14px !important;
    font-weight: normal !important;
}

/* Responsive - pour les petits écrans */
@media (max-width: 768px) {
    #manual-paypal-btn button,
    #manual-paypal-btn .paypal-button,
    #manual-paypal-btn [role="button"],
    #manual-paypal-btn a {
        padding: 12px 15px !important;
        font-size: 16px !important;
    }
}

/* Styles pour s'assurer que le bouton PayPal a la même hauteur que Facebook */
#divConnectPayPal {
    margin-bottom: 15px;
}

#divConnectPayPal #manual-paypal-btn {
    min-height: 40px !important;
}

/* Styles pour masquer les éléments indésirables de PayPal */
#manual-paypal-btn .paypal-logo-small,
#manual-paypal-btn .paypal-powered-by {
    display: none !important;
}

/* Styles pour les animations de chargement */
#manual-paypal-btn.loading {
    opacity: 0.7;
    pointer-events: none;
}

#manual-paypal-btn.loading::after {
    content: "Chargement...";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 14px;
}
