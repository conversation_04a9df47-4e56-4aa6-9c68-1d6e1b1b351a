<%@ Page Language="C#" AutoEventWireup="true" %>
<%@ Import Namespace="System.Collections.Generic" %>
<%@ Import Namespace="customerArea.classes" %>
<%@ Import Namespace="utilitaires2010" %>
<%@ Import Namespace="customerArea.App_Code" %>

<!DOCTYPE html>
<html>
<head>
    <title>Debug Configuration PayPal - Structure 0426</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.1.0/css/bootstrap.min.css">
</head>
<body>
    <div class="container mt-5">
        <div class="card">
            <div class="card-header bg-danger text-white">
                <h3>🔍 Debug Configuration PayPal - Structure 0426</h3>
            </div>
            <div class="card-body">
                <%
                try 
                {
                    string structureId = "0426";
                    string plateformCode = "Customer";
                    string lang = "fr";
                    int idpa = 0;
                    int resulEvent = 0;

                    List<CustomJsonSettings> listJsonSettingsToMerge = new List<CustomJsonSettings>
                    {
                        new CustomJsonSettings() { KeyOfJsonPath = "physicalPathOfSettingsJSON", IsDefault = true },
                        new CustomJsonSettings() { KeyOfJsonPath = "physicalPathOfSettingsJSON", IsDefault = false },
                        new CustomJsonSettings() { KeyOfJsonPath = "physicalPathOfSettingsPlateformJSON", IsDefault = false }
                    };

                    dynamic globalPlateform = utilitaires2010.Initialisations.GetMergeSettings(
                        listJsonSettingsToMerge, 
                        int.Parse(structureId), 
                        resulEvent, 
                        idpa, 
                        plateformCode, 
                        lang
                    );

                    // Récupérer Client ID
                    string clientId = "";
                    string clientSecret = "";
                    string returnUrl = "";
                    
                    try
                    {
                        dynamic paypalAppIdDynamic = globalPlateform.login.payPal.appid.Value;
                        if (globalPlateform.customer != null && 
                            globalPlateform.customer.login != null && 
                            globalPlateform.customer.login.payPal != null && 
                            globalPlateform.customer.login.payPal.appid != null)
                        {
                            paypalAppIdDynamic = globalPlateform.customer.login.payPal.appid.Value;
                        }
                        clientId = paypalAppIdDynamic?.ToString() ?? "Non configuré";
                    }
                    catch
                    {
                        clientId = "Erreur de récupération";
                    }

                    try
                    {
                        dynamic paypalSecretDynamic = globalPlateform.login.payPal.secret.Value;
                        if (globalPlateform.customer != null && 
                            globalPlateform.customer.login != null && 
                            globalPlateform.customer.login.payPal != null && 
                            globalPlateform.customer.login.payPal.secret != null)
                        {
                            paypalSecretDynamic = globalPlateform.customer.login.payPal.secret.Value;
                        }
                        clientSecret = paypalSecretDynamic?.ToString() ?? "Non configuré";
                    }
                    catch
                    {
                        clientSecret = "Erreur de récupération";
                    }

                    try
                    {
                        dynamic paypalReturnUrlDynamic = globalPlateform.login.payPal.returnurl.Value;
                        if (globalPlateform.customer != null && 
                            globalPlateform.customer.login != null && 
                            globalPlateform.customer.login.payPal != null && 
                            globalPlateform.customer.login.payPal.returnurl != null)
                        {
                            paypalReturnUrlDynamic = globalPlateform.customer.login.payPal.returnurl.Value;
                        }
                        returnUrl = paypalReturnUrlDynamic?.ToString()?.Replace("[structureid]", structureId) ?? "Non configuré";
                    }
                    catch
                    {
                        returnUrl = "Erreur de récupération";
                    }
                %>

                <div class="alert alert-warning">
                    <strong>⚠️ PROBLÈME DÉTECTÉ :</strong> Vous utilisez un Client ID de PRODUCTION au lieu de SANDBOX !
                </div>

                <table class="table table-bordered">
                    <tr>
                        <td><strong>Structure ID :</strong></td>
                        <td><span class="badge badge-info"><%=structureId%></span></td>
                    </tr>
                    <tr>
                        <td><strong>Client ID actuel :</strong></td>
                        <td>
                            <code><%=clientId%></code>
                            <% if (clientId.StartsWith("AcS20Beg")) { %>
                                <span class="badge badge-danger ml-2">❌ PRODUCTION</span>
                            <% } else if (clientId.StartsWith("AZKOsIyc")) { %>
                                <span class="badge badge-success ml-2">✅ SANDBOX</span>
                            <% } %>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Client ID attendu (sandbox) :</strong></td>
                        <td><code>AZKOsIycBytFxeUfRdiRDtI6rmqT8m96xaw4KBymF9hk44HjSw_ubrQwdI1QZz_-RijZvaIQKLAq93Jw</code></td>
                    </tr>
                    <tr>
                        <td><strong>Return URL :</strong></td>
                        <td><code><%=returnUrl%></code></td>
                    </tr>
                </table>

                <div class="alert alert-info">
                    <h5>🛠️ Pour corriger :</h5>
                    <ol>
                        <li>Ouvrez votre fichier de configuration externe</li>
                        <li>Trouvez la section PayPal pour la structure 0426</li>
                        <li>Remplacez le Client ID par : <code>AZKOsIycBytFxeUfRdiRDtI6rmqT8m96xaw4KBymF9hk44HjSw_ubrQwdI1QZz_-RijZvaIQKLAq93Jw</code></li>
                        <li>Sauvegardez et testez à nouveau</li>
                    </ol>
                </div>

                <%
                }
                catch (Exception ex)
                {
                %>
                    <div class="alert alert-danger">
                        <strong>Erreur :</strong> <%=ex.Message%>
                    </div>
                <%
                }
                %>
            </div>
        </div>
    </div>
</body>
</html>
