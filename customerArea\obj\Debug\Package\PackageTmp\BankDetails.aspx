﻿<%@ Page Title="" Language="C#" MasterPageFile="~/pagemaster.Master" AutoEventWireup="true" CodeBehind="BankDetails.aspx.cs" Inherits="customerArea.BankDetails" %>

<asp:Content ID="Content1" ContentPlaceHolderID="ctPlaceHold" runat="server">

<h2 data-trad="title_my_bank_details">My bank details</span></h2>
<!-- commentaire haut -->
<div id="commentsPageHaut"><asp:Literal ID="litForCommentaireHaut" runat="server" ></asp:Literal></div>


<!-- ACCOUNT START -->
<div class="card">
    <div class="card-header" id="headingBank1">
        <h5 class="mb-0">

            <a href="#" class="text-left d-block btn btn-link "  data-toggle="collapse" data-target="#collapseBank1" aria-expanded="true" aria-controls="collapseBank1">
                <div class="row no-gutters align-items-center">
                    <div class="col-auto">
                        <svg class="plusoumoins img-fluid" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 16.4 16.3" style="enable-background:new 0 0 16.4 16.3;" xml:space="preserve"><g><g><path class="st0" d="M13.4,9.2H3c-0.6,0-1-0.4-1-1s0.4-1,1-1h10.3c0.6,0,1,0.4,1,1S13.9,9.2,13.4,9.2z"></path></g><g><g><path class="st0" d="M9.5,16.3c-0.2,0-0.4-0.2-0.5-0.4c0-0.3,0.1-0.5,0.4-0.6c3.4-0.6,5.9-3.6,5.9-7.1c0-4-3.2-7.2-7.2-7.2 S1,4.2,1,8.2c0,3.5,2.5,6.5,5.9,7.1c0.3,0,0.5,0.3,0.4,0.6c0,0.3-0.3,0.5-0.6,0.4C2.8,15.6,0,12.2,0,8.2C0,3.7,3.7,0,8.2,0 c4.5,0,8.2,3.7,8.2,8.2C16.4,12.2,13.5,15.6,9.5,16.3C9.6,16.2,9.6,16.3,9.5,16.3z"></path></g></g></g><g class="verticalbar"><g><path class="st0" d="M8.2,14.4c-0.6,0-1-0.4-1-1V3c0-0.6,0.4-1,1-1s1,0.4,1,1v10.3C9.2,13.9,8.7,14.4,8.2,14.4z"></path></g></g></svg>
                    </div>
                    <div class="col card-customertitle">
                        <span class="font-weight-bold" data-trad="lbl_iban_bic">IBAN BIC</span>
                    </div>
                </div>
            </a>
        </h5>
    </div>

    <div id="collapseBank1" class="collapse show" aria-labelledby="headingBank1" >
        <div class="card-body"> 
            <!-- -->
            <div class="form-group">
                <span data-trad="lbl_modify_bank_details">Modify my bank details ?</span>
                <span class="ml-2 material-switch">
                    <input id="switchUpdateBankDetails" name="someSwitchOption001" type="checkbox"/>
                    <label for="switchUpdateBankDetails" class="bg-success"></label>
                </span>
            </div>
            <!-- titulaire -->
            <div class="switchhide">
                <div class="form-group d-none">
                    <label for="lbHolder" class="sr-only" data-trad="lbl_holder">Holder</label>
                    <input type="text" class="form-control" name="titulaire" data-tradplaceholder="placeholder_holder" required/>
                </div>
            </div>
            <!-- iban -->
            <div class="form-group">
                <label for="lbIban" class="sr-only" data-trad="lbl_iban">IBAN</label>
                <input type="text" class="form-control" data-tradplaceholder="lbl_iban" id="iban" name="iban" disabled required/>
            </div>
            <!-- bic -->
            <div class="form-group">
                <label for="lbBic" class="sr-only" data-trad="lbl_bic">BIC</label>
                <input type="text" class="form-control" id="bic" name="bic" disabled data-tradplaceholder="placeholder_bic" required/>
            </div>
            <div class="switchhide">
                <div class="form-check d-none">
                    <input type="checkbox" class="form-check-input" name="chkConditionsCertificate" id='chkConditionsCertificate'>
                    <label class="form-check-label" for="chkConditionsCertificate" data-trad="lbl_conditions_certificate" required>I certify that the information provided is accurate</label>
                </div>
            </div>
           
        </div>
    </div>
</div>
<!-- ACCOUNT END -->

<div class="row">
    <div class="col-sm-6 mt-2 text-center text-sm-left">
        <a href="" class="btn btn-secondary buttonBack" data-trad="btn_back">Back</a>
    </div>

    <div class="col-sm-6 mt-2 text-center text-sm-right">
        <a href="" id="buttonCancel" class="btn btn-secondary showWhenWidget" data-trad="btn_cancel_and_close">Cancel and close</a>
        <a id="btnValiderBankDetails" class="btn btn-primary" data-trad="btn_update" href="">Update</a>
    </div>
</div>
<!-- commentaire bas -->
<div id="commentsPageBas" class="small"><asp:Literal ID="litForCommentaireBas" runat="server" ></asp:Literal></div>

<asp:HyperLink ID="hlHomePage" runat="server" class="d-none">homePage</asp:HyperLink>


</asp:Content>
