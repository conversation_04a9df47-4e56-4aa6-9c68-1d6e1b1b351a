﻿
var arrEvents = new Array();

$(document).ready(function () {
    LoadEventsAndSessionsList();

    FillEventsList();

    $("#selectManifestations").on('change', function () {

        FillSessionsList();
    });

    $("#selectSeances").on('change', function () {
        FillCategoriesList();
    });



    $("#btnValiderWishList").on('click', function (e) {
        e.preventDefault();

        InsertWishList();
    });

});




                

function LoadEventsAndSessionsList() {

   
    $.ajax({
        type: "POST",
        url: 'Commons.asmx/LoadEventsAndSessionsList',
        contentType: 'application/json; charset=utf-8',
        dataType: "json",
        async: false,
        success: function (response) {

            if (response.d != null) {

                $.each(response.d, function () {

                    var evts = {};
                    evts.EventId = this.EventId;
                    evts.EventName = this.EventName;
                    var arrSessions = new Array();

                    $.each(this.ListSessions, function () {
                        
                        var sessions = {};
                        sessions.SessionId = this.SessionId;
                        sessions.sSessionStartDate = this.sSessionStartDate;

                        var arrCategs = new Array();
                        $.each(this.ListCategories, function () {

                            var categ = {};
                            categ.CategId = this.CategId;
                            categ.Category_name = this.Category_name;

                            arrCategs.push(categ);
                        });

                        sessions.LstCategs = arrCategs;


                        arrSessions.push(sessions);
                    });


                    evts.LstSessions = arrSessions;
                    arrEvents.push(evts);

                   
                });


                /*
var optionsManifs = $("#selectManifestations");
                $.each(response.d, function () {
                    optionsManifs.append(new Option(this.EventName, this.EventName));


                  
                    var optionsSeances = $("#selectSeances");
                    $.each(this.ListSessions, function () {
                        optionsSeances.append(new Option(this.sSessionStartDate, this.SessionId));
                    });
                  
                }); 
                */


              
            }

        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            // ShowError("error", XMLHttpRequest.responseJSON.Message + "<br />" + XMLHttpRequest.responseJSON.StackTrace + "\r\n", "alert alert-danger alert-dismissable", 5000);
            $('#modalForWaiting').modal('hide');
            console.log(XMLHttpRequest.responseJSON.Message + "\r\n" + XMLHttpRequest.responseJSON.StackTrace);
        }
    });
}




function FillEventsList() {
    var optionsManifs = $("#selectManifestations");

    //optionsManifs.find('option').remove().end();
    optionsManifs.empty();

    optionsManifs.append(new Option("Choisissez une manifestation", 0));
    $.each(arrEvents, function () {
        optionsManifs.append(new Option(this.EventName, this.EventId));
    });

    optionsManifs.chosen();

}


function FillSessionsList() {
    var optionsSeances = $("#selectSeances");


    var manifIdSelected = $("#selectManifestations option:selected").val();
    //séances de la manifs sélectionnée
    var arrSessionOfEventSelect = $.grep(arrEvents, function (a) {
        return a.EventId == manifIdSelected;
    })


    //optionsSeances.find('option').remove().end();
    optionsSeances.empty();
    optionsSeances.append(new Option("Choisissez une séance", 0));

    $.each(arrSessionOfEventSelect[0].LstSessions, function () {
        optionsSeances.append(new Option(this.sSessionStartDate, this.SessionId));
    });


    optionsSeances.chosen();

}


function FillCategoriesList() {

    var optionsCategs = $("#selectCategories");

    var manifIdSelected = $("#selectManifestations option:selected").val();
    //séances de la manifs sélectionnée
    var arrSessionOfEventSelect = $.grep(arrEvents, function (a) {
        return a.EventId == manifIdSelected;
    })


    var seanceIdSelected = $("#selectSeances option:selected").val();
    //séances de la manifs sélectionnée
    var arrCategsOfSeanceSelect = $.grep(arrSessionOfEventSelect[0].LstSessions, function (a) {
        return a.SessionId == seanceIdSelected;
    })


    //optionsSeances.find('option').remove().end();
    optionsCategs.empty();
    optionsCategs.append(new Option("Choisissez une catégorie", 0));

    if (arrCategsOfSeanceSelect.length > 0)
    {
        $.each(arrCategsOfSeanceSelect[0].LstCategs, function () {
            optionsCategs.append(new Option(this.Category_name, this.CategId));
        });
    }
   
    optionsCategs.chosen();

}



