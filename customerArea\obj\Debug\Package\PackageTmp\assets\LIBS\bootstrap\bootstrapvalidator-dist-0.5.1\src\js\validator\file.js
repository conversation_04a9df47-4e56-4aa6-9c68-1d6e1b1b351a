(function($) {
    $.fn.bootstrapValidator.i18n.file = $.extend($.fn.bootstrapValidator.i18n.file || {}, {
        'default': 'Please choose a valid file'
    });

    $.fn.bootstrapValidator.validators.file = {
        html5Attributes: {
            extension: 'extension',
            maxsize: 'maxSize',
            message: 'message',
            type: 'type'
        },

        /**
         * Validate upload file. Use HTML 5 API if the browser supports
         *
         * @param {BootstrapValidator} validator The validator plugin instance
         * @param {jQuery} $field Field element
         * @param {Object} options Can consist of the following keys:
         * - extension: The allowed extensions, separated by a comma
         * - maxSize: The maximum size in bytes
         * - message: The invalid message
         * - type: The allowed MIME type, separated by a comma
         * @returns {Boolean}
         */
        validate: function(validator, $field, options) {
            var value = $field.val();
            if (value === '') {
                return true;
            }

            var ext,
                extensions = options.extension ? options.extension.toLowerCase().split(',') : null,
                types      = options.type      ? options.type.toLowerCase().split(',')      : null,
                html5      = (window.File && window.FileList && window.FileReader);

            if (html5) {
                // Get FileList instance
                var files = $field.get(0).files,
                    total = files.length;
                for (var i = 0; i < total; i++) {
                    // Check file size
                    if (options.maxSize && files[i].size > parseInt(options.maxSize, 10)) {
                        return false;
                    }

                    // Check file extension
                    ext = files[i].name.substr(files[i].name.lastIndexOf('.') + 1);
                    if (extensions && $.inArray(ext.toLowerCase(), extensions) === -1) {
                        return false;
                    }

                    // Check file type
                    if (files[i].type && types && $.inArray(files[i].type.toLowerCase(), types) === -1) {
                        return false;
                    }
                }
            } else {
                // Check file extension
                ext = value.substr(value.lastIndexOf('.') + 1);
                if (extensions && $.inArray(ext.toLowerCase(), extensions) === -1) {
                    return false;
                }
            }

            return true;
        }
    };
}(window.jQuery));
