
var sAjaxSourceUrl = "fans.aspx/";
var oTableOrdersList;

var totalAmount = 0;
var cmdCharges = new Array();
var aboCharges = new Array();
var nbInBasket = 0;
var seuil = 0;
var solde = 0;
var nbmax = 10;
var deviseCode = "&euro;";
$(document).ready(function () {

    if ($('head').attr('devisecode') != undefined)
        deviseCode = $('head').attr('devisecode');

    FillStateSelect();
    $('#launchDatasToExport').val(ReadXmlTranslate("btn_valider"));
    GetDroitsFacture();
    GetOrdersList();
    GetTemplateFormAddFan();
    GetPersonsAttachedToManager();
    GetIdentiteAccompte();
    
    $('#selectBasketState').chosen({
        no_results_text: ReadXmlTranslate("lbl_aucun_resultat"),
        placeholder_text_multiple: ReadXmlTranslate("lbl_select_etat_panier"),
        search_contains: true,
        width: "95%"
    });
    $('#launchDatasToExport').on('click', function (e) {
        e.preventDefault();
        //demande standart de liege pour l'export 
        // Les gérants aimeraient pouvoir faire un export excel des données des membres (places, coordonnées, n° abo,…)
        GetAllIdentiteCommandesFans();
    });

    var tmpClicked = false;
    var origintable;
    var defaultLength;
    origintable = $('#orders_list_table').DataTable().data();
    defaultLength = $('#orders_list_table').DataTable().settings()[0]._iDisplayLength;
    $('input[type="checkbox"][name="nb_cmd_payees"]').on('click', function (e) {

        if (!tmpClicked)
        {
            /*origintable = $('#orders_list_table').DataTable().data();
            defaultLength = $('#orders_list_table').DataTable().settings()[0]._iDisplayLength;*/
            tmpClicked = true;
        }

        var table = $('#orders_list_table').DataTable();
        table.settings()[0]._iDisplayLength = -1;
        if ($(this).is(':checked')) {
            var nbAboPayeOuEdite = $.grep(table.data(), function (a) { return a.nbAboPayeOuEdite > 0 });
            table.clear().rows.add(nbAboPayeOuEdite).draw();
        } else {
            table.clear().rows.add(origintable).draw();
        }

        $('.checkAbo').on('click', function () {
            var idi = $(this).data('idt_');
            var hash = $(this).data('hash');
            CheckAbo(idi, hash);
        });

        DeplierReplier();
        AttachEventChkOrder();
        $('#orders_list_table_length option:eq(0)').prop("selected", true);
        $('#orders_list_table_length select').change();
    });


    $('input[type="checkbox"][name="nb_cmd_reservees"]').on('click', function (e) {
        if (!tmpClicked) {
            /*origintable = $('#orders_list_table').DataTable().data();
            defaultLength = $('#orders_list_table').DataTable().settings()[0]._iDisplayLength;*/
            tmpClicked = true;
        }

        var table = $('#orders_list_table').DataTable();
        table.settings()[0]._iDisplayLength = -1;

        if ($(this).is(':checked')) {
            var nbAboReserves = $.grep(table.data(), function (a) { return a.nbAboReserves > 0 });
            table.clear().rows.add(nbAboReserves).draw();
        } else {
            table.clear().rows.add(origintable).draw();
        }

        //réattribut le click du boutton
        $('.checkAbo').on('click', function () {
            var idi = $(this).data('idt_');
            var hash = $(this).data('hash');
            CheckAbo(idi, hash);
        });

        //.settings()[0]._iDisplayLength = defaultLength;
        DeplierReplier();
        AttachEventChkOrder();
        $('#orders_list_table_length option:eq(0)').prop("selected", true);
        $('#orders_list_table_length select').change();
    });


    //lors du click on coche toutes les checkbox
    $('input[name="chkAllRowsOrdersAttached"]').on('click', function (e) {
        var checkEvents = false;

        if ($(this).is(':checked')) {
            checkEvents = true;
        } else {
            checkEvents = false;
        }

        $.each($('input[name="chkOrderAttachedId"]'), function (indx, itm) {

         if ($('input[name="chkOrderAttachedId"]:checked').length < nbmax) {
            $(itm).prop('checked', checkEvents);
        } else {
         ShowModalError('modalMessage', ReadXmlTranslate("title_error_check_multi_abo"), ReadXmlTranslate("msg_error_check_multi_abo").replace('[nbMax]', nbmax), 'alert alert-danger', 3000);
         return false;
     }     
 });

        var table = $('#orders_list_table').dataTable();
        if (GetOrderIDSelected(table).length > 0) {
            $('#btnChkOrdersSelected').removeClass('hide');
        } else {
            $('#btnChkOrdersSelected').addClass('hide');
            $('#btnAddBasketOrdersSelected').addClass('hide');
        }

    }); //fin du click chkAllRowsEventId


    $('#btnChkOrdersSelected').on('click', function (e) {
        e.preventDefault();

        var table = $('#orders_list_table').dataTable();
        var oSettings = table.fnSettings();
        var lengthDisplay = oSettings._iDisplayLength;

        $('#orders_list_table_length option:eq(-1)').prop("selected", true);
        $('#orders_list_table_length select').change();

        $.each(GetOrderIDSelected(table), function (indx, item) {
            CheckAbo(item.id, item.hash, prct);
        });

        oSettings._iDisplayLength = lengthDisplay;

        $('#btnAddBasketOrdersSelected').removeClass('hide');
    });

    $('#btnAddBasketOrdersSelected').on('click', function (e) {
        e.preventDefault();
        $.each($('.addBasket'), function (indx, item) {
            if ($(item).parents().find('input[data-id="' + $(item).data('idt') + '"]').is(':checked')) {
                console.log($(item).data('idt'));
                $(item).click();
            }
        });

    });

    $('#removeAllBasket').on('click', function (e) {
        e.preventDefault();
        totalAmount = 0;
        var aboInBasket = $.grep(aboCharges, function (a) { return a.inbasket == true });
        $.each(aboInBasket, function (indx, item) { item.inbasket = false });
        RefreshBasket();
    });
});


function FillStateSelect(){
	var arrStates = [];
	arrStates.push({ value:"R", libelle : ReadXmlTranslate("lbl_reserver") });
	arrStates.push({ value:"B", libelle : ReadXmlTranslate("lbl_editer") });
	arrStates.push({ value:"P", libelle : ReadXmlTranslate("lbl_payer") });
	var options = $('#selectBasketState');
	$.each(arrStates, function() {
		options.append(new Option(this.libelle, this.value));
	});
}


var indxPb = 1;
var prct = parseFloat((indxPb / $('input[name="chkOrderAttachedId"]:checked').length) * 100).toFixed(0);
function updateProgress(percentage, current, max) {

    if ($('#myProgressbar').hasClass('hide'))
        $('#myProgressbar').removeClass('hide');

    if (percentage > 100) {
        percentage = 100;
        $('#myProgressbar').addClass('hide');
        var indxPb = 1;
        var prct = parseFloat((indxPb / $('input[name="chkOrderAttachedId"]:checked').length) * 100).toFixed(0);
    }


    var htmlprogress = '<span>' + parseFloat(percentage).toFixed(0) + ' %</span> <span class="pull-right">' + current + '/' + max + '</span>';
    $('#myProgressbar').css('width', percentage + '%');
    $('#myProgressbar').html(htmlprogress);
}



function GetOrderIDSelected(oTable) {
    var arrOrderSelected = new Array();

    $('input[name="chkOrderAttachedId"]:checked', oTable.fnGetNodes()).each(function () {
        var obj = {};
        obj.id = $(this).data('id');
        obj.hash = $(this).data('hash');
        arrOrderSelected.push(obj);
    });
    return arrOrderSelected;
}

function DeplierReplier() {

    $('a.replier').on('click', function (e) {
        e.preventDefault();
        var row = oTableOrdersList.row($(this).parents().find('td a[data-idt="' + $(this).data('idt') + '"]').parents('tr'));

        if (row.child.isShown()) {
            $(this).html('<i class="fa fa-plus"></i>')
            row.child.hide();
        } else {
            $(this).html('<i class="fa fa-minus"></i>')
            row.child.show();

            //si le boutton 'ajouter au panier' est desactiver alors on regarde si il est dans le panier                         
            if ($('#orders_list_table tr .ibox-title btn.addBasket[data-idt="' + $(this).data('idt') + '"] ').is('[disabled]')) {
                var identiteInBtn = $(this).data('idt');
                var isInBasket = $.grep($('#basket tr td'), function (a) {
                    return $(a).data('identid') == identiteInBtn
                })


                if (isInBasket.length == 0) {
                    //si il n'est pas dans le panier on le réactive                              
                    $('#orders_list_table tr .ibox-title btn.addBasket[data-idt="' + $(this).data('idt') + '"] ').removeAttr('disabled');
                }
            }

            //pour chaque commande de l'identite on peut replier sur le chevronup
            $('#orders_list_table .ibox#ibox-' + $(this).data('idt') + ' .collapse-cmd').click(function () {
                var ibox = $(this).closest('div.ibox');
                var button = $(this).find('i');
                var content = ibox.find('div.ibox-content');
                content.slideToggle(200);
                button.toggleClass('fa-chevron-up').toggleClass('fa-chevron-down');
                ibox.toggleClass('').toggleClass('border-bottom');
                setTimeout(function () {
                    ibox.resize();
                    ibox.find('[id^=map-]').resize();
                }, 50);
            });
        }
    });
}

function GetOrdersListForExport() {

    try {
        $.ajax({
            type: "POST",
            url: 'Commons.asmx/GetDatasForExport',
            contentType: 'application/json; charset=utf-8',
            dataType: "json",
            success: function (response) {
                $('#loading').html("");
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                console.log(XMLHttpRequest.responseJSON.Message + "\r\n" + XMLHttpRequest.responseJSON.StackTrace);
            }  
        });
    } catch (e) {
        console.log(e.Message);
    }

}



function GetOrdersList() {
    try {
        $.ajax({
            type: "POST",
            url: 'Commons.asmx/GetCommandsList',
            contentType: 'application/json; charset=utf-8',
            dataType: "json",
            success: function (response) {
                $('#loading').html("");

                if (response.d.length > 0) {
                    fillDataTableOrders(response.d);
                    $('#div_orders_attached').removeClass('hide');
                    DeplierReplier();
                    var arrObj = new Array();

                    if (sessionStorage.getItem("aboCharges") != null && sessionStorage.getItem("cmdCharges") != null) {
                        var panier = JSON.parse(sessionStorage.getItem("arrBasket"));
                        var aboCharges2 = JSON.parse(sessionStorage.getItem("aboCharges"));
                        cmdCharges = JSON.parse(sessionStorage.getItem("cmdCharges"));

                        //ajoute chaque couple identite/hash dans un tableau
                        $.each(aboCharges2, function (indx, item) {
                            if (item.inbasket) {
                                var Abojs = { identiteid: item.identiteid, hashKey: item.hash };
                                arrObj.push(Abojs);
                            }
                        });

                        $.ajax({
                            type: "POST",
                            async: false,
                            url: sAjaxSourceUrl + 'GetCommandsList', //appel la method Checabo pour chaque couple identite/hash 
                            contentType: 'application/json; charset=utf-8',
                            data: JSON.stringify({ lstAbo: arrObj }),
                            dataType: "json",
                            success: function (response) {
                                $.each(response.d, function (indx, item) {
                                    var itemTable = item;
                                    var commandReserverIsInTable = $.grep(item.listDossiersEntree, function (a) {
                                        return a.etat == "R"
                                    });

                                    if (commandReserverIsInTable.length > 0) {
                                        var aboCharges2 = JSON.parse(sessionStorage.getItem("aboCharges"));
                                        var aboOld = $.grep(aboCharges2, function (a) {
                                            return a.identiteid == itemTable.identiteid && a.inbasket == true;
                                        });

                                        if (aboOld.length == 1) {
                                            aboCharges.push(aboOld[0]);
                                        }
                                    }

                                });

                                if (aboCharges.length > 0) {
                                    RefreshBasket();
                                } else {
                                    sessionStorage.removeItem("aboCharges");
                                    sessionStorage.removeItem("arrBasket");
                                }

                            },
                            error: function (XMLHttpRequest, textStatus, errorThrown) {
                                console.log(XMLHttpRequest.responseJSON.Message + "\r\n" + XMLHttpRequest.responseJSON.StackTrace);
                            }
                        });

                    }

                }

            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                console.log(XMLHttpRequest.responseJSON.Message + "\r\n" + XMLHttpRequest.responseJSON.StackTrace);
            },
            complete: function () {
                $('#orders_list_table_length option:eq(0)').prop("selected", true);
                $('#orders_list_table_length select').change();
            }
        });
    } catch (e) {
        console.log(e.Message);
    }

}

function createCellPos(n) {
    var ordA = 'A'.charCodeAt(0);
    var ordZ = 'Z'.charCodeAt(0);
    var len = ordZ - ordA + 1;
    var s = "";
    while (n >= 0) {
        s = String.fromCharCode(n % len + ordA) + s;
        n = Math.floor(n / len) - 1;
    }
    return s;
}

///Rempli la datatable des commandes
function fillDataTableOrders(data) {

    //affiche le nombre de commandes payées
    //$('#nb_cmd_payees').html($.grep(oTableOrdersList.data(), function (a) { return a.nbAboPayeOuEdite > 0 }).length);
    //$('#nb_cmd_reservees').html($.grep(oTableOrdersList.data(), function (a) { return a.nbAboReserves > 0 }).length);
    $('#nb_cmd_payees').html($.grep(data, function (a) { return a.nbAboPayeOuEdite > 0 }).length);
    $('#nb_cmd_reservees').html($.grep(data, function (a) { return a.nbAboReserves > 0 }).length);

    oTableOrdersList = $('#orders_list_table').DataTable({
        "bDestroy": true,
        "bProcessing": false,
        "responsive": true,
        //"bSort": true,
        //"bFilter": true,
        "bAutoWidth": true,
        "iDisplayLength": 10,
        "aLengthMenu": [[10, 25, 50, 100, -1], [10, 25, 50, 100, ReadXmlTranslate('lbl_choose_all_datatable')]],
        "bDeferRender": true,
        "aaData": data,
        "oLanguage": GetLanguageDataTable(),
        "aaSorting": [[1, "asc"]],
        //"dom": '<"top"B><"clear"><"top"lf>rt<"bottom"ip><"clear">',
        "dom":"<'top'B>"+
                "<'row'<'col-sm-12 col-md-6'l><'col-sm-12 col-md-6'f>>" +
                "<'row'<'col-sm-12'tr>>" +
                "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
        // "dom": 'lBfrtip',
        "buttons": [
        {
            extend: 'excelHtml5',
            text: ReadXmlTranslate('lbl_export_excel'),
            exportOptions: {
                columns: [ 1, 2, 3, 4 ]
            },
            customize: function (xlsx) {
                var sheet = xlsx.xl.worksheets['sheet1.xml'];
                var lastCol = sheet.getElementsByTagName('col').length - 1;
                var colRange = createCellPos(lastCol) + '1';
                //Has to be done this way to avoid creation of unwanted namespace atributes.
                var afSerializer = new XMLSerializer();
                var xmlString = afSerializer.serializeToString(sheet);
                var parser = new DOMParser();
                var xmlDoc = parser.parseFromString(xmlString, 'text/xml');
                var xlsxFilter = xmlDoc.createElementNS('http://schemas.openxmlformats.org/spreadsheetml/2006/main', 'autoFilter');
                var filterAttr = xmlDoc.createAttribute('ref');
                filterAttr.value = 'A1:' + colRange;
                xlsxFilter.setAttributeNode(filterAttr);
                sheet.getElementsByTagName('worksheet')[0].appendChild(xlsxFilter);
            }
        },
        {
            extend: 'pdfHtml5',
            text: ReadXmlTranslate('lbl_export_pdf'),
            exportOptions: {
                columns: [1, 2, 3, 4]
            }

        },
        {
            extend: 'print',
            text: ReadXmlTranslate('lbl_export_print'),
            exportOptions: {
                columns: [1, 2, 3, 4]
            }
        },
        {
            extend: 'copyHtml5',
            text: ReadXmlTranslate('lbl_export_copy'),
            exportOptions: {
                columns: [1, 2, 3, 4]
            }
        }

        ],

        "aoColumns": [
        {
            "mDataProp": "Identite_id",
            "bSortable": false,
            "bSearchable": false,
            "mRender": function (data, type, full) {
                var html = '<label> <input type="checkbox" class="chkOrderAttachedId" name="chkOrderAttachedId" data-id="' + full.Identite_id + '" data-hash="' + full.hashKey + '" /><span class="lbl center"></span></label>';
                //  html += '<a href="" data-id="'+full.Identite_id+'" class="hide replier btn btn-success"><i class="fa fa-minus"></i></a>';
                return html;
            }
        },
        {
            "mDataProp": "Identite_id"
        },
        { "mDataProp": "SurName" },
        { "mDataProp": "FirstName" },
        {
            "mDataProp": "nbAboReserves",
            "mRender": function (data, type, full) {
                var html = '';
                for (var i = 0; i < full.nbAboPayeOuEdite; i++) {
                    html += '<span class="btn btn-circle btn-xs payerediter"><span class="hide">' + ReadXmlTranslate('lbl_payer_editer') + '</span></span> ';
                }


                for (var i = 0; i < full.nbAboReserves; i++) {
                    html += '<span class="btn btn-circle btn-xs reserver"><span class="hide">' + ReadXmlTranslate('lbl_reserver') + '</span></span>';
                }
                return html;
            }
        },
        {
            "mDataProp": "Identite_id",
            "mRender": function (data, type, full) {
                var html = '<btn class="checkAbo btn btn-primary" data-idt_=' + full.Identite_id + ' data-hash="' + full.hashKey + '">' + ReadXmlTranslate('btn_check_abo') + '</btn>';
                html += '<a href="" data-idt="' + full.Identite_id + '" class="hide replier btn btn-success"><i class="fa fa-minus"></i></a>';
                return html;
            }
        }
        ]
    });
    //
    $('.checkAbo').on('click', function () {
        var idi = $(this).data('idt_');
        var hash = $(this).data('hash');
        CheckAbo(idi, hash);
    });

    AttachEventChkOrder();
}

function AttachEventChkOrder() {
    $('input[name="chkOrderAttachedId"]').on('click', function () {
        if ($('input[name="chkOrderAttachedId"]:checked').length == 0) {
            $('#btnChkOrdersSelected').addClass('hide');
            $('#btnAddBasketOrdersSelected').addClass('hide');
        } else if ($('input[name="chkOrderAttachedId"]:checked').length > 0 && $('input[name="chkOrderAttachedId"]:checked').length <= 10) {
            $('#btnChkOrdersSelected').removeClass('hide');
        } else {
            //$('#btnChkOrdersSelected').addClass('hide');
            $(this).attr('checked', false);
            ShowModalError('modalMessage', ReadXmlTranslate("title_error_check_multi_abo"), ReadXmlTranslate("msg_error_check_multi_abo"), 'alert alert-danger', 3000);
        }
    });
}


//lorsque l'on clique sur le bouton checkAbo
function CheckAbo(idi, hash) {
    indxPb = 1;
    var identId = idi;

    $.ajax({
        type: "POST",
        //async: false,
        url: sAjaxSourceUrl + 'GetCommandsOfIdentite',
        contentType: 'application/json; charset=utf-8',

        data: JSON.stringify({
            identiteIdChild: idi,
            hash: hash
        }),
        dataType: "json",
        success: function (response) {

            if (response.d != null) {
                // var table = $('#orders_list_table').DataTable()
                $('#orders_list_table tr a[data-idt=' + idi + ']').removeClass('hide');
                $('#orders_list_table tr btn[data-idt_=' + idi + ']').hide();
                var row = oTableOrdersList.row($('#orders_list_table tr btn[data-idt_=' + idi + ']').parent().parent());
                //  jqtr = $('#childs tr btn[data-idt_=334267]').parent().parent();
                if (!row.child.isShown()) {
                    row.child(formatDetailCommande(response.d)).show();
                    var rowData = $.grep(oTableOrdersList.data(), function (a) { return a.Identite_id == idi });
                    //désactive le bouton Check abo
                    $('#orders_list_table tr btn[data-idt_=' + idi + ']').attr('disabled', 'disabled');
                    //pour chaque commande de l'identite on peut replier sur le chevronup
                    $('#orders_list_table .ibox#ibox-' + idi + ' .collapse-cmd').click(function () {
                        var ibox = $(this).closest('div.ibox');
                        var button = $(this).find('i');
                        var content = ibox.find('div.ibox-content');
                        content.slideToggle(200);
                        button.toggleClass('fa-chevron-up').toggleClass('fa-chevron-down');
                        ibox.toggleClass('').toggleClass('border-bottom');
                        setTimeout(function () {
                            ibox.resize();
                            ibox.find('[id^=map-]').resize();
                        }, 50);
                    });
                    $('#orders_list_table .ibox#ibox-' + idi + ' .collapse-cmd').click();
                }

                $('.montant').priceFormat({
                    prefix: '',
                    suffix: ' ' + deviseCode,
                    thousandsSeparator: ' '
                });
                prct = (indxPb / $('input[name="chkOrderAttachedId"]:checked').length) * 100;
                var t = setInterval(updateProgress(prct, indxPb, $('input[name="chkOrderAttachedId"]:checked').length), 1000);
                indxPb++;
            }
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            console.log(XMLHttpRequest.responseJSON.Message + "\r\n" + XMLHttpRequest.responseJSON.StackTrace);
        }
    });
}


/* Détail du check abo pour une commande */
function formatDetailCommande(d) {
    html = "";
    if (d.length > 0) {
        $.each(d, function (idx, comm) {
            $.each(comm.listAbonnement, function (idx2, abo) {
                cmdCharges.push(comm);
                aboCharges.push(abo);
                // `d` is the original data object for the row
                var etat = abo.listDossiersEntree[0].etat;
                var seat = abo.listDossiersEntree[0].ListSeats[0].siege;
                var rank = abo.listDossiersEntree[0].ListSeats[0].rang;
                var denom = abo.listDossiersEntree[0].ListSeats[0].denomination_name;
                var zone = abo.listDossiersEntree[0].ListSeats[0].zone_nom;
                var etage = abo.listDossiersEntree[0].ListSeats[0].etage_nom;
                var section = abo.listDossiersEntree[0].ListSeats[0].section_nom;

                html += '<div class="ibox float-e-margins" id="ibox-' + abo.identiteid + '"><div class="ibox-title">';
                html += '<label class="label label-success">' + ReadXmlTranslate('detail_command_commandid') + comm.commandeid + '</label> <label class="label label-success">' + ReadXmlTranslate('detail_command_denomination') + denom + '</label>';
                html += '<label class="label label-success">' + ReadXmlTranslate('detail_command_rank') + rank + ' </label> <label class="label label-success"> ' + ReadXmlTranslate('detail_command_seat') + seat + ' </label><label class="label label-success"> ' + ReadXmlTranslate('detail_command_aboid') + abo.aboId + '</label>';
                html += '<label class="label label-success">' + ReadXmlTranslate('detail_command_zone') + zone + '</label> <label class="label label-success"> ' + ReadXmlTranslate('detail_command_etage') + etage + '</label> <label class="label label-success">' + ReadXmlTranslate('detail_command_section') + section + '</label>';
                html += '<label class="label label-success">' + ReadXmlTranslate('detail_command_formule') + abo.formulaName + '</label><label class="label label-success"> ' + ReadXmlTranslate('detail_command_montant') + '<span class="montant">' + comm.amountTotal + ' </span> </label> <label class="label label-success"><span class="badge-danger badge">' + ReadXmlTranslate('detail_command_etat') + etat + '</span></label>';

                if (etat == "R") {
                    html += '<btn class="addBasket btn btn-primary" data-idt=' + comm.identiteid + '  data-formula=' + comm.listDossiersEntree[0].formuleAboId + ' data-cmd=' + comm.commandeid
                    + ' onclick=\'addBasket(' + comm.identiteid + ',' + comm.commandeid + ',' + abo.aboId + ',' + abo.formulaId + ',"' + comm.hashKey + '");\'><i class="fa fa-shopping-cart"></i> ' + ReadXmlTranslate("btn_add_basket") + '</btn>';
                }

                html += '<div class="ibox-tools"><a class="collapse-cmd"> <i class="fa fa-chevron-up"></i></a></div>  </div>  <div class="ibox-content">';
                html += '<table class="table table-striped" border="0" cellpadding="4" cellspacing="0" margin="0px" padding="0px">';

                html += '<tr><td colspan="9">';
                html += '<table class="table table-striped" border="0" cellpadding="3" cellspacing="0" margin="0px" padding="0px">';
                html += '<tr>' +
                '<th>' + ReadXmlTranslate('detail_command_event_name') + '</th>' +
                '<th>' + ReadXmlTranslate('detail_price_name') + '</th>' +
                '<th>' + ReadXmlTranslate('detail_command_montant') + '</th>' +
                '</tr>';

                $.each(comm.listDossiersEntree, function (idx2, doss) {
                    $.each(doss.ListSeats, function (idx3, seat) {
                        html += '<tr>' +
                        '<td>' + doss.manif_nom + '</td>' +
                        '<td>' + seat.type_tarif_nom + '</td>' +
                        '<td>' + seat.siege + '</td>' +
                        '<td>' + doss.manif_id + '</td>' +
                        '<td><span class="montant">' + seat.amountPaidCents + '</span></td>' +
                        '</tr>';
                    });
                });

                html += '</table></td></tr>';
                html += '</table>';
                html += ' </div></div>';
            });
});
} else {
    html = ReadXmlTranslate('lbl_aucune_commande');
}
return html;
}


function addBasket(identiteid, commandeid, aboid, formuleid, haskey) {
    var cmd = $.grep(cmdCharges, function (a) {
        return a.commandeid == commandeid;
    });

    var rowData = $.grep(oTableOrdersList.data(), function (a) { return a.Identite_id == identiteid })
    if (rowData.length > 0) {
        var haskey = rowData[0].hashKey

        thisAbos = $.grep(aboCharges, function (a) {
            return a.aboId == aboid;
        });
        if (thisAbos.length == 1) {
            thisAbo = thisAbos[0];
            thisAbo.inbasket = true;
            thisAbo.hash = haskey;
            console.log(thisAbo.aboId);
        }
    }
    RefreshBasket();
}


function delFromBasket(aboid) {
    //cmdsChargees.push(comm);
    thisAbos = $.grep(aboCharges, function (a) {
        return a.aboId == aboid;
    });
    if (thisAbos.length == 1) {
        thisAbo = thisAbos[0];
        thisAbo.inbasket = false;
        console.log(thisAbo.aboId);
    }
    RefreshBasket();
}

function RefreshBasket() {
    thisAbos = $.grep(aboCharges, function (a) {
        return a.inbasket == true;
    });

    if (thisAbos.length > 0)
    {
        var thisCmd = $.grep(cmdCharges, function (a) {
            return a.commandeid == thisAbos[0].commandeid;
        });
    }


    if (thisAbos.length <= nbmax) {
        fillBasket(thisAbos);

        if (thisAbos.length == 1) {
            //si il y a 0 dans le panier
            $('.title_orders').html(ReadXmlTranslate('title_one_order_selected'));
            $('#showSelectedOrders').removeAttr('disabled');
            $('#removeAllBasket').removeAttr('disabled');
        } else if (thisAbos.length > 0) {
            $('.title_orders').html(ReadXmlTranslate('title_orders_selected').replace('[nbOrders]', thisAbos.length));
            $('#showSelectedOrders').removeAttr('disabled');
            $('#removeAllBasket').removeAttr('disabled');

        } else {

            //si il y a 0 dans le panier
            $('.title_orders').html(ReadXmlTranslate('title_none_order_selected'));
            $('#showSelectedOrders').attr('disabled', 'disabled');
            $('#removeAllBasket').attr('disabled', 'disabled');
        }

        //on active tous les bouton ajouter au panier ici
        //$('#orders_list_table table btn').removeAttr('disabled');
        $('#orders_list_table tr .ibox-title btn').removeAttr('disabled');
        //on active tous les bouton ajouuter au panier du tableau de commande de l'utilisateur
        // $('#orders_user_list_wrapper btn').removeAttr('disabled');
        var mttotal = 0;



        $.each(thisAbos, function (index, item) {

         var thisCmd = $.grep(cmdCharges, function (a) {
            return a.commandeid == item.commandeid;
        });

         mttotal += thisCmd[0].amountTotal;
            //mttotal += item.amountTotal;
            //désactive les boutons qui sont dans le panier du tableau de commande reliés
            // $('#orders_list_table table btn[data-cmd="' + item.commandeid + '"]').attr('disabled', 'disabled');

            $('#orders_list_table tr .ibox-title btn.addBasket[data-cmd="' + item.commandeid + '"]').attr('disabled', 'disabled');
            //désactive les boutons qui sont dans le panier du tableau de commande de l'utilisateur
            // $('#orders_user_list_wrapper table btn[data-cmd="' + item.commandeid + '"]').attr('disabled', 'disabled');
        });



        var mttotalFloat = parseFloat(mttotal / 100).toFixed(2);
        var mntTotalglobal = parseFloat(mttotalFloat) + parseFloat(solde);

        if (mntTotalglobal > seuil) {
            //$('#montantBtbasketValid').text(ReadXmlTranslate("msg_montant_sup_seuil").replace("[mntTotal]", mttotalFloat).replace("[solde]", solde).replace("[mntTotalGlobal]", mntTotalglobal.toFixed(2))).addClass('label label-danger');

            var msgErrorHtml = "<span class='label label-danger'>" + ReadXmlTranslate("msg_montant_sup_seuil").replace("[mntTotal]", '<span class="montant">' + mttotalFloat + '</span>').replace("[solde]", '<span class="montant">' + solde + '</span>').replace("[mntTotalGlobal]", '<span class="montant">' + mntTotalglobal.toFixed(2) + '</span>') + "</span>";

            $('#montantBtbasketValid').html(msgErrorHtml);
            //$('#montantBtbasketValid').html(ReadXmlTranslate("msg_montant_sup_seuil").replace("[mntTotal]", '<span class="montant">' + mttotalFloat + '</span>').replace("[solde]", '<span class="montant">' + solde + '</span>').replace("[mntTotalGlobal]", '<span class="montant">' + mntTotalglobal.toFixed(2) + '</span>'));

        }
        else {
            $('#montantBtbasketValid').html(ReadXmlTranslate("montant_panier").replace("[mntPanier]", '<span class="montant">' + mttotalFloat + '</span>'));
        }





        $('.montant').priceFormat({
            prefix: '',
            suffix: ' ' + deviseCode,
            thousandsSeparator: ' '
        });

        /* if (thisAbos.length > 0) {
             $('#basket').show();
             $('#basketValid').show();
         }
         else {
             $('#basket').hide();
             $('#basketValid').hide();
         }*/
     } else {
        ShowModalError('modalMessage', ReadXmlTranslate("title_error_check_multi_abo"), ReadXmlTranslate("msg_error_check_multi_abo").replace('[nbMax]', nbmax), 'alert alert-danger', 3000);
    }
}



//Rempli le tableau panier
function fillBasket(data) {
	totalAmount=0;
    var htmlTable = '';

    $.each(data, function (indx, item) {

        var thisCmd = $.grep(cmdCharges, function (a) {
            return a.commandeid == item.commandeid;
        });
        totalAmount = totalAmount + thisCmd[0].amountTotal;
        
        var rowData = $.grep(oTableOrdersList.data(), function (a) { return a.Identite_id == item.identiteid })

        htmlTable += '<div class="ibox-content"><div class="table-responsive"><table class="table shoping-cart-table"><tbody><tr> ';
        htmlTable += '<td data-identid="' + rowData[0].Identite_id + '"><h3>' + rowData[0].Identite_id + ' </h3></td>';
        htmlTable += '<td class="name_firstname"><h3 class="text-navy"> ' + rowData[0].FirstName + ' ' + rowData[0].SurName + '</h3></td>';

        //<select id="784617" aboid="784617" name="selectModeObtention" onchange="RefreshMontantBasket()"><option>Aucun mode d'obtention</option><option selected="selected" value="1750002" data-mqtid="0" data-hashkey="CB8181280429ED1C7E5655C9722D4C4B085A29A1" data-mt="300">Réactivation abonnement (300)</option></select>
        htmlTable += '<td><h4 class="totalAmount montant">' + thisCmd[0].amountTotal + '</h4></td> ';

        htmlTable += '<td class="select_mo"> ';
        htmlTable += '<select class="form-control" id=' + item.aboId + '  aboid=' + item.aboId + ' name="selectModeObtention"><option>' + ReadXmlTranslate('select_aucun_mode_obtention') + '</option>';
        if (item.listProduitsMODisponibles.length == 1) {
            var mo = item.listProduitsMODisponibles[0];
            htmlTable += '<option selected="selected" value="' + mo.produit_id + '" data-mqtid=' + mo.maquette_id + ' data-hashkey="' + mo.hashKey + '" data-mt=' + mo.totalAmount + '>' + mo.productName + ' (' + mo.totalAmount + ')</option>';

        } else {

            $.each(item.listProduitsMODisponibles, function (indx2, itemMo) {

                htmlTable += '<option  value="' + itemMo.produit_id + '" data-mqtid=' + itemMo.maquette_id + ' data-hashkey="' + itemMo.hashKey + '" data-mt=' + itemMo.totalAmount + '>' + itemMo.productName + ' (' + itemMo.totalAmount + ')</option>';
            });
        }
        htmlTable += '</select></td> ';

        htmlTable += '<td><h4 class="moAmount montant">' + totalAmount + '</h4></td>';
        htmlTable += '<td> <a href="" data-aboid="' + item.aboId + '" class="delete_basket pull-right text-muted btn btn-primary"><i class="fa fa-trash"></i> ' + ReadXmlTranslate("btn_delete_basket") + '</a></td></tr></tbody></table></div></div>';

    });

    $('#basket').html(htmlTable);

    CheckModeObtention();

    $('#basket select[name="selectModeObtention"]').on('change', function () {

        var valueSelected = $(this).val();
        $.each($('#basket select[name="selectModeObtention"]'), function (indx, item) {
            $(item).val(valueSelected);
        });

        CheckModeObtention();
    });

    $('.delete_basket').on('click', function (e) {
        e.preventDefault();

        var aboid = $(this).data('aboid');

        delFromBasket(aboid);

    });

    $('.montant').priceFormat({
        prefix: '',
        suffix: ' '+deviseCode,
        thousandsSeparator: ' '
    });


}


function CheckModeObtention() {

    //check si tous les mode d'obtention sont valid
    var selected = [];
    $.each($('#basket select[name="selectModeObtention"] option:selected'), function (indx, itemMo) {

        console.log($(this).val());

        if ($(this).val() > 0) {
            selected.push(true);

            //met à jour le montant du mode d'obtention
            var mntMo = parseFloat($(this).data('mt') / 100).toFixed(2);
            var mntFinal = parseFloat((totalAmount/100) + parseFloat(mntMo)).toFixed(2);
            $(this).parents('tr').find('.moAmount').html(mntFinal);

        } else {
            selected.push(false);
            $(this).parents('tr').find('.moAmount').html(0);
        }
    });

    if (selected.length == 0) {
        $('#basketValid').prop('disabled', true);
    } else {
        //si dans le tableau il y a un false alors on desactive
        if ($.inArray(false, selected) > -1) {
            $('#basketValid').prop('disabled', true);
        } else {
            $('#basketValid').prop('disabled', false);
        }
    }

    $('.montant').priceFormat({
        prefix: '',
        suffix: ' ' + deviseCode,
        thousandsSeparator: ' '
    });

}

function sendToBasket() {
    thisCmds = $.grep(aboCharges, function (a) {
        return a.inbasket == true;
    });

    var arr = new Array();
    $.each(thisCmds, function (index, item) {

        var cmd = $.grep(cmdCharges, function (a) {
            return a.commandeid == item.commandeid;
        });

        var arrDossierEntree = new Array();

        $.each(item.listDossiersEntree, function (indx, itemobjDossier) {
            var date = new Date(parseInt(itemobjDossier["sessionDate"].substr(6)));
            itemobjDossier.sessionDate = date;
            $.each(itemobjDossier.ListSeats, function (indx, itemobjSeat) {
                var date = new Date(parseInt(itemobjSeat["seance_date_deb"].substr(6)));
                itemobjSeat.seance_date_deb = date;
            });
            arrDossierEntree.push(itemobjDossier)
        });
        var dataParam = {
            commandeid: item.commandeid,
            identiteid: item.identiteid,
            aboid: item.aboId,
            formuleid: item.formuleAboId,
            mt: cmd[0].amountTotal,
            hashKey: item.hashKey,
            moid: $('select#' + item.aboId + ' option:selected').val(),
            mqid: $('select#' + item.aboId + ' option:selected').data('mqtid'),
            hkmo: $('select#' + item.aboId + ' option:selected').data('hashkey'),
            listDossiersEntree: item.listDossiersEntree
        };
        arr.push(dataParam)
    });

    sessionStorage.setItem("arrBasket", JSON.stringify(arr));

    $.ajax({
        type: "POST",
        url: sAjaxSourceUrl + 'AddBasket',
        contentType: 'application/json; charset=utf-8',
        data: JSON.stringify({
            listAbos: arr
        }),
        dataType: "json",
        success: function (response) {

            $("#modalForWaiting").modal({
                backdrop: 'static'
            });

            var arrBasket = $.grep(aboCharges, function (a) {
                return a.inbasket == true;
            });

            sessionStorage.setItem("aboCharges", JSON.stringify(aboCharges));
            sessionStorage.setItem("cmdCharges", JSON.stringify(cmdCharges));

            window.location = response.d;
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            console.log(XMLHttpRequest.responseJSON.Message + "\r\n" + XMLHttpRequest.responseJSON.StackTrace);
        },
        complete: function () {
            //Simule le changement du nombre de résultat car par défaut on charge tout
            $.fancybox.close();
        }
    });
}


//retourne une identite avec le seuil et le solde de rempli
function GetDroitsFacture() {
    $('#montantSeuil').html(ReadXmlTranslate("lbl_montant_seuil").replace('[montantSeuil]', '<span class="montant">0</span>'));
    $('#montantSolde').html(ReadXmlTranslate("lbl_montant_solde").replace('[montantSolde]', '<span class="montant">0</span>'));
     $('.montant').priceFormat({
                    prefix: '',
                    suffix: ' ' + deviseCode,
                    thousandsSeparator: ' '
                });
    $.ajax({
        type: "POST",

        url: 'Commons.asmx/GetDroitsFacture',
        contentType: 'application/json; charset=utf-8',
        dataType: "json",
        success: function (response) {
            if (response.d != null) {
                solde = parseFloat(response.d.Solde / 100).toFixed(2);
                seuil = parseFloat(response.d.Seuil / 100).toFixed(2);
                if ((seuil - solde) < 0) {
                    //ne peut plus payer en facture - rouge
                    $('#montantSeuil').html(ReadXmlTranslate("lbl_montant_seuil").replace('[montantSeuil]', '<span class="montant label label-danger">' + seuil + '</span>'));
                    $('#montantSolde').html(ReadXmlTranslate("lbl_montant_solde").replace('[montantSolde]', '<span class="montant label label-danger">' + solde + '</span>'));
                } else if ((0.9 * seuil - solde) < 0) {
                    //on se rapproche du solde 10% - orange
                    $('#montantSeuil').html(ReadXmlTranslate("lbl_montant_seuil").replace('[montantSeuil]', '<span class="montant label label-warning">' + seuil + '</span>'));
                    $('#montantSolde').html(ReadXmlTranslate("lbl_montant_solde").replace('[montantSolde]', '<span class="montant label label-warning">' + solde + '</span>'));
                } else {
                    // vert normal
                    $('#montantSeuil').html(ReadXmlTranslate("lbl_montant_seuil").replace('[montantSeuil]', '<span class="montant">' + seuil + '</span>'));
                    $('#montantSolde').html(ReadXmlTranslate("lbl_montant_solde").replace('[montantSolde]', '<span class="montant">' + solde + '</span>'));
                }

                $('.montant').priceFormat({
                    prefix: '',
                    suffix: ' ' + deviseCode,
                    thousandsSeparator: ' '
                });
            }
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            //ShowModalError("modalMessage", "Erreur pendant le chargement des logos", XMLHttpRequest.responseJSON.Message + "<br />" + XMLHttpRequest.responseJSON.StackTrace, "alert alert-danger alert-dismissable", 5000);
            console.log(XMLHttpRequest.responseJSON.Message + "\r\n" + XMLHttpRequest.responseJSON.StackTrace);
        }
    });
}

function GetTemplateFormAddFan() {

    var baseUrlEdition = GetUrlTemplate("addChildFan");
    if (fileExists(baseUrlEdition)) {
        url = baseUrlEdition;
        var text = $.ajax({
            url: url,
            dataType: 'text',
            async: false
        });
        $('#modalAddChildFan .modal-body').html(text.responseText);
        $('#btn_add_relation').on('click', function (e) {
            e.preventDefault();
            $("#modalAddChildFan").modal({
                backdrop: 'static'
            });
        });
        AddChildFan();
    }
}

function AddChildFan() {

    $('#validerFan').on('click', function (e) {
        e.preventDefault();

        //cache le formulaire
        var form = $('#addChildForm').data('formValidation');
        if (form.isValid()) {
            var nbInput = $('#addChildForm input[type="text"], input[type="number"], input[type="date"]').length;
            //les valeurs sont séparés par un |
            var valuesOfForm = "";
            $.each($('#addChildForm input[type="text"],  input[type="number"], input[type="date"]'), function (indx, item) {
                if (indx < nbInput) {
                    if (valuesOfForm == "") {
                        valuesOfForm = item.value;
                    } else {
                        valuesOfForm = valuesOfForm + "|" + item.value;
                    }
                    console.log(valuesOfForm);
                }
            });
            console.log(valuesOfForm);

            $.ajax({
                type: "POST",
                url: 'commons.asmx/AddChild',
                contentType: 'application/json; charset=utf-8',
                data: JSON.stringify({ checkValue: valuesOfForm }),
                dataType: "json",
                success: function (response) {
                    $('#loading').html("");

                    if (response.d.Identite_id == 0) {
                        var mess = {
                            message: response.d.SurName.split("|")[1],
                            code: response.d.SurName.split("|")[0]
                        }
                        gestionMessageErreur(mess);
                    }
                    else {
                        ShowModalError('modalMessage', ReadXmlTranslate("title_msg_success_add_relation"), ReadXmlTranslate("msg_success_add_relation"), 'alert alert-success', 5000);
                        GetPersonsAttachedToManager();
                    }
                },
                error: function (XMLHttpRequest, textStatus, errorThrown) {
                    console.log(XMLHttpRequest.responseJSON.Message + "\r\n" + XMLHttpRequest.responseJSON.StackTrace);
                },
                complete: function () {
                    //Simule le changement du nombre de résultat car par défaut on charge tout
                    $('#modalAddChildMessage').modal('hide');
                }
            });
        } else {
            $('#addChildForm').data('formValidation').validate()
        }
    });
}



function GetInformationsIdentite(identiteid) {

    var identiteToUpdated = {};

    var sData = JSON.stringify({ identiteIdChild: identiteid });

    $.ajax({
        type: "POST",
        url: 'commons.asmx/GetInformationsFanChild',
        contentType: 'application/json; charset=utf-8',
        data: sData,
        async: false,
        dataType: "json",
        success: function (response) {
            identiteToUpdated = response.d;
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            console.log(XMLHttpRequest.responseJSON.Message + "\r\n" + XMLHttpRequest.responseJSON.StackTrace);
        },
        complete: function () {
        }
    });
    return identiteToUpdated;
}


/************************ UPDATE FAN ***********************/
function GetTemplateFormUpdateFan(identiteid) {
    var baseUrlEdition = GetUrlTemplate("updateChildFan");
    if (fileExists(baseUrlEdition)) {

        url = baseUrlEdition;

        var text = $.ajax({
            url: url,
            dataType: 'jsonp', // jsonp
            async: false //IGNORED!!
        });

        $('#modalUpdateChildFan .modal-body').html(text.responseText);


        $("#modalUpdateChildFan").modal({
            backdrop: 'static'
        });
            // $('#modalAddChildFan').modal('show');
            UpdateChildFan(identiteid);
        }
    }

    function UpdateChildFan(identiteid) {
        var objIdentite = GetInformationsIdentite(identiteid);
        $.datepicker.setDefaults($.datepicker.regional["fr"]);
        var date = $.datepicker.formatDate('d/mm/yy', new Date(parseInt(objIdentite.DateOfBirthday.substr(6))));

        objIdentite.DateOfBirthday = date;
        $.each(objIdentite, function (name, value) {


            if (name == "DateOfBirthday") {
                $('input[name="' + name + '"]').val(date);
            } else {
                if ($('input[name="' + name + '"]').length > 0 || $('label[name="' + name + '"]').length) {
                    $('input[name="' + name + '"]').val(value);
                    $('label[name="' + name + '"]').text(value);
                }
            }
        });

        $('#updateValiderFan').on('click', function (e) {
            e.preventDefault();
            var nbInput = $('#updateChildForm input[type="text"], input[type="number"], input[type="date"]').length;
            //les valeurs sont séparés par un |
            var valuesOfForm = "";
            $.each($('#updateChildForm input[type="text"],  input[type="number"], input[type="date"]'), function (indx, item) {

                if (indx < nbInput) {
                    if (valuesOfForm == "") {
                        valuesOfForm = item.value;
                    } else {
                        valuesOfForm = valuesOfForm + "|" +item.name+'%'+ item.value;
                    }
                    console.log(valuesOfForm);
                }
            });
            console.log(valuesOfForm);

            $.ajax({
                type: "POST",
                url: 'commons.asmx/UpdateChild',
                contentType: 'application/json; charset=utf-8',
                data: JSON.stringify({ checkValue: valuesOfForm }),
                dataType: "json",
                success: function (response) {
                    $('#loading').html("");

                    if (response.d.contains('|'))
                    {
                        ShowModalError('modalMessage', ReadXmlTranslate(response.d.split('|')[1]), ReadXmlTranslate(response.d.split('|')[2]), 'alert alert-' + response.d.split('|')[0], 5000);
                    }else if (response.d == "True") {
                        ShowModalError('modalMessage', ReadXmlTranslate("title_msg_success_update_relation"), ReadXmlTranslate("msg_success_update_relation"), 'alert alert-success', 5000);
                        GetPersonsAttachedToManager();
                    }
                    $("#modalUpdateChildFan").modal('hide');

                },
                error: function (XMLHttpRequest, textStatus, errorThrown) {
                    console.log(XMLHttpRequest.responseJSON.Message + "\r\n" + XMLHttpRequest.responseJSON.StackTrace);
                },
                complete: function () {                   
                    $('#modalAddChildMessage').modal('hide');

                }
            });
        });
    }

//Liste des personnes attachées au manager (utilisateur connecté)
function GetPersonsAttachedToManager() {

    $.ajax({
        type: "POST",

        url: 'commons.asmx/GetMyRelations',
        contentType: 'application/json; charset=utf-8',
        dataType: "json",
        success: function (response) {
            $('#loading').html("");

            $('#logos_events tbody').empty();
            //oTable.fnDestroy();
            //oTable = null;
            //var info = jQuery.parseJSON(response.d);
            fillPersonsAttachedManager(response.d);

            //ShowEventsSessionsTable(response.d);
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            //ShowModalError("modalMessage", "Erreur pendant le chargement des logos", XMLHttpRequest.responseJSON.Message + "<br />" + XMLHttpRequest.responseJSON.StackTrace, "alert alert-danger alert-dismissable", 5000);
            console.log(XMLHttpRequest.responseJSON.Message + "\r\n" + XMLHttpRequest.responseJSON.StackTrace);
        },
        complete: function () {

            $('#childs_list_table_length option:eq(0)').prop("selected", true);
            $('#childs_list_table_length select').change();

        }
    });
}



function fillPersonsAttachedManager(data) {
    oTable = $('#childs_list_table').dataTable({
        "bDestroy": true,
        "bProcessing": false,
        "responsive": true,
        //"bSort": true,
        //"bFilter": true,
        "bAutoWidth": true,
        "iDisplayLength": 10,
        "aLengthMenu": [[10, 25, 50, 100, -1], [10, 25, 50, 100, ReadXmlTranslate('lbl_choose_all_datatable')]],
        "bDeferRender": true,
        "aaData": data,
        "oLanguage": GetLanguageDataTable(),
        //"dom": '<"top"B><"clear"><"top"lf>rt<"bottom"ip><"clear">',
        "dom":"<'top'B>"+
                "<'row'<'col-sm-12 col-md-6'l><'col-sm-12 col-md-6'f>>" +
                "<'row'<'col-sm-12'tr>>" +
                "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
        // "dom": 'lBfrtip',
        "buttons": [
        {
         extend: 'excelHtml5',
         text: ReadXmlTranslate('lbl_export_excel'),
         exportOptions: {
             columns: [0, 1, 2]
         },
         customize: function (xlsx) {
             var sheet = xlsx.xl.worksheets['sheet1.xml'];
             var lastCol = sheet.getElementsByTagName('col').length - 1;
             var colRange = createCellPos(lastCol) + '1';
                   //Has to be done this way to avoid creation of unwanted namespace atributes.
                   var afSerializer = new XMLSerializer();
                   var xmlString = afSerializer.serializeToString(sheet);
                   var parser = new DOMParser();
                   var xmlDoc = parser.parseFromString(xmlString, 'text/xml');
                   var xlsxFilter = xmlDoc.createElementNS('http://schemas.openxmlformats.org/spreadsheetml/2006/main', 'autoFilter');
                   var filterAttr = xmlDoc.createAttribute('ref');
                   filterAttr.value = 'A1:' + colRange;
                   xlsxFilter.setAttributeNode(filterAttr);
                   sheet.getElementsByTagName('worksheet')[0].appendChild(xlsxFilter);
               }
           },
           {
             extend: 'pdfHtml5',
             text: ReadXmlTranslate('lbl_export_pdf'),
             exportOptions: {
                 columns: [0, 1, 2]
             }

         },
         {
            extend: 'print',
            text: ReadXmlTranslate('lbl_export_print'),
            exportOptions: {
                columns: [0, 1, 2]
            }
        },
        {
           extend: 'copyHtml5',
           text: ReadXmlTranslate('lbl_export_copy'),
           exportOptions: {
               columns: [0, 1, 2]
           }
       }

       ],

       "aoColumns": [
       {
        "mDataProp": "Identite_id"
    },
    { "mDataProp": "SurName" },
    { "mDataProp": "FirstName" },
    {
        "mDataProp": "Identite_id",
        "mRender": function (data, type, full) {
                    //var html = '<btn class="deleterelation btn btn-warning" data-idt_=' + full.Identite_id + ' id="idt_' + full.Identite_id + '" onclick=\'DeleteChild(' + full.Identite_id + ');\'>'+ReadXmlTranslate("delete_relation")+'</btn>';
                    var html = '<a href="#" class="updaterelation btn btn-success" data-idt_=' + full.Identite_id + ' id="idt_' + full.Identite_id + '">' + ReadXmlTranslate("update_relation") + '</a>';
                    html += '<a href="#" class="deleterelation btn btn-warning" data-idt_=' + full.Identite_id + ' id="idt_' + full.Identite_id + '">' + ReadXmlTranslate("delete_relation") + '</a>';
                    return html;
                }
            }
            ]
        });
    $('.updaterelation').on('click', function () {


        var identiteId = $(this).data('idt_');


        GetTemplateFormUpdateFan(identiteId);

        //$(this).closest('tr').find('a.updaterelation').addClass('selected_link');
        //AddEffectToRowSelected('childs_list_table', $(this).parent().parent());


    });

    $('.deleterelation').on('click', function () {
        var identiteId = $(this).data('idt_');

        $(this).closest('tr').find('a.deleterelation').addClass('selected_link');
        AddEffectToRowSelected('childs_list_table', $(this).parent().parent());


        $.confirm({
            text: ReadXmlTranslate("confirm_msg_delete_relation"),
            title: ReadXmlTranslate("confirm_title_delete_relation"),
            confirm: function () {

                //$('.selected_link').data('formuleid');
                var sData = JSON.stringify({ identiteIdChild: identiteId });

                $.ajax({
                    type: "POST",
                    url: 'commons.asmx/DeleteChild',
                    data: sData,
                    contentType: 'application/json; charset=utf-8',
                    dataType: "json",
                    success: function (response) {


                        if (response.d.Identite_id == 0) {
                            var mess = {
                                message: response.d.Nom.split("|")[1],
                                code: response.d.Nom.split("|")[0]
                            }

                            gestionMessageErreur(mess);
                        }
                        else {
                            ShowModalError('modalMessage', ReadXmlTranslate('title_msg_success_delete_relation'), ReadXmlTranslate('msg_success_delete_relation'), 'alert alert-success', 3000);
                            GetPersonsAttachedToManager();
                        }
                        //oTable.fnDestroy();
                        //oTable = null;
                        //var info = jQuery.parseJSON(response.d);
                        fillPersonsAttachedManager(response.d);

                    },
                    complete: function () {
                        $('#childs_list_table_length option:eq(0)').prop("selected", true);
                        $('#childs_list_table_length select').change();
                    }
                });
            },
            cancel: function (button) {
                // do something
            },
            confirmButton: ReadXmlTranslate('btn_confirm_valider'),
            cancelButton: ReadXmlTranslate('btn_confirm_annuler'),
            post: true
        });

    });


}


function GetIdentiteAccompte() {
    $.ajax({
        type: "POST",

        url: 'commons.asmx/GetIdentiteAccompte',
        contentType: 'application/json; charset=utf-8',
        dataType: "json",
        success: function (response) {
            //$('#loading').html("");

            if (response.d != null) {
                var amountdeposit = parseFloat(response.d.Acompte / 100);
                $('#amountDeposit').html(ReadXmlTranslate("lbl_amount_of_deposit").replace('[amount_of_deposit]', '<span class="montant">'+amountdeposit+'</span>'));
                $('.montant').priceFormat({
                    prefix: '',
                    suffix: ' ' + deviseCode,
                    thousandsSeparator: ' '
                });
            } else {
                $('#amountDeposit').hide();
            }


        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            //ShowModalError("modalMessage", "Erreur pendant le chargement des logos", XMLHttpRequest.responseJSON.Message + "<br />" + XMLHttpRequest.responseJSON.StackTrace, "alert alert-danger alert-dismissable", 5000);
            console.log(XMLHttpRequest.responseJSON.Message + "\r\n" + XMLHttpRequest.responseJSON.StackTrace);
        }

    });
}


function GetAllIdentiteCommandesFans() {

    $('#orders_export_wrapper').removeClass('hide');

    var chkValidPlaces = false;
    if ($('input[name="chkValidPlaces"]:checked').length > 0)
        chkValidPlaces = true;

    var sData = JSON.stringify({ listBastetState: GetBasketStatesSelected(), isValidPlace: chkValidPlaces });


    try {
        $.ajax({
            type: "POST",
            url: 'Commons.asmx/GetAllIdentiteCommandesFans',
            data: sData,
            contentType: 'application/json; charset=utf-8',
            dataType: "json",
            success: function (response) {
                $('#loading').html("");

                if (response.d.length > 0) {
                    fillDataTableAllIdentiteCommandesFans(response.d);


                }

            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                //ShowModalError("modalMessage", "Erreur pendant le chargement des logos", XMLHttpRequest.responseJSON.Message + "<br />" + XMLHttpRequest.responseJSON.StackTrace, "alert alert-danger alert-dismissable", 5000);
                console.log(XMLHttpRequest.responseJSON.Message + "\r\n" + XMLHttpRequest.responseJSON.StackTrace);
            },
            complete: function () {

                $('#orders_export_list_table_length option:eq(0)').prop("selected", true);
                $('#orders_export_list_table_length select').change();

            }
        });
    } catch (e) {
        console.log(e.Message);
    }
}


function fillDataTableAllIdentiteCommandesFans(data) {


    var hideFromExport = [ 1, 2];


    var table = $('#orders_export_list_table').dataTable({
        "bDestroy": true,
        "bProcessing": true,
         "responsive": true,
        "bSort": true,
        "bFilter": true,
        "bAutoWidth": false,
        "iDisplayLength": -1,
        "bDeferRender": true,
        "aaData": data,
        "oLanguage": GetLanguageDataTable(),
        ///"dom": '<"top"B><"clear"><"top"lf>rt<"bottom"ip><"clear">',
        "dom":"<'top'B>"+
                "<'row'<'col-sm-12 col-md-6'l><'col-sm-12 col-md-6'f>>" +
                "<'row'<'col-sm-12'tr>>" +
                "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
        // "dom": 'Bfrtip',
        // "dom": 'lBfrtip',
        "buttons": [
        {
         text: ReadXmlTranslate("btn_choose_column_visible"),
         extend: 'colvis',
         columns: ':not(:first-child)'
     },
     {
         extend: 'excelHtml5',
         text: ReadXmlTranslate('lbl_export_excel'),
         customize: function (xlsx) {
             var sheet = xlsx.xl.worksheets['sheet1.xml'];
             var lastCol = sheet.getElementsByTagName('col').length - 1;
             var colRange = createCellPos(lastCol) + '1';
                   //Has to be done this way to avoid creation of unwanted namespace atributes.
                   var afSerializer = new XMLSerializer();
                   var xmlString = afSerializer.serializeToString(sheet);
                   var parser = new DOMParser();
                   var xmlDoc = parser.parseFromString(xmlString, 'text/xml');
                   var xlsxFilter = xmlDoc.createElementNS('http://schemas.openxmlformats.org/spreadsheetml/2006/main', 'autoFilter');
                   var filterAttr = xmlDoc.createAttribute('ref');
                   filterAttr.value = 'A1:' + colRange;
                   xlsxFilter.setAttributeNode(filterAttr);
                   sheet.getElementsByTagName('worksheet')[0].appendChild(xlsxFilter);
               }
           }

           ],

           "aoColumns": [
           { "mDataProp": "Identite.Identite_id" },
           { "mDataProp": "Identite.SurName" },
           { "mDataProp": "Identite.FirstName" },
           { "mDataProp": "Identite.PostalCode" },
           { "mDataProp": "Identite.PhoneNumber1" },
           { "mDataProp": "Identite.PhoneNumber2" },
           { "mDataProp": "Identite.PhoneNumber3" },
           { "mDataProp": "Identite.PhoneNumber4" },
           { "mDataProp": "Identite.PhoneNumber5" },
           { "mDataProp": "Identite.PhoneNumber6" },
           { "mDataProp": "Identite.PhoneNumber7" },
           { "mDataProp": "Identite.Address1" },
           { "mDataProp": "Identite.Address2" },
           { "mDataProp": "Identite.Address3" },
           { "mDataProp": "Identite.Address4" },
           { "mDataProp": "Identite.City" },
           { "mDataProp": "Identite.Country" },
           { "mDataProp": "Dossier.identite_id" },
           { "mDataProp": "Dossier.aboId" },
           { "mDataProp": "Dossier.commande_id" },
           { "mDataProp": "Dossier.etat" },
           { "mDataProp": "Dossier.formuleAboName" },
           { "mDataProp": "Dossier.montantpayer" },
           { "mDataProp": "Seat.Seat" },
           { "mDataProp": "Seat.Rank" },
           { "mDataProp": "Seat.Categ_name" },
           { "mDataProp": "Seat.Type_tarif_name" },
           { "mDataProp": "Seat.Zone_name" },
           { "mDataProp": "Seat.Section_name" },
           { "mDataProp": "Recette.numbillet" },
           { "mDataProp": "Recette.codeBarre" }
           ]
       });
}

function GetBasketStatesSelected() {
    var strArrBasketState = "";

    var nbSelected = $('#selectBasketState option:selected').length;

    $('#selectBasketState option:selected').each(function (indx, item) {

        if (indx+1 < nbSelected)
        {
           strArrBasketState += $(this).val() + ",";

       } else if (indx+1 == nbSelected)
       {
        strArrBasketState +=  $(this).val();

    }

});

    return strArrBasketState;
}
