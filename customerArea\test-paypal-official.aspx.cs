using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using customerArea.classes;
using utilitaires2010;
using customerArea.App_Code;

namespace customerArea
{
    public partial class TestPayPalOfficial : System.Web.UI.Page
    {
        public string SettingsJson { get; set; } = "{}";

        protected void Page_Load(object sender, EventArgs e)
        {
            try
            {
                // Récupérer l'ID de structure depuis l'URL ou utiliser une valeur par défaut
                string structureId = Request.QueryString["idstructure"] ?? "991";
                
                // Simuler la session pour le test
                if (Session["idstructure"] == null)
                {
                    Session["idstructure"] = int.Parse(structureId);
                }

                // Récupérer les settings comme dans votre application principale
                string plateformCode = "Customer";
                string lang = "fr";
                int idpa = 0;
                int resulEvent = 0;

                List<CustomJsonSettings> listJsonSettingsToMerge = new List<CustomJsonSettings>
                {
                    new CustomJsonSettings() { KeyOfJsonPath = "physicalPathOfSettingsJSON", IsDefault = true },
                    new CustomJsonSettings() { KeyOfJsonPath = "physicalPathOfSettingsJSON", IsDefault = false },
                    new CustomJsonSettings() { KeyOfJsonPath = "physicalPathOfSettingsPlateformJSON", IsDefault = false }
                };

                dynamic globalPlateform = utilitaires2010.Initialisations.GetMergeSettings(
                    listJsonSettingsToMerge, 
                    int.Parse(structureId), 
                    resulEvent, 
                    idpa, 
                    plateformCode, 
                    lang
                );

                // Créer un objet settings simplifié pour le test
                var testSettings = new
                {
                    login = new
                    {
                        payPal = new
                        {
                            appid = GetPayPalAppId(globalPlateform),
                            returnurl = GetPayPalReturnUrl(globalPlateform)
                        }
                    }
                };

                SettingsJson = Newtonsoft.Json.JsonConvert.SerializeObject(testSettings);
            }
            catch (Exception ex)
            {
                // En cas d'erreur, créer des settings de test par défaut
                var defaultSettings = new
                {
                    login = new
                    {
                        payPal = new
                        {
                            appid = "AZKOsIycBytFxeUfRdiRDtI6rmqT8m96xaw4KBymF9hk44HjSw_ubrQwdI1QZz_-RijZvaIQKLAq93Jw",
                            returnurl = "https://test.themisweb.fr/customerV3/_loginPayPal.aspx?idstructure=[structureid]"
                        }
                    },
                    error = ex.Message
                };

                SettingsJson = Newtonsoft.Json.JsonConvert.SerializeObject(defaultSettings);
            }
        }

        private string GetPayPalAppId(dynamic globalPlateform)
        {
            try
            {
                // Récupération de l'appid PayPal (utilisé comme username)
                dynamic paypalAppIdDynamic = globalPlateform.login.payPal.appid.Value;
                if (globalPlateform.customer != null && 
                    globalPlateform.customer.login != null && 
                    globalPlateform.customer.login.payPal != null && 
                    globalPlateform.customer.login.payPal.appid != null)
                {
                    paypalAppIdDynamic = globalPlateform.customer.login.payPal.appid.Value;
                }
                return paypalAppIdDynamic?.ToString() ?? "";
            }
            catch
            {
                return "AZKOsIycBytFxeUfRdiRDtI6rmqT8m96xaw4KBymF9hk44HjSw_ubrQwdI1QZz_-RijZvaIQKLAq93Jw"; // Valeur par défaut de test
            }
        }

        private string GetPayPalReturnUrl(dynamic globalPlateform)
        {
            try
            {
                // Récupération de l'URL de retour PayPal
                dynamic paypalReturnUrlDynamic = globalPlateform.login.payPal.returnurl.Value;
                if (globalPlateform.customer != null && 
                    globalPlateform.customer.login != null && 
                    globalPlateform.customer.login.payPal != null && 
                    globalPlateform.customer.login.payPal.returnurl != null)
                {
                    paypalReturnUrlDynamic = globalPlateform.customer.login.payPal.returnurl.Value;
                }
                return paypalReturnUrlDynamic?.ToString() ?? "";
            }
            catch
            {
                return "https://test.themisweb.fr/customerV3/_loginPayPal.aspx?idstructure=[structureid]"; // Valeur par défaut de test
            }
        }
    }
}
