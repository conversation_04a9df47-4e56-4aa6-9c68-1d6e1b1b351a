/****
* mainColor : 088eb2  8,142,178 <---- couleur principale (override par couleur client)
* secondaryColor : 43474d  <---- couleur secondaire (par défaut gris rodrigue)
* normalGray : 8f949a <---- normal grey
* lightGray : f1f3f5 <---- light grey
****/

/*couleur principale du client, bouton d'action principal*/
@primaryColor: #088eb2;

/*couleur secondaire du client, bouton d'action secondaire*/
@secondaryColor: #43474d;

/*couleur terciaire du client, bouton d'action terciaire*/
@tertiaryColor: #8f949a; 

/*couleur de fond de la bannière*/
@bannerColor: #fff;

/*couleur du texte générique*/
@txtColor: #43474d;

/*couleur de fond léger*/
@lightGrey: #f1f3f5;

/****************** FIX ***********************/
.col, .col-1, .col-10, .col-11, .col-12, .col-2, .col-3, .col-4, .col-5, .col-6, .col-7, .col-8, .col-9, .col-auto, .col-lg, .col-lg-1, .col-lg-10, .col-lg-11, .col-lg-12, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9, .col-lg-auto, .col-md, .col-md-1, .col-md-10, .col-md-11, .col-md-12, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-md-auto, .col-sm, .col-sm-1, .col-sm-10, .col-sm-11, .col-sm-12, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9, .col-sm-auto, .col-xl, .col-xl-1, .col-xl-10, .col-xl-11, .col-xl-12, .col-xl-2, .col-xl-3, .col-xl-4, .col-xl-5, .col-xl-6, .col-xl-7, .col-xl-8, .col-xl-9, .col-xl-auto {
  min-height: 0px;
}

/****************** COMON ***********************/
:focus, .custom-file-input:focus~.custom-file-control {
  outline: 0 !important;
  -webkit-box-shadow: none !important;
  box-shadow: none !important;
}

:invalid {
  -o-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}

body, html {
  font-family: 'Open Sans', sans-serif;
  font-size: 13px;
  color: @txtColor;
}

.hideWhenWidget, .showWhenWidget, .buttonBack {
  display: none;
}

a, a:hover, .btn-link, .btn-link:hover {
  color: fade(@primaryColor, 100%);
  text-decoration: none;
}

ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

table {
  font-size: 1rem;
}

input[type="checkbox"] {
  margin-right: 5px;
  vertical-align: middle;
}

.form-check {
  padding-left: 0;
}

:not(.material-switch) > input[type="checkbox"].form-check-input {
  display: none;
  margin-left: 0;
}

:not(.material-switch) > input[type="checkbox"].form-check-input + label {
  position: relative;
}

:not(.material-switch) > input[type="checkbox"].form-check-input + label:hover {
  cursor: pointer;
}

:not(.material-switch) > input[type="checkbox"].form-check-input + label:before {
  content: '';
  display: inline-block;
  position: relative;
  margin-right: 0.5rem;
  top: .15rem;
  left: 0;
  width: 1rem;
  height: 1rem;
  pointer-events: none;
  content: "";
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  border-radius: .25rem;
  background-color: rgba(222, 222, 222, 1);
  transition: all 0.15s ease-in;
}

:not(.material-switch) > input[type="checkbox"].form-check-input:checked + label:before {
  background-color: fade(@primaryColor, 100%);
}

:not(.material-switch) > input[type="checkbox"].form-check-input:checked + label:after {
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
  content: "\f00c";
  font-size: 8px;
  color: #fff;
  position: absolute;
  left: 0.16rem;
  top: 50%;
  -webkit-transform: translate(0, -50%);
  -ms-transform: translate(0, -50%);
  transform: translate(0, -50%);
}

#maincontent {
  opacity: 0;
}

.d-flex {
  display: -webkit-flex !important;
}

.flex-wrap {
  -webkit-flex-wrap: wrap !important;
}

.chosen-container {
  width: 100% !important;
}

.chosen-container-multi .chosen-choices li.search-choice {
  background-color: rgba(67, 71, 77, 0.7);
  color: #fff;
  background-image: none;
  border: 0;
  border-radius: 0;
  -webkit-box-shadow: none;
  box-shadow: none;
}

.chosen-container-multi .chosen-choices li.search-choice .search-choice-close {
  top: 3px;
  background: none;
  background-image: none;
}

.chosen-container-multi .chosen-choices li.search-choice .search-choice-close:hover {
  background: none;
  background-image: none;
}

.chosen-container-multi .chosen-choices li.search-choice .search-choice-close:after {
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
  content: "\f00d";
  font-size: 13px;
  background: none;
  color: rgba(255, 255, 255, 0.5);
  display: block;
  width: 12px;
  height: 12px;
}

.chosen-container-multi .chosen-choices li.search-choice .search-choice-close:hover:after {
  color: rgba(255, 255, 255, 1);
}

.chosen-container-multi .chosen-choices {
  display: block;
  width: 100%;
  padding: .375rem .75rem;
  font-size: 1rem;
  line-height: 1.5;
  color: #495057;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #ced4da;
  border-radius: .25rem;
  -webkit-transition: border-color .15s ease-in-out, -webkit-box-shadow .15s ease-in-out;
  transition: border-color .15s ease-in-out, -webkit-box-shadow .15s ease-in-out;
  -o-transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out;
  transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out;
  transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out, -webkit-box-shadow .15s ease-in-out;
  -webkit-box-shadow: none;
  box-shadow: none;
  background-image: none;
}

.chosen-container-multi .chosen-choices li.search-field input[type=text] {
  padding: 0;
  margin: 0;
  height: auto;
  color: #6c757d;
  opacity: 1;
}

[data-pageihm-visible="false"] {
  display: none;
}

.intl-tel-input {
  width: 100%;
}

.btn {
  border-radius: 0;
  -webkit-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  white-space:normal;
}

/*.btn:not(:first-child) {
  margin-left: 2px;
}

.btn:not(:last-child) {
  margin-right: 2px;
}
.btn:first-child {
 margin-left: 0px;
}
.btn:last-child {
 margin-right: 0px;
}*/

:not(.input-group-append) > .btn {
  margin-top: 2px;
  margin-bottom: 2px;
}

.btn-primary, .next .btn {
  background-color: fade(@primaryColor, 100%);
  border-color: fade(@primaryColor, 100%);
}

.btn-primary.disabled, .btn-primary:disabled {
  color: #fff;
  background-color: #ccc;
  border-color: #ccc;
}

.btn-primary:not(:disabled):not(.disabled):hover, .btn-primary:not(:disabled):not(.disabled):active {
  background-color: fade(@primaryColor, 100%);
  border-color: fade(@primaryColor, 100%);
  -webkit-filter: brightness(1.2);
  filter: brightness(1.2);
}

.btn-secondary {
  background-color: @secondaryColor;
  border-color: @secondaryColor;
}

.btn-secondary-light {
  color: #fff;
  background-color: @tertiaryColor;
  border-color: @tertiaryColor;
}
.btn-secondary-light:hover {
  color: #fff;
}

.btn-group-toggle .btn-secondary-light:not(:disabled) {
  background-color: fade(@tertiaryColor, 50%);
  border-color: fade(@tertiaryColor, 50%);
}

.btn-secondary-light:not(:disabled):not(.disabled).active, .btn-secondary-light:not(:disabled):not(.disabled):active, .show>.btn-secondary-light.dropdown-toggle {
  background-color: fade(@tertiaryColor, 100%);
  border-color: fade(@tertiaryColor, 100%);
  color: #fff;
}

.btn-danger {
  background-color: #f16565;
  border-color: #f16565;
}
.btn-danger:not(:disabled):not(.disabled):hover, .btn-danger:not(:disabled):not(.disabled):active {
  background-color: #f16565;
  border-color: #f16565;
  -webkit-filter: brightness(1.2);
  filter: brightness(1.2);
}



.bg-primary {
  background-color: fade(@primaryColor, 75%) !important;
}

.bg-secondary {
  background-color: fade(@secondaryColor, 75%) !important;
}

.bg-info {
  background-color: fade(@primaryColor, 50%) !important;
}

.stepwizard-row {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.stepwizard-step {
  padding: 7px 11px;
  border-color: fade(@primaryColor, 100%);
  color: fade(@primaryColor, 100%)
}

.stepwizard-step .circle {
  border: 2px solid fade(@primaryColor, 100%);
  border-radius: 50%;
  padding: 15px;
  position: relative;
  background-color: #fff;
  margin-bottom: 5px;
}

.stepwizard-step.disabled .circle {
  border-color: #aeb5ba;
}

.stepwizard-step:not(:first-child) .circle:before {
  content: '';
  width: 110%;
  height: 2px;
  display: block;
  right: calc(100% + 2px);
  position: absolute;
  top: calc(50% - 1px);
}

.nav-pills .nav-link.stepwizard-step {
  padding: 0;
}

.nav-pills .nav-link.stepwizard-step.active {
  background-color: transparent;
  border-color: transparent;
  color: fade(@primaryColor, 100%);
}

.nav-pills .nav-link.stepwizard-step.active .circle {
  background-color: fade(@primaryColor, 100%);
  border-color: fade(@primaryColor, 100%);
  color: #fff;
}

.nav-pills .nav-link.stepwizard-step.error .circle {
  border-color: #e72e45;
}

.stepwizard-step .circle:before {
  background-color: fade(@primaryColor, 100%);
}

.stepwizard-step.active ~ .stepwizard-step .circle:before {
  background-color: #aeb5ba;
}

.table .thead-dark {
  background-color: black;
}

.table .thead-dark th {
  background-color: fade(@primaryColor, 50%);
  border: 0;
  border-bottom: 1px solid #444;
}

.table .thead-dark th:not(:last-child) {
  border-right: 1px solid fade(@primaryColor, 60%);
}

.table-striped tbody tr:nth-of-type(odd) {
  background-color: fade(@primaryColor, 5%);
}

a.nav-link.active {
  color: fade(@primaryColor, 100%);
}

a.nav-link {
  border: 2px solid transparent;   /*   border-color: @lightGrey @lightGrey #fff;*/
}

.nav-pills .nav-link.active, .nav-pills .show>.nav-link {
  color: fade(@primaryColor, 100%);
  background-color: transparent;
  border-color: fade(@primaryColor, 100%);
}

.nav-pills .nav-link.disabled {
  color: #aeb5ba;
}

.nav-pills .nav-link.disabled:hover {
  cursor: default;
}

.tab-content {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.material-switch > input[type="checkbox"] {
  display: none;
}

.material-switch > label {
  cursor: pointer;
  height: 0px;
  position: relative;
  width: 40px;
}

.material-switch > label::before {
  background: rgb(0, 0, 0);
  -webkit-box-shadow: inset 0px 0px 10px rgba(0, 0, 0, 0.5);
  box-shadow: inset 0px 0px 10px rgba(0, 0, 0, 0.5);
  border-radius: 8px;
  content: '';
  height: 16px;
  margin-top: -8px;
  position: absolute;
  opacity: 0.3;
  -webkit-transition: all 0.4s ease-in-out;
  -o-transition: all 0.4s ease-in-out;
  transition: all 0.4s ease-in-out;
  width: 40px;
}

.material-switch > label::after {
  background: rgb(255, 255, 255);
  border-radius: 16px;
  -webkit-box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.3);
  box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.3);
  content: '';
  height: 24px;
  left: -4px;
  margin-top: -8px;
  position: absolute;
  top: -4px;
  -webkit-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
  width: 24px;
}

.material-switch > input[type="checkbox"]:checked + label::before {
  background: inherit;
  opacity: 0.5;
}

.material-switch > input[type="checkbox"]:checked + label::after {
  background: inherit;
  left: 20px;
}

.card {
  border-radius: 0;
  border: 2px solid #f1f3f5;
}

.card-header {
  border-radius: 0 !important;
  border: 0;
  background-color: #f1f3f5;
  position: relative;
}

.card-header .btn-link .card-customertitle span {
  display: block;
}

.card-header.error:before {
  color: #e72e45;
  display: block;
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
  content: "\f00d";
  font-size: 14px;
  font-size: xx-large;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  position: absolute;
  margin: auto;
  right: 0;
  padding: 0 17px;
}

.card-header.error:after {
  content: "";
  display: block;
  width: 9px;
  height: 100%;
  background-color: #e72e45;
  position: absolute;
  left: 0;
  top: 0;
}

.card {
  border-radius: 0;
  border: 2px solid #f1f3f5;
}

.card-header .btn-link {
  font-size: 16px;
  color: #43474d;
  white-space: normal;
  width: 100%;
  text-align: left;
  text-decoration: none;
}

.card-header .btn-link .plusoumoins {
  fill: #43474d;
  max-width: 100%;
  vertical-align: bottom;
  margin-right: 7px;
  width: 25px;
  height: 25px;
}

.plusoumoins .verticalbar {
  -webkit-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
  -webkit-transform-origin: 50% 50%;
  -ms-transform-origin: 50% 50%;
  transform-origin: 50% 50%;
  -webkit-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  transform: rotate(90deg);
}

.plusoumoins .verticalbar {
  -webkit-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
  -webkit-transform-origin: 50% 50%;
  -ms-transform-origin: 50% 50%;
  transform-origin: 50% 50%;
  -webkit-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  transform: rotate(90deg);
}

.collapsed .plusoumoins .verticalbar {
  -webkit-transform: rotate(0deg);
  -ms-transform: rotate(0deg);
  transform: rotate(0deg);
}

/************** LOADING **************/
#loadingcircle {
  width: 100px;
  position: fixed;
  margin: auto;
  display: block;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 9999;
}

.loadingcircle #greycircle {
  fill: #666666;
  -webkit-animation: loading 2s  linear infinite;
  animation: loading 2s  linear infinite;
  -webkit-transform-origin: center;
  -ms-transform-origin: center;
  transform-origin: center;
}

.loadingcircle #colorcircle {
  fill: fade(@primaryColor, 100%);
  -webkit-animation: loading 2s  linear infinite reverse;
  animation: loading 2s  linear infinite reverse;
  -webkit-transform-origin: center;
  -ms-transform-origin: center;
  transform-origin: center;
}

#loadingcircle-modal {
  width: 100px;
  position: relative;
  margin: auto;
  display: block;
  left: 0;
  right: 0;
  z-index: 9999;
}

@-webkit-keyframes loading {

  /* chrom */
  0% {
    opacity: 1;
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  100% {

    /*opacity: 0.5;*/
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }

}

@keyframes loading {

  /* chrom */
  0% {
    opacity: 1;
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  100% {

    /*opacity: 0.5;*/
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }

}

#wrap {
  min-height: 100%;
}

#commentsPageHaut div:not(:empty) {
  padding-top: 15px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  margin-top: 15px;
  padding-bottom: 15px;
}

/********* HEADER ********/
header {
  margin-bottom: 20px;
  background-color: @secondaryColor;
}

.navbar {
  background-color: @secondaryColor;
  padding: 15px  1rem;
}

.navbar, .navbar a {
  color: #fff;
}

.navbar-toggler-icon {
  width: auto;
  height: auto;
  font-size: 13px;
}

.navbar-toggler {
  border: 1px solid #fff;
  color: #fff;
  padding: 4px 10px 5px 10px;
  -webkit-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}

.navbar-toggler:hover {
  background-color: fade(@primaryColor, 100%);
  border: 1px solid fade(@primaryColor, 100%);
}

.nav-link {
  -webkit-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  position: relative;
}

.nav-link:hover, .nav-link[aria-expanded="true"] {
  color: fade(@primaryColor, 100%);
}

#navbarNav .svg-inline--fa {
  margin: 0 4px;
}

.lang {
  text-transform: uppercase;
  font-weight: bold;
  font-size: 0.7em;
}

/**** CREATION LOGIN *********/
ul.pager.wizard {
  list-style: none;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  padding: 0;
}

ul.pager.wizard li:only-child {
  margin-left: auto;
}

.orsepar {
  text-align: center;
  position: relative;
  margin-bottom: 15px;
  margin-top: 15px;
  z-index: 2;
}

.orsepar:before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  width: 100%;
  border-bottom: 1px solid rgba(0, 0, 0, .1);
  z-index: -1;
}

.orsepar:after {
  content: "";
  position: absolute;
  top: 0;
  left: 50%;
  width: 48px;
  height: 100%;
  margin-left: -24px;
  background-color: #fff;
  z-index: -1;
}

#divConnectFacebook {
  margin-bottom: 15px;
}

.btn-facebook {
  background-color: #4467b0;
  color: #fff;
  margin: 0;
}

.btn-facebook:hover {
  color: #fff;
  -webkit-filter: brightness(1.1);
  filter: brightness(1.2);
}

.btn-paypal {
  background-color: #1e88e5 !important;
  border-color: #1e88e5 !important;
  color: #fff !important;
  margin: 0;
}

.btn-paypal:hover {
  background-color: #1e88e5 !important;
  border-color: #1e88e5 !important;
  color: #fff !important;
  -webkit-filter: brightness(1.1);
  filter: brightness(1.2);
}

.btn-paypal:focus, .btn-paypal:active {
  background-color: #1e88e5 !important;
  border-color: #1e88e5 !important;
  color: #fff !important;
}

/* Règle très spécifique pour forcer le style du bouton PayPal */
#manual-paypal-btn, #manual-paypal-btn:hover, #manual-paypal-btn:focus, #manual-paypal-btn:active {
  background-color: #1e88e5 !important;
  border-color: #1e88e5 !important;
  color: #fff !important;
  background-image: none !important;
}

/* Surcharger les styles Bootstrap par défaut */
.btn.btn-paypal, button.btn-paypal {
  background-color: #1e88e5 !important;
  border-color: #1e88e5 !important;
  color: #fff !important;
}

/* Forcer le bouton PayPal officiel à avoir la même taille que Facebook */
#manual-paypal-btn {
  width: 100% !important;
  display: block !important;
  box-sizing: border-box !important;
  margin: 0 !important;
}

/* Styles pour tous les éléments enfants du bouton PayPal officiel */
#manual-paypal-btn * {
  width: 100% !important;
  display: block !important;
  box-sizing: border-box !important;
}

/* Styles spécifiques pour les iframes PayPal */
#manual-paypal-btn iframe {
  width: 100% !important;
  height: auto !important;
  min-height: 40px !important;
  border: none !important;
}

/* Styles pour les boutons générés par PayPal */
#manual-paypal-btn button,
#manual-paypal-btn .paypal-button,
#manual-paypal-btn [role="button"] {
  width: 100% !important;
  display: block !important;
  padding: 10px 15px !important;
  font-size: 14px !important;
  line-height: 1.42857143 !important;
  border-radius: 4px !important;
  border: 1px solid #0070ba !important;
  background-color: #0070ba !important;
  color: white !important;
  text-align: center !important;
  text-decoration: none !important;
  cursor: pointer !important;
  box-sizing: border-box !important;
}

/* Hover states pour les boutons PayPal */
#manual-paypal-btn button:hover,
#manual-paypal-btn .paypal-button:hover,
#manual-paypal-btn [role="button"]:hover {
  background-color: #005ea6 !important;
  border-color: #005ea6 !important;
  filter: brightness(1.1) !important;
}

#creationForm .nav-link .svg-inline--fa {
  font-size: 20px;
}

/****** DATATABLE ****/
table.dataTable {
  border-collapse: collapse !important;
}

table.dataTable th {
  width: auto !important;
  white-space: nowrap;
}

table.dataTable thead>tr>th.sorting_asc, table.dataTable thead>tr>th.sorting_desc, table.dataTable thead>tr>th.sorting, table.dataTable thead>tr>td.sorting_asc, table.dataTable thead>tr>td.sorting_desc, table.dataTable thead>tr>td.sorting {
  padding-right: 23px;
}

table.dataTable thead .sorting:after, table.dataTable thead .sorting_asc:after, table.dataTable thead .sorting_desc:after, table.dataTable thead .sorting_asc_disabled:after, table.dataTable thead .sorting_desc_disabled:after {
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
  content: "\f0de";
  font-size: 16px;
  right: 0.5em;
}

table.dataTable thead .sorting:before, table.dataTable thead .sorting_asc:before, table.dataTable thead .sorting_desc:before, table.dataTable thead .sorting_asc_disabled:before, table.dataTable thead .sorting_desc_disabled:before {
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
  content: "\f0dd";
  font-size: 16px;
  right: 0.5em;
}

table.dataTable thead .sorting:before, table.dataTable thead .sorting:after, table.dataTable thead .sorting_asc:before, table.dataTable thead .sorting_asc:after, table.dataTable thead .sorting_desc:before, table.dataTable thead .sorting_desc:after, table.dataTable thead .sorting_asc_disabled:before, table.dataTable thead .sorting_asc_disabled:after, table.dataTable thead .sorting_desc_disabled:before, table.dataTable thead .sorting_desc_disabled:after {
  bottom: 0.55em;
}

table.dataTable.dtr-inline.collapsed>tbody>tr[role="row"]>td:first-child:before, table.dataTable.dtr-inline.collapsed>tbody>tr[role="row"]>th:first-child:before {
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
  content: "\f0fe";
  font-size: 28px;
  color: #31aade;
  background-color: transparent;
  left: 7px;
  border: 0;
  border-radius: 0;
  -webkit-box-shadow: none;
  box-shadow: none;
  -webkit-box-sizing: content-box;
  box-sizing: content-box;
  text-align: center;
  text-indent: 0 !important;
  line-height: 20px;
}

table.dataTable.dtr-inline.collapsed>tbody>tr.parent>td:first-child:before, table.dataTable.dtr-inline.collapsed>tbody>tr.parent>th:first-child:before {
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
  content: "\f146";
  font-size: 28px;
  color: #e72e45;
  line-height: 20px;
}

table.dataTable.dtr-column>tbody>tr>td.control:before, table.dataTable.dtr-column>tbody>tr>th.control:before {
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
  content: "\f0fe";
  font-size: 28px;
  color: #31aade;
  background-color: transparent;
  left: 18px;
  border: 0;
  border-radius: 0;
  -webkit-box-shadow: none;
  box-shadow: none;
  -webkit-box-sizing: content-box;
  box-sizing: content-box;
  text-align: center;
  text-indent: 0 !important;
  line-height: 20px;
}

table.dataTable.dtr-column>tbody>tr>td.control {
  padding-left: 17px;
  display: table-cell !important;
}

table.dataTable.dtr-column>thead>tr>th.control {
  display: table-cell !important;
}

table.dataTable.dtr-column:not(.collapsed)>tbody>tr>td.control:before, table.dataTable.dtr-column:not(.collapsed)>tbody>tr>th.control:before {
  color: rgba(0, 0, 0, 0);
}

table.dataTable.dtr-column>tbody>tr.parent td.control:before, table.dataTable.dtr-column>tbody>tr.parent th.control:before {
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
  content: "\f146";
  font-size: 28px;
  color: #ff9f1c;
  background-color: transparent;
  line-height: 20px;
}

table.dataTable>tbody>tr.child ul.dtr-details {
  width: 100%;
}

table.dataTable td.dataNo {
  color: rgba(231, 46, 69, 1);
  font-weight: bold;
}

table.dataTable td.dataYes {
  color: rgba(46, 196, 182, 1);
  font-weight: bold;
}

table.dataTable .delete {
  color: rgba(231, 46, 69, 1);
}

/****** FOOTER *****/
footer {
  background-color: #43474d;
  color: rgba(255, 255, 255, 0.5);
  padding: 30px 0;
  margin-top: 20px;
}

/********** HOME **********/
#homeList ul {
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
}

#homeList li.homeItem {
  height: 200px;
  background-color: black;
  min-width: 33.33%;
}

#homeList li.homeItem a {
  height: 100%;
  display: block;
  background-color: fade(@primaryColor, 100%);
  color: #fff;
  padding: 30px 0;
  -webkit-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}

#homeList li.homeItem:nth-of-type(even) a {
  background-color: fade(@primaryColor, 95%);
}

#homeList li.homeItem a .svg-inline--fa {
  font-size: 40px;
  margin-top: 40px;
  margin-bottom: 5px;
  -webkit-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}

#homeList li.homeItem a:hover {
  -webkit-filter: brightness(1.1);
  filter: brightness(1.2);
}

#homeList li.homeItem a:hover  .svg-inline--fa {
  margin-top: 35px;
  margin-bottom: 10px;
}

@media (max-width:767.98px) {
  #homeList li.homeItem {
    min-width: 50.1%;
  }

}

/************** modal *************/
.modal-header {
  background-color: fade(@primaryColor, 100%);
  color: #fff;
}

.modal-header .close {
  color: #fff;
  text-shadow: 0 1px 0 #000;
}
#myDropzone .row {
  height:200px;
  pointer-events:   none;
}
#myDropzone {
  min-height:200px;
  background-color:@lightGrey;
  border-radius: 0.5em;
  border:0;
}
#myDropzone:hover {
  cursor:   pointer;
}
#previews .oneUpload  {
  margin-top:  7px;
}
#previews .preview {
  min-height:60px;
  min-width: 60px;
      display: block;
}