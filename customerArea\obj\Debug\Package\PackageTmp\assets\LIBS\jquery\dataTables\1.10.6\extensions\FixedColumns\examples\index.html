<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<link rel="shortcut icon" type="image/ico" href="http://www.datatables.net/favicon.ico">
	<meta name="viewport" content="initial-scale=1.0, maximum-scale=2.0">
	<link rel="stylesheet" type="text/css" href="../../../examples/resources/syntax/shCore.css">
	<link rel="stylesheet" type="text/css" href="../../../examples/resources/demo.css">
	<script type="text/javascript" language="javascript" src="../../../media/js/jquery.js"></script>
	<script type="text/javascript" language="javascript" src="../../../examples/resources/syntax/shCore.js"></script>
	<script type="text/javascript" language="javascript" src="../../../examples/resources/demo.js"></script>

	<title>FixedColumns examples - FixedColumns examples</title>
</head>

<body class="dt-example">
	<div class="container">
		<section>
			<h1>FixedColumns example <span>FixedColumns examples</span></h1>

			<div class="info">
				<p>When making use of DataTables' x-axis scrolling feature (<a href="//datatables.net/reference/option/scrollX"><code class="option" title=
				"DataTables initialisation option">scrollX<span>DT</span></code></a>), you may wish to fix the left or right most columns in place. This extension for DataTables
				provides exactly this option (for non-scrolling tables, please use the <a href="//datatables.net/extensions/fixedheader">FixedHeader extension</a>, which can fix
				headers, footers and columns). Key features include:</p>

				<ul class="markdown">
					<li>Freezes the left most column to the side of the table</li>
					<li>Option to freeze two or more columns</li>
					<li>Full integration with DataTables' scrolling options</li>
				</ul>
			</div>
		</section>
	</div>

	<section>
		<div class="footer">
			<div class="gradient"></div>

			<div class="liner">
				<div class="toc">
					<div class="toc-group">
						<h3><a href="./index.html">Examples</a></h3>
						<ul class="toc">
							<li><a href="./left_right_columns.html">Left and right fixed columns</a></li>
							<li><a href="./simple.html">Basic initialisation</a></li>
							<li><a href="./two_columns.html">Multiple fixed columns</a></li>
							<li><a href="./right_column.html">Right column only</a></li>
							<li><a href="./rowspan.html">Complex headers</a></li>
							<li><a href="./colvis.html">ColVis integration</a></li>
							<li><a href="./server-side-processing.html">Server-side processing</a></li>
							<li><a href="./css_size.html">CSS row sizing</a></li>
							<li><a href="./size_fixed.html">Assigned column width</a></li>
							<li><a href="./size_fluid.html">Fluid column width</a></li>
							<li><a href="./col_filter.html">Individual column filtering</a></li>
							<li><a href="./bootstrap.html">Bootstrap</a></li>
							<li><a href="./index_column.html">Index column</a></li>
						</ul>
					</div>
				</div>

				<div class="epilogue">
					<p>Please refer to the <a href="http://www.datatables.net">DataTables documentation</a> for full information about its API properties and methods.<br>
					Additionally, there are a wide range of <a href="http://www.datatables.net/extras">extras</a> and <a href="http://www.datatables.net/plug-ins">plug-ins</a>
					which extend the capabilities of DataTables.</p>

					<p class="copyright">DataTables designed and created by <a href="http://www.sprymedia.co.uk">SpryMedia Ltd</a> &#169; 2007-2015<br>
					DataTables is licensed under the <a href="http://www.datatables.net/mit">MIT license</a>.</p>
				</div>
			</div>
		</div>
	</section>
</body>
</html>