<%@ Page Title="" Language="C#" MasterPageFile="~/pagemaster.Master" AutoEventWireup="true" CodeBehind="passWordReset.aspx.cs" Inherits="customerArea.passWordReset" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ctPlaceHold" runat="server">
<asp:ScriptManager ID="ScriptManager1" runat="server" EnablePageMethods="True"></asp:ScriptManager>

<div class="container">
    <div class="row">
        <div class="col-12 col-lg-6 offset-lg-3 col-md-8 offset-md-2">
            <div id="commentsPageHaut">
                <asp:Literal ID="Literal2" runat="server"></asp:Literal>
                <asp:Literal ID="litForCommentaireHaut" runat="server"></asp:Literal>
            </div>
            <div id="resetPasswordWrapper">

                <h2 class="text-center" runat="server" id="spanReinitialisePassword" clientidmode="Static" data-trad="title_reset_password">Reset my password</h2>
                
                <div id="steps"><asp:Literal ID="litForSteps" runat="server"></asp:Literal></div>
                

                <div id="resetPasswordForm" class="form-horizontal" role="form">
                    <div class="row form-group">
                        <label class="col-12 control-label text-md-right sr-only">
                            <asp:Label for="tbMotPasse" id="lblPassword" runat="server" data-trad="lbl_password">Password</asp:Label>
                        </label>
                        <div class="col-12">
                            <asp:TextBox ID="tbMotPasse" runat="server" MaxLength="50" TextMode="Password" class="form-control" name="password" placeholder="Password" data-tradplaceholder="placeholder_password" required></asp:TextBox>
                            <div class="invalid-feedback"  data-trad="password_explain"></div>
                        </div>
                    </div>
                    <div class="row form-group">
                        <label class="col-12 control-label text-md-right sr-only">
                            <asp:Label for="tbMotPasseConfirme" id="lblConfirmPassword" runat="server" data-trad="lbl_confirm_password">Confirm password</asp:Label>
                        </label>
                        <div class="col-12">
                            <asp:TextBox ID="tbMotPasseConfirme" runat="server" AutoCompleteType="Disabled" MaxLength="50" TextMode="Password" class="form-control" name="confirmPassword" placeholder="Confirm password" data-tradplaceholder="lbl_confirm_password" required></asp:TextBox>
                            <div class="invalid-feedback" data-trad="msg_error_confirm_password">Your password is not identical</div>
                        </div>
                    </div>
                    <div class="row form-group">
                        <div class="col-12">
                        <small  class="form-text text-muted"  data-trad='password_explain'></small>
                    </div>
                    </div>

                    <span id="spBtnResetPassword" runat="server" >
                        <a href="" id="LnkBtnCreate" class="btn btn-primary btn-block" data-trad="btn_update" >Update</a>
                    </span>

                </div>
            </div>

            <div id="divCommentaireBas" class="small"><asp:Literal ID="litForCommentaireBas" runat="server"></asp:Literal></div>

        </div>
    </div>
</div>

<span id="isLinkValid" runat="server" class="d-none"></span>
<asp:HyperLink ID="hlHomePage" runat="server" class="d-none">homePage</asp:HyperLink>
</asp:Content>
