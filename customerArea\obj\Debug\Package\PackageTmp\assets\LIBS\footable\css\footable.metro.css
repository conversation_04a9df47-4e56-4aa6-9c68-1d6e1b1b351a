.footable {
  border-collapse: separate;
  border-spacing: 0;
  width: 100%;
  border: 3px solid #52b9e9;
  font-family: "Open Sans", Arial, Helvetica, sans-serif;
  color: #444444;
  background: #efefef;
  -moz-border-radius: 0;
  -webkit-border-radius: 0;
  border-radius: 0;
}
.footable.breakpoint > tbody > tr.footable-row-detail {
  background: #eeeeee;
}
.footable.breakpoint > tbody > tr:hover:not(.footable-row-detail) {
  cursor: pointer;
}
.footable.breakpoint > tbody > tr > td.footable-row-detail-cell {
  background: #fff;
}
.footable > tbody img {
  vertical-align: middle;
}
.footable > tbody > tr:hover:not(.footable-row-detail) {
  background: #dddddd;
}
.footable > tbody > tr:hover:not(.footable-row-detail) > td {
  color: #000000;
}
.footable > tbody > tr:last-child > td {
  border-bottom: none;
}
.footable > tbody > tr > td {
  border-top: 1px solid #dddddd;
  padding: 10px;
  text-align: left;
  border-left: none;
}
.footable > tbody > tr > td.footable-row-detail-cell,
.footable > tbody > tr > td.footable-first-column {
  border-left: none;
}
.footable > thead > tr > th {
  border-bottom: 1px solid #dddddd;
  padding: 10px;
  text-align: left;
}
.footable > thead > tr > th,
.footable > thead > tr > td {
  background-color: #52b9e9;
  border: 1px solid #52b9e9;
  color: #ffffff;
  border-top: none;
  border-left: none;
  font-weight: normal;
}
.footable > thead > tr > th.footable-first-column,
.footable > thead > tr > td.footable-first-column {
  border-left: none;
}
.footable > tfoot > tr > th,
.footable > tfoot > tr > td {
  background-color: #52b9e9;
  border: 1px solid #52b9e9;
  color: #ffffff;
  border-top: none;
  border-left: none;
  padding: 5px;
}
.footable > tfoot .pagination {
  text-align: center;
}
.footable > tfoot .pagination ul {
  display: inline-block;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
  vertical-align: middle;
  margin: 0;
  padding: 0;
}
.footable > tfoot .pagination ul > li {
  display: inline;
}
.footable > tfoot .pagination ul > li > a,
.footable > tfoot .pagination ul > li > span {
  float: left;
  border: 2px solid transparent;
  margin: 0 3px;
  color: #ffffff;
  background: transparent;
  padding: 1px 5px;
  text-decoration: none;
}
.footable > tfoot .pagination ul > li.disabled > a {
  color: #888;
}
.footable > tfoot .pagination ul > li.active > a {
  border-color: #ffffff;
}
.footable > tfoot .pagination ul > li:first-child > a,
.footable > tfoot .pagination ul > li:last-child > a,
.footable > tfoot .pagination ul > li:first-child > span,
.footable > tfoot .pagination ul > li:last-child > span {
  -moz-border-radius: 0;
  -webkit-border-radius: 0;
  border-radius: 0;
}
.footable.metro-red {
  border-color: #fa3031;
}
.footable.metro-red > tfoot > tr > th,
.footable.metro-red > thead > tr > th,
.footable.metro-red > tfoot > tr > td,
.footable.metro-red > thead > tr > td {
  background-color: #fa3031;
  border-color: #fa3031;
}
.footable.metro-purple {
  border-color: #932ab6;
}
.footable.metro-purple > tfoot > tr > th,
.footable.metro-purple > thead > tr > th,
.footable.metro-purple > tfoot > tr > td,
.footable.metro-purple > thead > tr > td {
  background-color: #932ab6;
  border-color: #932ab6;
}
.footable.metro-green {
  border-color: #43c83c;
}
.footable.metro-green > tfoot > tr > th,
.footable.metro-green > thead > tr > th,
.footable.metro-green > tfoot > tr > td,
.footable.metro-green > thead > tr > td {
  background-color: #43c83c;
  border-color: #43c83c;
}
.footable.metro-blue {
  border-color: #1171a3;
}
.footable.metro-blue > tfoot > tr > th,
.footable.metro-blue > thead > tr > th,
.footable.metro-blue > tfoot > tr > td,
.footable.metro-blue > thead > tr > td {
  background-color: #1171a3;
  border-color: #1171a3;
}
.footable-odd {
  background-color: #f7f7f7;
}
