using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using utilitaires2010;

namespace customerArea
{
    public partial class test_paypal_flow : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            try
            {
                string structureId = Request.QueryString["idstructure"] ?? "991";
                string plateformCode = "Customer";
                string lang = "fr";
                int resulEvent = 0;
                string idpa = "0";

                List<utilitaires2010.CustomJsonSettings> listJsonSettingsToMerge = new List<utilitaires2010.CustomJsonSettings>
                {
                    new utilitaires2010.CustomJsonSettings() { KeyOfJsonPath = "physicalPathOfSettingsJSON", IsDefault = true },
                    new utilitaires2010.CustomJsonSettings() { KeyOfJsonPath = "physicalPathOfSettingsJSON", IsDefault = false },
                    new utilitaires2010.CustomJsonSettings() { KeyOfJsonPath = "physicalPathOfSettingsPlateformJSON", IsDefault = false }
                };

                dynamic globalPlateform = utilitaires2010.Initialisations.GetMergeSettings(listJsonSettingsToMerge, int.Parse(structureId), resulEvent, int.Parse(idpa), plateformCode, lang);

                string appid = globalPlateform.login.payPal.appid.Value;
                string secret = globalPlateform.login.payPal.secret.Value;
                string returnurl = globalPlateform.login.payPal.returnurl.Value;

                string configInfo = $"Structure ID: {structureId}\n";
                configInfo += $"Client ID: {appid}\n";
                configInfo += $"Secret: {secret.Substring(0, 10)}...\n";
                configInfo += $"Return URL: {returnurl}\n";
                
                // Générer l'URL PayPal complète
                string scope = "openid email profile address";
                string paypalUrl = $"https://www.sandbox.paypal.com/signin/authorize?client_id={appid}&response_type=code&scope={HttpUtility.UrlEncode(scope)}&redirect_uri={HttpUtility.UrlEncode(returnurl)}";
                
                if (!string.IsNullOrEmpty(structureId))
                {
                    paypalUrl += "&state=" + HttpUtility.UrlEncode($"idstructure={structureId}");
                }

                Response.Write($"<script>");
                Response.Write($"document.getElementById('config-info').textContent = '{configInfo.Replace("'", "\\'").Replace("\n", "\\n")}';");
                Response.Write($"document.getElementById('paypal-url').textContent = '{paypalUrl.Replace("'", "\\'")}';");
                Response.Write($"document.getElementById('paypal-link').href = '{paypalUrl.Replace("'", "\\'")}';");
                Response.Write($"</script>");
            }
            catch (Exception ex)
            {
                Response.Write($"<div style='color: red;'>Erreur: {ex.Message}</div>");
            }
        }
    }
}
