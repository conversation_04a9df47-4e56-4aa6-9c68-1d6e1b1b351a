using System;
using System.Collections.Generic;
using System.Web.UI;
using Newtonsoft.Json;

namespace customerArea
{
    public partial class test_paypal_settings : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            string structureId = "991"; // Structure de test
            string plateformCode = "";
            string lang = "fr";
            int resulEvent = 0;
            string idpa = "0";

            if (Request.QueryString["idstructure"] != null)
            {
                structureId = Request.QueryString["idstructure"];
            }

            List<utilitaires2010.CustomJsonSettings> listJsonSettingsToMerge = new List<utilitaires2010.CustomJsonSettings>
            {
                new utilitaires2010.CustomJsonSettings() { KeyOfJsonPath = "physicalPathOfSettingsJSON", IsDefault = true },
                new utilitaires2010.CustomJsonSettings() { KeyOfJsonPath = "physicalPathOfSettingsJSON", IsDefault = false },
                new utilitaires2010.CustomJsonSettings() { KeyOfJsonPath = "physicalPathOfSettingsPlateformJSON", IsDefault = false }
            };

            try
            {
                // Récupération des settings avec merge
                dynamic globalPlateform = utilitaires2010.Initialisations.GetMergeSettings(listJsonSettingsToMerge, int.Parse(structureId), resulEvent, int.Parse(idpa), plateformCode, lang);

                // Test de récupération des settings PayPal
                string paypalAppId = "";
                string paypalSecret = "";
                string paypalReturnUrl = "";
                string cryptoKeyValue = "";

                try
                {
                    // Récupération de l'appid PayPal
                    dynamic paypalAppIdDynamic = globalPlateform.payPal.appid.Value;
                    if (globalPlateform.customer != null && globalPlateform.customer.payPal != null && globalPlateform.customer.payPal.appid != null)
                    {
                        paypalAppIdDynamic = globalPlateform.customer.payPal.appid.Value;
                    }
                    paypalAppId = paypalAppIdDynamic;
                }
                catch (Exception ex)
                {
                    paypalAppId = "Erreur: " + ex.Message;
                }

                try
                {
                    // Récupération du secret PayPal
                    dynamic paypalSecretDynamic = globalPlateform.payPal.secret.Value;
                    if (globalPlateform.customer != null && globalPlateform.customer.payPal != null && globalPlateform.customer.payPal.secret != null)
                    {
                        paypalSecretDynamic = globalPlateform.customer.payPal.secret.Value;
                    }
                    paypalSecret = paypalSecretDynamic;
                }
                catch (Exception ex)
                {
                    paypalSecret = "Non configuré dans JSON: " + ex.Message;
                }

                try
                {
                    // Récupération de l'URL de retour PayPal
                    dynamic paypalReturnUrlDynamic = globalPlateform.payPal.returnurl.Value;
                    if (globalPlateform.customer != null && globalPlateform.customer.payPal != null && globalPlateform.customer.payPal.returnurl != null)
                    {
                        paypalReturnUrlDynamic = globalPlateform.customer.payPal.returnurl.Value;
                    }
                    paypalReturnUrl = paypalReturnUrlDynamic;
                }
                catch (Exception ex)
                {
                    paypalReturnUrl = "Erreur: " + ex.Message;
                }

                try
                {
                    // Récupération de la CryptoKey
                    dynamic cryptoKeyDynamic = globalPlateform.cryptoKey.Value;
                    if (globalPlateform.customer != null && globalPlateform.customer.cryptoKey != null)
                    {
                        cryptoKeyDynamic = globalPlateform.customer.cryptoKey.Value;
                    }
                    cryptoKeyValue = cryptoKeyDynamic;
                }
                catch (Exception ex)
                {
                    cryptoKeyValue = "Non configuré dans JSON: " + ex.Message;
                }

                // Affichage des settings dans les spans existants
                settingsPlateform.InnerText = JsonConvert.SerializeObject(utilitaires2010.Initialisations.LoadSettingsOfCustomer("physicalPathOfSettingsPlateformJSON", plateformCode, int.Parse(structureId), resulEvent, int.Parse(idpa), lang, false), Formatting.Indented);
                settingsCustomer.InnerText = JsonConvert.SerializeObject(utilitaires2010.Initialisations.LoadSettingsOfCustomer("physicalPathOfSettingsJSON", plateformCode, int.Parse(structureId), resulEvent, int.Parse(idpa), lang, false), Formatting.Indented);
                settingsDefaultCustomer.InnerText = JsonConvert.SerializeObject(utilitaires2010.Initialisations.LoadSettingsOfCustomer("physicalPathOfSettingsJSON", plateformCode, int.Parse(structureId), resulEvent, int.Parse(idpa), lang, true), Formatting.Indented);

                // Log des valeurs récupérées pour debug
                Response.Write("<!-- PayPal AppId: " + paypalAppId + " -->");
                Response.Write("<!-- PayPal Secret: " + (paypalSecret.Length > 10 ? paypalSecret.Substring(0, 10) + "..." : paypalSecret) + " -->");
                Response.Write("<!-- PayPal Return URL: " + paypalReturnUrl + " -->");
                Response.Write("<!-- Crypto Key: " + (cryptoKeyValue.Length > 10 ? cryptoKeyValue.Substring(0, 10) + "..." : cryptoKeyValue) + " -->");
            }
            catch (Exception ex)
            {
                Response.Write("Erreur: " + ex.Message);
            }
        }
    }
}
