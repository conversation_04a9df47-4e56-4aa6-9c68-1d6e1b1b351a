<%@ Page Language="C#" AutoEventWireup="true" %>

<!DOCTYPE html>
<html>
<head>
    <title>Configuration PayPal - Structure 0426</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.1.0/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>
<body>
    <div class="container mt-5">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h3><i class="fab fa-paypal"></i> Configuration PayPal - Structure 0426</h3>
            </div>
            <div class="card-body">
                
                <!-- Étape 1 -->
                <div class="alert alert-info">
                    <h5><i class="fas fa-step-forward"></i> Étape 1 : Application PayPal créée</h5>
                    <p>✅ Vous devez avoir créé une application sur <a href="https://developer.paypal.com/" target="_blank">PayPal Developer Dashboard</a></p>
                </div>

                <!-- Étape 2 : Configuration -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-cog"></i> Étape 2 : Configuration requise</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>🔑 Informations PayPal :</h6>
                                <div class="form-group">
                                    <label>Client ID (Sandbox) :</label>
                                    <input type="text" class="form-control" id="clientId" placeholder="A..." />
                                </div>
                                <div class="form-group">
                                    <label>Client Secret (Sandbox) :</label>
                                    <input type="text" class="form-control" id="clientSecret" placeholder="E..." />
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6>🔗 URLs de redirection :</h6>
                                <ul class="list-group">
                                    <li class="list-group-item">https://dev.themisweb.fr/customer/_loginPayPal.aspx</li>
                                    <li class="list-group-item">https://test.themisweb.fr/customer/_loginPayPal.aspx</li>
                                    <li class="list-group-item">http://localhost:56846/_loginPayPal.aspx</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="row mt-3">
                            <div class="col-md-12">
                                <h6>🎯 Scopes (permissions) :</h6>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="checkbox" id="scope-openid" checked disabled>
                                    <label class="form-check-label" for="scope-openid">openid (obligatoire)</label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="checkbox" id="scope-email" checked>
                                    <label class="form-check-label" for="scope-email">email</label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="checkbox" id="scope-profile" checked>
                                    <label class="form-check-label" for="scope-profile">profile</label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="checkbox" id="scope-address">
                                    <label class="form-check-label" for="scope-address">address</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Étape 3 : Test de configuration -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-test-tube"></i> Étape 3 : Test de la configuration</h5>
                    </div>
                    <div class="card-body">
                        <button type="button" class="btn btn-success" onclick="testConfiguration()">
                            <i class="fas fa-play"></i> Tester la configuration
                        </button>
                        <div id="test-result" class="mt-3"></div>
                    </div>
                </div>

                <!-- Étape 4 : Génération du fichier de configuration -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-file-code"></i> Étape 4 : Configuration JSON</h5>
                    </div>
                    <div class="card-body">
                        <p>Configuration JSON pour votre fichier externe :</p>
                        <pre id="json-config" class="bg-light p-3"></pre>
                        <button type="button" class="btn btn-primary" onclick="generateConfig()">
                            <i class="fas fa-code"></i> Générer la configuration
                        </button>
                    </div>
                </div>

                <!-- Étape 5 : Bouton de test -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-mouse-pointer"></i> Étape 5 : Test du bouton PayPal</h5>
                    </div>
                    <div class="card-body">
                        <div id="paypal-test-button"></div>
                        <button type="button" class="btn btn-warning mt-2" onclick="createTestButton()">
                            <i class="fab fa-paypal"></i> Créer le bouton de test
                        </button>
                    </div>
                </div>

            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://www.sandbox.paypal.com/js/external/api.js"></script>
    
    <script>
        function testConfiguration() {
            var clientId = $('#clientId').val();
            var clientSecret = $('#clientSecret').val();
            
            if (!clientId || !clientSecret) {
                $('#test-result').html('<div class="alert alert-warning">⚠️ Veuillez remplir Client ID et Client Secret</div>');
                return;
            }
            
            $('#test-result').html('<div class="alert alert-info"><i class="fas fa-spinner fa-spin"></i> Test en cours...</div>');
            
            // Test de l'API PayPal
            var credentials = btoa(clientId + ':' + clientSecret);
            
            $.ajax({
                url: 'https://api.sandbox.paypal.com/v1/oauth2/token',
                type: 'POST',
                headers: {
                    'Authorization': 'Basic ' + credentials,
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                data: 'grant_type=client_credentials',
                success: function(response) {
                    $('#test-result').html('<div class="alert alert-success">✅ Configuration PayPal valide !</div>');
                },
                error: function(xhr, status, error) {
                    $('#test-result').html('<div class="alert alert-danger">❌ Erreur de configuration : ' + xhr.responseText + '</div>');
                }
            });
        }
        
        function generateConfig() {
            var clientId = $('#clientId').val();
            var clientSecret = $('#clientSecret').val();
            
            if (!clientId || !clientSecret) {
                alert('Veuillez remplir Client ID et Client Secret');
                return;
            }
            
            var scopes = [];
            if ($('#scope-openid').is(':checked')) scopes.push('openid');
            if ($('#scope-email').is(':checked')) scopes.push('email');
            if ($('#scope-profile').is(':checked')) scopes.push('profile');
            if ($('#scope-address').is(':checked')) scopes.push('address');
            
            var config = {
                "login": {
                    "payPal": {
                        "appid": clientId,
                        "secret": clientSecret,
                        "returnurl": "https://dev.themisweb.fr/customer/_loginPayPal.aspx?idstructure=[structureid]",
                        "scopes": scopes.join(' ')
                    }
                }
            };
            
            $('#json-config').text(JSON.stringify(config, null, 2));
        }
        
        function createTestButton() {
            var clientId = $('#clientId').val();
            
            if (!clientId) {
                alert('Veuillez remplir le Client ID');
                return;
            }
            
            $('#paypal-test-button').html('<div id="paypal-btn-test"></div>');
            
            paypal.use(['login'], function (login) {
                login.render({
                    "appid": clientId,
                    "scopes": "openid email profile",
                    "containerid": "paypal-btn-test",
                    "responseType": "code",
                    "locale": "fr-fr",
                    "buttonType": "CWP",
                    "buttonShape": "pill",
                    "buttonSize": "medium",
                    "fullPage": "true",
                    "returnurl": "https://dev.themisweb.fr/customer/_loginPayPal.aspx?idstructure=0426",
                    "state": "idstructure=0426"
                });
            });
        }
    </script>
</body>
</html>
