﻿$(document).ready(function() {
    LoadPageResetPassword();
});

function LoadPageResetPassword() {
	if($('#ctPlaceHold_isLinkValid').text() == "false") {
		$('#resetPasswordWrapper').html(ReadXmlTranslate("msg_error_password_link_already_clicked"))
	}
    // $('#resetPasswordForm').data('bootstrapValidator');
    $('#ctPlaceHold_tbMotPasse, #ctPlaceHold_tbMotPasseConfirme').attr('pattern', settings.global.passwordRules)
    $('#LnkBtnCreate').on('click', function(e) {
        e.preventDefault();
        $('#LnkBtnCreate').addClass('disabled')
        var curForm = $('#resetPasswordForm')
        if (validForm(curForm)) {
            var structId = $('#ctPlaceHold_spBtnResetPassword').data("structureid");
            var identiteId = $('#ctPlaceHold_spBtnResetPassword').data("identiteid");
            var demandResetPassword = $('#ctPlaceHold_spBtnResetPassword').data("demandresetpassword");
            var hash = $('#ctPlaceHold_spBtnResetPassword').data("hash");
            jsChangePassWord(structId, identiteId, demandResetPassword, hash);

        } else {
            showAlertError('', ReadXmlTranslate("msg_error_update"), '')
            setTimeout(function() {
		        $('#LnkBtnCreate').removeClass('disabled')
		    }, 2000);
            
        }
        //$('#resetPasswordForm').data('bootstrapValidator').validate()

    });
}


/// envoi du mot de passe
function jsChangePassWord(structureid, identifid, demandResetPassword, computHsh) {

    var newPass = $('#ctPlaceHold_tbMotPasse').val();
    var pgmeth = PageMethods.ChangePassWord(structureid,
        identifid, demandResetPassword, computHsh, newPass, jsChangePassWordSucess, MethodeJS_Error);
}

function jsChangePassWordSucess(resultc, userContext, methodName) {
    if (resultc.split(':')[0] == "success") {

        baseUrlDV = GetUrlTemplate("pageModalResetPassword");

        if (fileExists(baseUrlDV)) {

            if (baseUrlDV.contains("Default")) {
                $('#resetPasswordWrapper').html(ReadXmlTranslate(resultc.split(':')[1]))
            } else {
                // url = baseUrlDV + language + ".html";
                url = baseUrlDV;

                var text = $.ajax({
                    url: url,
                    dataType: 'jsonp', // jsonp
                    async: false //IGNORED!!
                })
                $('#resetPasswordWrapper').html(ReadXmlTranslate(resultc.split(':')[1]))
                ShowGenericModal('', text.responseText, '', '', '', '')
            }
        }

        
    } else {
        console.debug("resultc=" + resultc)
        showAlertError('', ReadXmlTranslate(resultc.split(':')[1]), '');

        $('#LnkBtnCreate').removeClass('disabled')
        //ShowModalError('modalMessage', '', ReadXmlTranslate(resultc.split(':')[1]), 'text-danger', 5000);
    }
}