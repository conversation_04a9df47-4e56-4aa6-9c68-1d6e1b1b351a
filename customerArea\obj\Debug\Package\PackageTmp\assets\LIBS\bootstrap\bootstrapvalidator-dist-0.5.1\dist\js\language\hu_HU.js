(function($) {
    /**
     * Hungarian language package
     * Translated by @blackfyre
     */
    $.fn.bootstrapValidator.i18n = $.extend(true, $.fn.bootstrapValidator.i18n, {
        base64: {
            'default': '<PERSON><PERSON><PERSON><PERSON>, hogy érvényes base 64 karakter láncot adj meg'
        },
        between: {
            'default': '<PERSON><PERSON>rlek, hogy %s és %s között adj meg értéket',
            notInclusive: '<PERSON><PERSON><PERSON><PERSON>, hogy %s és %s között adj meg értéket'
        },
        callback: {
            'default': '<PERSON><PERSON>rle<PERSON>, hogy érvényes értéket adj meg'
        },
        choice: {
            'default': '<PERSON><PERSON><PERSON><PERSON>, hogy érvényes értéket adj meg',
            less: '<PERSON><PERSON><PERSON><PERSON>, hogy legalább %s lehetőséget válassz ki',
            more: '<PERSON><PERSON>rle<PERSON>, hogy maximum %s lehetőséget válassz ki',
            between: '<PERSON><PERSON><PERSON><PERSON>, hogy válassz %s - %s lehetőséget'
        },
        creditCard: {
            'default': '<PERSON><PERSON><PERSON><PERSON>, hogy érvényes bankkártya számot adj meg'
        },
        cusip: {
            'default': '<PERSON><PERSON>rlek, hogy érvényes CUSIP számot ajd meg'
        },
        cvv: {
            'default': 'Kérlek, hogy érvényes CVV számot ajd meg'
        },
        date: {
            'default': 'Kérlek, hogy érvényes dátumot ajd meg'
        },
        different: {
            'default': 'Kérlek, hogy egy másik értéket adj meg'
        },
        digits: {
            'default': 'Kérlek, hogy csak számot adj meg'
        },
        ean: {
            'default': 'Kérlek, hogy érvényes EAN számot ajd meg'
        },
        emailAddress: {
            'default': 'Kérlek, hogy érvényes email címet ajd meg'
        },
        file: {
            'default': 'Kérlek, hogy érvényes fájlt válassz'
        },
        greaterThan: {
            'default': 'Kérlek, hogy ezzel (%s) egyenlő vagy nagyobb számot adj meg',
            notInclusive: 'Kérlek, hogy ennél (%s) nagyobb számot adj meg'
        },
        grid: {
            'default': 'Kérlek, hogy érvényes GRId számot ajd meg'
        },
        hex: {
            'default': 'Kérlek, hogy érvényes hexadecimális számot ajd meg'
        },
        hexColor: {
            'default': 'Kérlek, hogy érvényes hexadecimális színt ajd meg'
        },
        iban: {
            'default': 'Kérlek, hogy érvényes IBAN számot ajd meg',
            countryNotSupported: 'A(z) %s országkód nem támogatott',
            country: 'Kérlek, hogy helyes %s IBAN számot adj meg',
            countries: {
                AD: 'andorrai',
                AE: 'Egyesül Arab Emirátusok beli',
                AL: 'albán',
                AO: 'angolai',
                AT: 'osztrák',
                AZ: 'azerbajdzsáni',
                BA: 'Bosznia-Hercegovina beli',
                BE: 'belga',
                BF: 'Burkina Faso beli',
                BG: 'bulgár',
                BH: 'bahraini',
                BI: 'burundi',
                BJ: 'benini',
                BR: 'brazil',
                CH: 'svájci',
                CI: 'elefántcsontparti',
                CM: 'camerooni',
                CR: 'Costa Rica-i',
                CV: 'Zöld-foki Köztársaság beli',
                CY: 'ciprusi',
                CZ: 'cseh',
                DE: 'Német',
                DK: 'dán',
                DO: 'Dominikai Köztársaság beli',
                DZ: 'algériai',
                EE: 'észtországi',
                ES: 'spanyol',
                FI: 'finn',
                FO: 'Feröer-szigetek-i',
                FR: 'francia',
                GB: 'Egyesült Királyság beli',
                GE: 'grúz',
                GI: 'gibraltári',
                GL: 'grönlandi',
                GR: 'görög',
                GT: 'guatemalai',
                HR: 'horvát',
                HU: 'magyar',
                IE: 'ír',
                IL: 'izraeli',
                IR: 'iráni',
                IS: 'izlandi',
                IT: 'olasz',
                JO: 'jordán',
                KW: 'kuwaiti',
                KZ: 'kazah',
                LB: 'libanoni',
                LI: 'liechtensteini',
                LT: 'litván',
                LU: 'luxembourgi',
                LV: 'latviai',
                MC: 'monacoi',
                MD: 'moldáv',
                ME: 'montenegroi',
                MG: 'madagaszkári',
                MK: 'macedón',
                ML: 'Mali -i',
                MR: 'Mauritánia -i',
                MT: 'máltai',
                MU: 'Mauritius -i',
                MZ: 'mozambik',
                NL: 'holland',
                NO: 'norvég',
                PK: 'pakisztáni',
                PL: 'lengyel',
                PS: 'palesztín',
                PT: 'portugál',
                QA: 'qatari',
                RO: 'román',
                RS: 'szerb',
                SA: 'saudi',
                SE: 'svéd',
                SI: 'szlovén',
                SK: 'szlovák',
                SM: 'San Marino beli',
                SN: 'senegali',
                TN: 'tunéz',
                TR: 'török',
                VG: 'Brit Virgin-szigetek beli'
            }
        },
        id: {
            'default': 'Kérlek, hogy érvényes személy azonosító számot adj meg',
            countryNotSupported: 'A(z) %s országkód nem támogatott',
            country: 'Kérlek, hogy érvényes %s személy azonosító számot ajd meg',
            countries: {
                BA: 'Bosznia-Hercegovina beli',
                BG: 'bulgár',
                BR: 'brazil',
                CH: 'svájci',
                CL: 'chilei',
                CZ: 'cseh',
                DK: 'dán',
                EE: 'észt',
                ES: 'spanyol',
                FI: 'finn',
                HR: 'horvát',
                IE: 'ír',
                IS: 'izlandi',
                LT: 'litván',
                LV: 'lett',
                ME: 'montenegroi',
                MK: 'macedón',
                NL: 'holland',
                RO: 'román',
                RS: 'szerb',
                SE: 'svéd',
                SI: 'szlovén',
                SK: 'szlovák',
                SM: 'San Marino beli',
                ZA: 'dél afrikai'
            }
        },
        identical: {
            'default': 'Kérlek, hogy ugyan azt az értéket add meg'
        },
        imei: {
            'default': 'Kérlek, hogy érvényes IMEI számot adj meg'
        },
        imo: {
            'default': 'Kérlek, hogy érvényes IMO számot adj meg'
        },
        integer: {
            'default': 'Kérlek, hogy számot adj meg'
        },
        ip: {
            'default': 'Kérlek, hogy IP címet adj meg',
            ipv4: 'Kérlek, hogy érvényes IPv4 címet ajd meg',
            ipv6: 'Kérlek, hogy érvényes IPv6 címet ajd meg'
        },
        isbn: {
            'default': 'Kérlek, hogy érvényes ISBN számot ajd meg'
        },
        isin: {
            'default': 'Kérlek, hogy érvényes ISIN számot ajd meg'
        },
        ismn: {
            'default': 'Kérlek, hogy érvényes ISMN számot ajd meg'
        },
        issn: {
            'default': 'Kérlek, hogy érvényes ISSN számot ajd meg'
        },
        lessThan: {
            'default': 'Kérlek, hogy adj meg egy számot ami kisebb vagy egyenlő mint %s',
            notInclusive: 'Kérlek, hogy adj meg egy számot ami kisebb mint %s'
        },
        mac: {
            'default': 'Kérlek, hogy érvényes MAC címet ajd meg'
        },
        meid: {
            'default': 'Kérlek, hogy érvényes MEID számot ajd meg'
        },
        notEmpty: {
            'default': 'Kérlek, hogy adj értéket a mezőnek'
        },
        numeric: {
            'default': 'Please enter a valid float number'
        },
        phone: {
            'default': 'Kérlek, hogy érvényes telefonszámot adj meg',
            countryNotSupported: 'A(z) %s országkód nem támogatott',
            country: 'Kérlek, hogy érvényes %s telefonszámot adj meg',
            countries: {
                BR: 'brazil',
                ES: 'spanyol',
                FR: 'francia',
                GB: 'Egyesült Királyság beli',
                MA: 'Marokkó',
                PK: 'Pakisztán',
                US: 'Egyesült Államok beli'
            }
        },
        regexp: {
            'default': 'Kérlek, hogy a mintának megfelelő értéket adj meg'
        },
        remote: {
            'default': 'Kérlek, hogy érvényes értéket adj meg'
        },
        rtn: {
            'default': 'Kérlek, hogy érvényes RTN számot adj meg'
        },
        sedol: {
            'default': 'Kérlek, hogy érvényes SEDOL számot adj meg'
        },
        siren: {
            'default': 'Kérlek, hogy érvényes SIREN számot adj meg'
        },
        siret: {
            'default': 'Kérlek, hogy érvényes SIRET számot adj meg'
        },
        step: {
            'default': 'Kérlek, hogy érvényes lépteket adj meg (%s)'
        },
        stringCase: {
            'default': 'Kérlek, hogy csak kisbetüket ajd meg',
            upper: 'Kérlek, hogy csak nagy betüket adj meg'
        },
        stringLength: {
            'default': 'Kérlek, hogy érvényes karakter hosszúsággal adj meg értéket',
            less: 'Kérlek, hogy kevesebb mint %s karaktert adj meg',
            more: 'Kérlek, hogy több mint %s karaktert adj meg',
            between: 'Kérlek, hogy legalább %s, de maximum %s karaktert adj meg'
        },
        uri: {
            'default': 'Kérlek, hogy helyes URI -t adj meg'
        },
        uuid: {
            'default': 'Kérlek, hogy érvényes UUID számot adj meg',
            version: 'Kérlek, hogy érvényes UUID verzió %s számot adj meg'
        },
        vat: {
            'default': 'Kérlek, hogy helyes adó számot adj meg',
            countryNotSupported: '%s - nem támogatott ország',
            country: 'Kérlek, hogy heles %s adószámot ajd meg',
            countries: {
                AT: 'osztrák',
                BE: 'belga',
                BG: 'bulgár',
                BR: 'brazil',
                CH: 'svájci',
                CY: 'ciprusi',
                CZ: 'cseh',
                DE: 'német',
                DK: 'dán',
                EE: 'észt',
                ES: 'spanyol',
                FI: 'finn',
                FR: 'francia',
                GB: 'Egyesült Királyság beli',
                GR: 'görög',
                EL: 'görög',
                HU: 'magyar',
                HR: 'horvát',
                IE: 'ír',
                IS: 'izlandi',
                IT: 'olasz',
                LT: 'litván',
                LU: 'luxemburgi',
                LV: 'latviai',
                MT: 'máltai',
                NL: 'holland',
                NO: 'norvég',
                PL: 'lengyel',
                PT: 'portugál',
                RO: 'román',
                RU: 'orosz',
                RS: 'szerb',
                SE: 'svéd',
                SI: 'szlovén',
                SK: 'szlovák',
                ZA: 'dél afrikai'
            }
        },
        vin: {
            'default': 'Kérlek, hogy érvényes VIN számot adj meg'
        },
        zipCode: {
            'default': 'Kérlek, hogy érvényes irányítószámot adj meg',
            countryNotSupported: '%s - nem támogatott ország',
            country: 'Kérlek, hogy helyes %s irányítószámot adj meg',
            countries: {
                BR: 'brazil',
                CA: 'kanadai',
                DK: 'dán',
                GB: 'Egyesült Királyság beli',
                IT: 'olasz',
                MA: 'Marokkó',
                NL: 'holland',
                SE: 'svájci',
                SG: 'singaporei',
                US: 'Egyesült Államok beli'
            }
        }
    });
}(window.jQuery));
