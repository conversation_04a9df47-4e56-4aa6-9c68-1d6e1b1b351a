$(document).ready(function() {
	$('[data-trad="lbl_resa_available_until"], [data-trad="lbl_resa_expired_since"]').each(function(i,k ) {
		var dataLang= $(k).attr("data-lang").toLowerCase()
		moment.locale(dataLang);
	  	var resaAvailableUntilDate = moment($(k).attr("data-date"), "DD/MM/YYYY hh:mm:ss")
	  	var resaAvailableUntilDateDayTxt = resaAvailableUntilDate.format('dddd')
	  	var resaAvailableUntilDateDay = resaAvailableUntilDate.format('D')
	  	var resaAvailableUntilDateMonthTxt = resaAvailableUntilDate.format('MMMM')
	  	var resaAvailableUntilDateYear = resaAvailableUntilDate.format('YYYY')
	  	var resaAvailableUntilDateHour = resaAvailableUntilDate.format('HH')
	  	var resaAvailableUntilDateMinutes = resaAvailableUntilDate.format('mm')
		$(k).html(ReadXmlTranslate($(k).attr("data-trad")).replace("[daytxt]", resaAvailableUntilDateDayTxt).replace("[day]", resaAvailableUntilDateDay).replace("[monthtxt]", resaAvailableUntilDateMonthTxt).replace("[year]", resaAvailableUntilDateYear).replace("[hour]", resaAvailableUntilDateHour).replace("[min]", resaAvailableUntilDateMinutes))
		
	});
});
