﻿
var urlparent = "http://localhost:61720";
var bankDetailsCurrent = {};

$(document).ready(function () {


    /*$('input[name="chkConditionsCertificate"]').on('click', function () {
        if($('input[name="chkConditionsCertificate"]:checked').length > 0)
        {
            $('#btnValiderBankDetails').removeClass('disabled');
            $('#btnValiderBankDetails').removeAttr('disabled');
        } else {
            $('#btnValiderBankDetails').addClass('disabled');
        }
    });*/
    $('input[name="chkConditionsCertificate"]').on('click', function () {
        IsConditionsCertificateChecked();
    });
    IsConditionsCertificateChecked();

    $('#btnValiderBankDetails').on('click', function (e) {
        //e.preventDefault();
        //$('#bankDetailsForm').data('bootstrapValidator').validate();
        e.preventDefault();
        e.stopPropagation();
        //$('#updateForm').data('formValidation').validate();

        var curForm = $(this).closest('form');
        if (validForm(curForm)) {
            SaveBankDetails();
        } else {
            showAlertError('', ReadXmlTranslate("msg_error_update"), '')
        }
    });
    //ValidBankDetailForm();

    LoadBankDetails();

    //affiche / cache les input confirm email et mot de passe
    $('switchhide').collapse('hide')
    $('#switchUpdateBankDetails').on('click', function (e) {
        if ($('input#switchUpdateBankDetails').prop('checked')) {
            console.log('switchUpdateBankDetails is checked')
            $('#iban, #bic').removeAttr('disabled');
            
            $('.switchhide').find('.form-group, .form-check').removeClass('d-none')
            $('.switchhide').collapse('show')
        } else {
            console.log('switchUpdateBankDetails is NOT checked')
            $('#iban, #bic').attr('disabled', 'disabled');
            $('#btnValiderBankDetails').addClass('disabled');
            $('.switchhide').collapse('hide')
            $('.switchhide').on('hidden.bs.collapse', function () {
                $(this).find('.form-group, .form-check').addClass('d-none')
            })
        }
    })

    $('#switchUpdateBankDetails').on('click', function (e) {
        if ($('input#switchUpdateBankDetails').prop('checked')) {
            $('.div_hide_update').show("slide", 1000);
            //$('#iban').val("");
            //$('#bic').val("");
            //décoche la checkbox
            $('input[name="chkConditionsCertificate"]').prop('checked', false);

        } else {
            $('.div_hide_update').fadeOut("slow");
            $('#iban').val(bankDetailsCurrent.IBAN);
            $('#bic').val(bankDetailsCurrent.BIC);
        }
    })

    $('.div_hide_update').hide();
});


function LoadBankDetails() {

    $.ajax({
        type: "POST",
        url: 'BankDetails.aspx/LoadBankDetails',
        contentType: 'application/json; charset=utf-8',
        dataType: "json",
        success: function (response) {

            if (response.d != null) {
                bankDetailsCurrent.IBAN = response.d.IBAN;
                bankDetailsCurrent.BIC = response.d.BIC;

                $('#iban').val(response.d.IBAN);
                $('#bic').val(response.d.BIC);
            }


            //if (response.d)
            //    $('#modalForMessageBody').html(ReadXmlTranslate('msg_success_insert_bank_details'));
            //else
            //    $('#modalForMessageBody').html(ReadXmlTranslate('msg_error_insert_bank_details'));


            //$('#modalForWaiting').modal('hide');
            //$('#modalForMessage').modal('show');

            //$('#modalForMessage').delay(2500).fadeOut(function () {
            //    $('#modalForMessage').modal("hide");
            //    parent.postMessage("Closeme", urlparent);

            //});
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            // ShowError("error", XMLHttpRequest.responseJSON.Message + "<br />" + XMLHttpRequest.responseJSON.StackTrace + "\r\n", "alert alert-danger alert-dismissable", 5000);
            //$('#modalForWaiting').modal('hide');
            //console.log(XMLHttpRequest.responseJSON.Message + "\r\n" + XMLHttpRequest.responseJSON.StackTrace);
            showAlertError('', XMLHttpRequest.responseJSON.Message + "\r\n" + XMLHttpRequest.responseJSON.StackTrace, '')
        }
    });
}


function IsConditionsCertificateChecked() {

    var flagStatus = false;

    //  $('input[name="chkConditionsCertificate"]').on('click', function () {
    if ($('input[name="chkConditionsCertificate"]:checked').length > 0) {
        $('#btnValiderBankDetails').removeClass('disabled');
        $('#btnValiderBankDetails').removeAttr('disabled');
        flagStatus = true;
    } else {
        $('#btnValiderBankDetails').addClass('disabled');
        flagStatus = false;
    }
    // });

    return flagStatus;
}



/*function ValidBankDetailForm() {

    $('#bankDetailsForm').on('init.form.bv', function (e, data) {
        data.bv.disableSubmitButtons(true);
    }).bootstrapValidator({
        excluded: ':disabled',
        feedbackIcons: {
            valid: 'glyphicon glyphicon-ok',
            invalid: 'glyphicon glyphicon-remove',
            validating: 'glyphicon glyphicon-refresh'
        },      
        fields: {

            titulaire: {
                validators: {
                    notEmpty: {
                        message: ReadXmlTranslate('msg_error_titulaire_empty')
                    }

                }
            },
            iban: {
                validators: {
                    notEmpty: {
                        message: ReadXmlTranslate('msg_error_iban_empty')
                    },
                    iban: {
                        message: ReadXmlTranslate('msg_error_iban_valid')
                    }
                }
            },
            bic: {
                validators: {
                    notEmpty: {
                        message: ReadXmlTranslate('msg_error_bic_empty')
                    },
                    bic: {
                        message: ReadXmlTranslate('msg_error_bic_valid')
                    }

                }
            }
            
        }
    }).on('success.form.bv', function (e) {
        // Prevent submit form
        e.preventDefault();


        if (IsConditionsCertificateChecked())
            SaveBankDetails();
        else
        {
            $('#modalForMessageBody').html(ReadXmlTranslate('msg_error_accept_conditions'));

            $('#modalForMessage').modal('show');
        }
    });

}*/


var urlparentP = getUrlVars()["parent"];
if (urlparentP != undefined)
    urlparent = urlparentP;

function SaveBankDetails() {


    var IdentityRIBEntity = {};
    //IdentityRIBEntity.IBAN = $('input[name="iban"]').val();
    //IdentityRIBEntity.BIC = $('input[name="bic"]').val();
    IdentityRIBEntity.IBAN = $('input[name="iban"]').val().replace(/[^a-zA-Z0-9]/g, '').toUpperCase();
    IdentityRIBEntity.BIC = $('input[name="bic"]').val().replace(/[^a-zA-Z0-9]/g, '').toUpperCase();


    var sData = JSON.stringify({ identityRIB: IdentityRIBEntity, titulaire: $('input[name="titulaire"]').val() });


    $.ajax({
        type: "POST",
        url: 'BankDetails.aspx/CreateBankDetails',
        data: sData,
        contentType: 'application/json; charset=utf-8',
        dataType: "json",
        success: function (response) {

            //ShowError("error", response.d.split(':')[1], "alert alert-" + response.d.split(':')[0] + " alert-dismissable", 5000);
            //   ShowError("error", ReadXmlTranslate(response.d.split(':')[1]), "alert alert-" + response.d.split(':')[0] + " alert-dismissable", 3000);
            //  $('#widgetForm').hide();
            //  $('#dialogForWaitingInPop').dialog('close');


            /* if (response.d)
                 $('#modalForMessageBody').html(ReadXmlTranslate('msg_success_insert_bank_details'));
             else
                 $('#modalForMessageBody').html(ReadXmlTranslate('msg_error_insert_bank_details'));*/$
            if (response.d.split(':')[0] == 'true') {
                showAlertSuccess('', ReadXmlTranslate(response.d.split(':')[1]), '')
            } else {
                showAlertError('', ReadXmlTranslate(response.d.split(':')[1]), '')
            }

            /*$('#modalForMessageBody').html(ReadXmlTranslate(response.d.split(':')[1]));

            
            $('#modalForWaiting').modal('hide');
            $('#modalForMessage').modal('show');

            $('#modalForMessage').delay(2500).fadeOut(function () {
                $('#modalForMessage').modal("hide");
                parent.postMessage("Closeme", urlparent);

            });*/
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            // ShowError("error", XMLHttpRequest.responseJSON.Message + "<br />" + XMLHttpRequest.responseJSON.StackTrace + "\r\n", "alert alert-danger alert-dismissable", 5000);
            //$('#modalForWaiting').modal('hide');
            //console.log(XMLHttpRequest.responseJSON.Message + "\r\n" + XMLHttpRequest.responseJSON.StackTrace);
            showAlertError('', XMLHttpRequest.responseJSON.Message + "\r\n" + XMLHttpRequest.responseJSON.StackTrace, '')
        }
    });
}