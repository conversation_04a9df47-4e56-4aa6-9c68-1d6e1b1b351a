$(document).ready(function() {
    initAdhesionsOrders()
})

function initAdhesionsOrders() {
    // call widget local mvc
    var widgetData = getWidgetSignature();

    if (widgetData != undefined) {
        var thisPartnerName = widgetData[0];
        var thisSignature = widgetData[1];

        SphereCustomer.AdhesionOrders({
            selector: '#WidgetAdhesionsWrapper',
            structureId: $('#receptVar').attr('data-structureid'),
            identityId: $('#receptVar').attr('data-iid'),
            webUserId: $('#receptVar').attr('data-curriduser'),
            langCode: $('#receptVar').attr('data-lang'),
            partnerName: thisPartnerName,
            signature: thisSignature
        })


    }

    function getWidgetSignature() {
        var resultData;
        $.ajax({
            type: "POST",
            url: 'commons.asmx/GetWidgetSignature',
            contentType: 'application/json; charset=utf-8',
            async: false,
            data: JSON.stringify({
                structureId: structureid
            }),
            success: function(data, textStatus, jqXHR) {
                resultData = data.d;

            },
            error: function(XMLHttpRequest, textStatus, errorThrown) {

            }
        });
        return resultData;
    }
}