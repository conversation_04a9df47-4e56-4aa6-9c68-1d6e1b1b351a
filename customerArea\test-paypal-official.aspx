<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="test-paypal-official.aspx.cs" Inherits="customerArea.TestPayPalOfficial" %>

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title>Test PayPal API Officielle</title>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.1.0/css/bootstrap.min.css">
    <script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
</head>
<body>
    <form id="form1" runat="server">
        <div class="container mt-5">
            <div class="row">
                <div class="col-md-8 offset-md-2">
                    <div class="card">
                        <div class="card-header">
                            <h3>Test PayPal API Officielle</h3>
                        </div>
                        <div class="card-body">
                            <h5>Configuration actuelle :</h5>
                            <ul>
                                <li><strong>Structure ID :</strong> <span id="structureId"><%=Request.QueryString["idstructure"] ?? "991"%></span></li>
                                <li><strong>Client ID :</strong> <span id="clientId">Chargement...</span></li>
                                <li><strong>Return URL :</strong> <span id="returnUrl">Chargement...</span></li>
                            </ul>
                            
                            <hr />
                            
                            <h5>Bouton PayPal Officiel :</h5>
                            <div id="manual-paypal-btn" class="mb-3"></div>
                            
                            <hr />
                            
                            <h5>Logs :</h5>
                            <div id="logs" style="background-color: #f8f9fa; padding: 10px; border-radius: 4px; max-height: 300px; overflow-y: auto;">
                                <div>Initialisation...</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>

    <!-- Settings simulés pour le test -->
    <span id="settingsCustomer" class="d-none"><%=SettingsJson%></span>
    <span id="settingsDefaultCustomer" class="d-none">{}</span>
    <span id="settingsPlateform" class="d-none">{}</span>

    <!-- PayPal API -->
    <script src='https://www.paypalobjects.com/js/external/api.js'></script>
    
    <script>
        function log(message) {
            var timestamp = new Date().toLocaleTimeString();
            $('#logs').append('<div>[' + timestamp + '] ' + message + '</div>');
            $('#logs').scrollTop($('#logs')[0].scrollHeight);
            console.log(message);
        }

        function getStructure() {
            return $('#structureId').text() || '991';
        }

        $(document).ready(function() {
            log('Début de l\'initialisation PayPal...');
            
            // Charger les settings
            var settings = {};
            try {
                if ($('#settingsCustomer').text() !== "") {
                    settings = JSON.parse($('#settingsCustomer').text());
                    log('Settings chargés avec succès');
                } else {
                    log('ERREUR: Aucun settings trouvé');
                    return;
                }
            } catch (e) {
                log('ERREUR: Impossible de parser les settings - ' + e.message);
                return;
            }

            // Vérifier la structure des settings PayPal
            if (!settings.login || !settings.login.payPal) {
                log('ERREUR: Structure settings.login.payPal manquante');
                return;
            }

            var clientId = settings.login.payPal.appid;
            var returnUrl = settings.login.payPal.returnurl;
            var thisStructureId = getStructure().trim();

            // Afficher les valeurs
            $('#clientId').text(clientId ? clientId.substring(0, 20) + '...' : 'NON DÉFINI');
            $('#returnUrl').text(returnUrl ? returnUrl.replace('[structureid]', thisStructureId) : 'NON DÉFINI');

            // Vérifications
            if (!clientId || clientId === "") {
                log('ERREUR: PayPal appid vide dans les settings');
                return;
            }

            if (!returnUrl || returnUrl === "") {
                log('ERREUR: PayPal returnurl vide dans les settings');
                return;
            }

            // Remplacer [structureid] dans l'URL de retour
            returnUrl = returnUrl.replace('[structureid]', thisStructureId);
            log('URL de retour finale: ' + returnUrl);

            // Initialiser PayPal
            log('Initialisation du bouton PayPal avec Client ID: ' + clientId.substring(0, 10) + '...');
            
            try {
                paypal.use(['login'], function (login) {
                    log('Module PayPal login chargé avec succès');
                    
                    login.render({
                        "appid": clientId,
                        "scopes": "openid email profile address",
                        "containerid": "manual-paypal-btn",
                        "responseType": "code",
                        "locale": "fr-fr",
                        "buttonType": "LWP",
                        "buttonShape": "rectangle", 
                        "buttonSize": "lg",
                        "fullPage": "true",
                        "returnurl": returnUrl,
                        "state": "idstructure=" + thisStructureId
                    });
                    
                    log('Bouton PayPal rendu avec succès !');
                });
            } catch (e) {
                log('ERREUR lors de l\'initialisation PayPal: ' + e.message);
            }
        });
    </script>
</body>
</html>
