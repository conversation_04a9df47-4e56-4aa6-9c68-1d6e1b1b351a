<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="test-paypal-official.aspx.cs" Inherits="customerArea.TestPayPalOfficial" %>

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title>Test PayPal API Officielle</title>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.1.0/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.2.0/css/all.css">
    <script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>

    <style>
        /* Styles pour forcer le bouton PayPal à la même taille que Facebook */
        #manual-paypal-btn {
            width: 100% !important;
            display: block !important;
            box-sizing: border-box !important;
            margin: 0 !important;
        }

        #manual-paypal-btn * {
            width: 100% !important;
            box-sizing: border-box !important;
        }

        #manual-paypal-btn iframe {
            width: 100% !important;
            height: auto !important;
            min-height: 40px !important;
            border: none !important;
        }

        #manual-paypal-btn button,
        #manual-paypal-btn .paypal-button,
        #manual-paypal-btn [role="button"] {
            width: 100% !important;
            display: block !important;
            padding: 10px 15px !important;
            font-size: 14px !important;
            line-height: 1.42857143 !important;
            border-radius: 4px !important;
            border: 1px solid #0070ba !important;
            background-color: #0070ba !important;
            color: white !important;
            text-align: center !important;
            text-decoration: none !important;
            cursor: pointer !important;
            box-sizing: border-box !important;
        }

        #manual-paypal-btn button:hover,
        #manual-paypal-btn .paypal-button:hover,
        #manual-paypal-btn [role="button"]:hover {
            background-color: #005ea6 !important;
            border-color: #005ea6 !important;
            filter: brightness(1.1) !important;
        }

        /* Style pour simuler le bouton Facebook */
        .btn-facebook-demo {
            background-color: #4467b0;
            color: #fff;
            margin: 0;
            padding: 10px 15px;
            font-size: 14px;
            line-height: 1.42857143;
            border-radius: 4px;
            border: 1px solid #4467b0;
            width: 100%;
            display: block;
            text-align: center;
            text-decoration: none;
            cursor: pointer;
        }

        .btn-facebook-demo:hover {
            color: #fff;
            background-color: #3a5998;
            border-color: #3a5998;
            filter: brightness(1.1);
        }
    </style>
</head>
<body>
    <form id="form1" runat="server">
        <div class="container mt-5">
            <div class="row">
                <div class="col-md-8 offset-md-2">
                    <div class="card">
                        <div class="card-header">
                            <h3>Test PayPal API Officielle</h3>
                        </div>
                        <div class="card-body">
                            <h5>Configuration actuelle :</h5>
                            <ul>
                                <li><strong>Structure ID :</strong> <span id="structureId"><%=Request.QueryString["idstructure"] ?? "991"%></span></li>
                                <li><strong>Client ID :</strong> <span id="clientId">Chargement...</span></li>
                                <li><strong>Return URL :</strong> <span id="returnUrl">Chargement...</span></li>
                            </ul>
                            
                            <hr />
                            
                            <h5>Bouton Facebook (référence) :</h5>
                            <button class="btn btn-facebook-demo mb-3">
                                <i class="fab fa-facebook-f"></i> Connexion avec Facebook
                            </button>

                            <h5>Bouton PayPal Officiel :</h5>
                            <div id="manual-paypal-btn" class="mb-3"></div>
                            
                            <hr />
                            
                            <h5>Logs :</h5>
                            <div id="logs" style="background-color: #f8f9fa; padding: 10px; border-radius: 4px; max-height: 300px; overflow-y: auto;">
                                <div>Initialisation...</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>

    <!-- Settings simulés pour le test -->
    <span id="settingsCustomer" class="d-none"><%=SettingsJson%></span>
    <span id="settingsDefaultCustomer" class="d-none">{}</span>
    <span id="settingsPlateform" class="d-none">{}</span>

    <!-- PayPal API -->
    <script src='https://www.paypalobjects.com/js/external/api.js'></script>
    
    <script>
        function log(message) {
            var timestamp = new Date().toLocaleTimeString();
            $('#logs').append('<div>[' + timestamp + '] ' + message + '</div>');
            $('#logs').scrollTop($('#logs')[0].scrollHeight);
            console.log(message);
        }

        function getStructure() {
            return $('#structureId').text() || '991';
        }

        // Fonction pour forcer les styles du bouton PayPal
        function forcePayPalButtonStyles() {
            var container = $('#manual-paypal-btn');
            if (container.length) {
                // Forcer les styles sur le conteneur
                container.css({
                    'width': '100%',
                    'display': 'block',
                    'box-sizing': 'border-box',
                    'margin': '0'
                });

                // Forcer les styles sur tous les éléments enfants
                container.find('*').css({
                    'width': '100%',
                    'box-sizing': 'border-box'
                });

                // Styles spécifiques pour les iframes
                container.find('iframe').css({
                    'width': '100%',
                    'height': 'auto',
                    'min-height': '40px',
                    'border': 'none'
                });

                // Styles pour les boutons
                container.find('button, .paypal-button, [role="button"]').css({
                    'width': '100%',
                    'display': 'block',
                    'padding': '10px 15px',
                    'font-size': '14px',
                    'line-height': '1.42857143',
                    'border-radius': '4px',
                    'border': '1px solid #0070ba',
                    'background-color': '#0070ba',
                    'color': 'white',
                    'text-align': 'center',
                    'text-decoration': 'none',
                    'cursor': 'pointer',
                    'box-sizing': 'border-box'
                });

                log('Styles PayPal forcés sur le conteneur');
            }
        }

        $(document).ready(function() {
            log('Début de l\'initialisation PayPal...');
            
            // Charger les settings
            var settings = {};
            try {
                if ($('#settingsCustomer').text() !== "") {
                    settings = JSON.parse($('#settingsCustomer').text());
                    log('Settings chargés avec succès');
                } else {
                    log('ERREUR: Aucun settings trouvé');
                    return;
                }
            } catch (e) {
                log('ERREUR: Impossible de parser les settings - ' + e.message);
                return;
            }

            // Vérifier la structure des settings PayPal
            if (!settings.login || !settings.login.payPal) {
                log('ERREUR: Structure settings.login.payPal manquante');
                return;
            }

            var clientId = settings.login.payPal.appid;
            var returnUrl = settings.login.payPal.returnurl;
            var thisStructureId = getStructure().trim();

            // Afficher les valeurs
            $('#clientId').text(clientId ? clientId.substring(0, 20) + '...' : 'NON DÉFINI');
            $('#returnUrl').text(returnUrl ? returnUrl.replace('[structureid]', thisStructureId) : 'NON DÉFINI');

            // Vérifications
            if (!clientId || clientId === "") {
                log('ERREUR: PayPal appid vide dans les settings');
                return;
            }

            if (!returnUrl || returnUrl === "") {
                log('ERREUR: PayPal returnurl vide dans les settings');
                return;
            }

            // Remplacer [structureid] dans l'URL de retour
            returnUrl = returnUrl.replace('[structureid]', thisStructureId);
            log('URL de retour finale: ' + returnUrl);

            // Initialiser PayPal
            log('Initialisation du bouton PayPal avec Client ID: ' + clientId.substring(0, 10) + '...');
            
            try {
                paypal.use(['login'], function (login) {
                    log('Module PayPal login chargé avec succès');
                    
                    login.render({
                        "appid": clientId,
                        "scopes": "openid email profile address",
                        "containerid": "manual-paypal-btn",
                        "responseType": "code",
                        "locale": "fr-fr",
                        "buttonType": "LWP",
                        "buttonShape": "rectangle",
                        "buttonSize": "lg",
                        "fullPage": "true",
                        "returnurl": returnUrl,
                        "state": "idstructure=" + thisStructureId
                    });

                    log('Bouton PayPal rendu avec succès !');

                    // Forcer les styles après le rendu du bouton
                    setTimeout(function() {
                        forcePayPalButtonStyles();
                        log('Styles PayPal appliqués');
                    }, 500);

                    // Observer les changements dans le DOM pour maintenir les styles
                    var observer = new MutationObserver(function(mutations) {
                        mutations.forEach(function(mutation) {
                            if (mutation.type === 'childList') {
                                forcePayPalButtonStyles();
                            }
                        });
                    });

                    var targetNode = document.getElementById('manual-paypal-btn');
                    if (targetNode) {
                        observer.observe(targetNode, { childList: true, subtree: true });
                        log('Observer PayPal configuré');
                    }
                });
            } catch (e) {
                log('ERREUR lors de l\'initialisation PayPal: ' + e.message);
            }
        });
    </script>
</body>
</html>
