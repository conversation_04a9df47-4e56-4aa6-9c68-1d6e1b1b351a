<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="test-paypal-callback.aspx.cs" Inherits="customerArea.test_paypal_callback" %>

<!DOCTYPE html>
<html>
<head>
    <title>Test PayPal Callback</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .info { background: #e7f3ff; padding: 15px; border-left: 4px solid #2196F3; margin: 10px 0; }
        .error { background: #ffebee; padding: 15px; border-left: 4px solid #f44336; margin: 10px 0; }
        .success { background: #e8f5e8; padding: 15px; border-left: 4px solid #4caf50; margin: 10px 0; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
        .btn { background: #2196F3; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; display: inline-block; margin: 5px; }
    </style>
</head>
<body>
    <h1>🧪 Test PayPal Callback</h1>
    
    <div class="info">
        <h3>Cette page teste la configuration PayPal sans passer par PayPal</h3>
        <p>Elle simule le retour de PayPal avec un code d'autorisation factice.</p>
    </div>

    <div id="results" runat="server"></div>

    <div style="margin-top: 20px;">
        <a href="test-paypal-callback.aspx?action=test&idstructure=991" class="btn">🧪 Tester avec Structure 991</a>
        <a href="test-paypal-callback.aspx?action=settings&idstructure=991" class="btn">⚙️ Voir Settings PayPal</a>
        <a href="login.aspx" class="btn">🔙 Retour Login</a>
    </div>
</body>
</html>
