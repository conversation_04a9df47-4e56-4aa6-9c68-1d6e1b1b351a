﻿<%@ Master Language="C#" AutoEventWireup="true" CodeBehind="pageMasterKiosq.master.cs" Inherits="customerArea.pageMasterKiosq" %>


<!doctype html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="<%=System.Web.HttpContext.Current.Session["SVarLangue"] %>">
<head runat="server" id="myhead">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <!--<meta name="viewport" content="width=device-width, maximum-scale=1, initial-scale=1, user-scalable=0">-->
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title></title>
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.1.0/css/bootstrap.min.css" integrity="sha384-9gVQ4dYFwwWSjIDZnLEWnxCjeSWFphJiwGPXr1jddIhOegiu1FwO5qRGvFXOdJZ4" crossorigin="anonymous">
    <!-- Fonts CSS -->
    <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700,800" rel="stylesheet">
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.2.0/css/all.css" integrity="sha384-hWVjflwFxL6sNzntih27bfxkr27PmbbK/iSvJ+a4+0owXq79v+lsFkW54bOGbiDQ" crossorigin="anonymous">
    <!-- JQUERY -->
    <script src="https://code.jquery.com/jquery-3.3.1.min.js" integrity="sha256-FgpCb/KJQlLNfOu91ta32o/NMZxltwRo8QtmkMRdAu8=" crossorigin="anonymous"></script>
    <!-- Internationnal Tel input -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/12.1.9/css/intlTelInput.css" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/12.1.9/js/intlTelInput.js"></script>
    <!-- GOOGLEMAPS API -->
    <script src="https://maps.googleapis.com/maps/api/js?sensor=false&libraries=places&key=AIzaSyB3GgRiYX1o93BkSy7DmqTA-bNTqGYQXYE"></script>
    <!-- choosen-->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/chosen/1.8.5/chosen.jquery.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/chosen/1.8.5/chosen.min.css" />
    <!-- MOMENT -->
    <script src="/assets/LIBS/moment-develop/min/moment-with-locales.js" type="text/javascript"></script>
    <script src="/assets/LIBS/bootstrapvalidator/moment/moment-with-locales.js" type="text/javascript"></script>
    <!-- jquery MASK -->
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jquery.mask/1.14.15/jquery.mask.min.js"></script>
    <!--keyboard-->
    <script src="/assets/LIBS/jkeyboard/1.0.0/jkeyboard.js"></script>

    <!-- THEMIS LIBS -->
    <link type="text/css" href="https://www.themisweb.fr/assets/phoca-flags/css/phoca-flags.custom.min.css" rel="stylesheet" />
    <!-- keyboard -->
    <link rel="stylesheet" href="/assets/LIBS/jkeyboard/1.0.0/jkeyboard.css">
    <!-- Style CSS generic -->
    <link rel="stylesheet/less" runat="server" id="lessfileofkiosk" type="text/css" href="" />
    <script src="//cdnjs.cloudflare.com/ajax/libs/less.js/3.0.0/less.min.js"></script>
    <!--commons-->
    <script src="./javascriptfiles/workspace.js"></script>
    <script src="./javascriptfiles/commons.js"></script>
    <script src="./javascriptfiles/traduction_commune.js"></script>
    <script src="./javascriptfiles/commonsKiosq.js"></script>

</head>

<body class="top-navigation">

 <span id='receptVar' class='hide' data-lang="<%=System.Web.HttpContext.Current.Session["SVarLangue"] %>" 
        data-structureid="<%=System.Web.HttpContext.Current.Session["idstructure"] %>" 
        data-iid="<%=System.Web.HttpContext.Current.Session["SVarUserIdentityID"] %>"
        data-sessid="<%=System.Web.HttpContext.Current.Session.SessionID %>"
        data-curriduser="<%=System.Web.HttpContext.Current.Session["currIdUser"]%>"
        ></span>

 <!-- main content START -->
    <div id="mainwrap" class="container-fluid">
        <div class="row">
            <div class="col-9">
                <div class="row" id="mainNavigation">
                        <nav class="fixed-top col-9 navbar navbar-expand-lg">
                            <!--left-->
                            <div class="mr-auto">
                                <a href="" id="btnleave" class="btn btn-outline-secondary my-2 my-sm-0">Quittez</a>
                            </div>
                            <!--right-->
                            <ul class="navbar-nav">
                                <li class="nav-item dropdown" id="languagelistWrapper">
                                    <a class="ml-2 dropdown-toggle btn btn-outline-primary" href="#" id="navbarDropdown" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Langue</a>
                                    <div id="languagelist" class="dropdown-menu dropdown-menu-right" aria-labelledby="navbarDropdown">
                                       <!-- <a class="dropdown-item" href="#">Français</a>
                                        <a class="dropdown-item" href="#">Anglais</a>-->
                                    </div>
                                </li>
                            </ul>
                        </nav>
                    </div>
                <!-- maincontent-->
                <div class="row" id="mainContent">
                     <svg version="1.1" id="loadingcircle" class="loadingcircle" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 25.8 25.6" style="enable-background: new 0 0 25.8 25.6;" xml:space="preserve">
                        <g id="greycircle">
                            <path class="st0" d="M16.4,6.4c0,0,5.4,2.9,4.8,9.2c-0.4,3.1-1.5,4.5-1.5,4.5s1.8-3.8,0.9-7.3C19.8,9,16.4,6.4,16.4,6.4z" />
                            <path class="st0" d="M2,15.5c0,0-2.4-4.9,1.5-10.1c5-6.7,12.7-3.4,12.7-3.4S9.3,0,4.7,5.7S2,15.5,2,15.5z" />
                            <path class="st0" d="M10.7,4c0,0,4.9-1,8.7,2.5c4.2,3.8,3.5,8.9,3.5,8.9s0.6-5.4-4.3-8.8C14.7,3.4,10.7,4,10.7,4z" />
                            <path class="st0" d="M2.6,12.2c0,0,0.1,8,8.7,9.6c7.8,1.2,10.6-3.1,10.6-3.1S18.6,25,9.8,22.9C2.1,20.4,2.6,12.2,2.6,12.2z" />
                            <path class="st1" d="M7.2,5.5" />
                            <path class="st0" d="M7.1,5.7C7.1,5.7,7.1,5.7,7.1,5.7c-0.3,0.4-2.8,3.2-2.6,6.4c-0.1,5.5,3.3,8.2,3.3,8.2s-2.9-2.8-3-8
                                C4.9,8.2,7.1,5.7,7.1,5.7z" />
                        </g>
                        <g id="colorcircle">
                            <path class="st2" d="M4.9,6.3C4.9,6.3,9,0,17.3,3c6,2.5,6.3,8.7,6.3,8.7s-1.3-6.3-7.7-8C8.8,1.8,4.9,6.3,4.9,6.3z" />
                            <path class="st2" d="M3.3,11.8c0,0,0.1-2.7,2.2-5.1c2.1-2.4,5.1-2.9,5.1-2.9s-2.9,0.7-4.7,3C3.6,9.2,3.3,11.8,3.3,11.8z" />
                            <path class="st2" d="M1.3,14c0,0,0,5.5,4.3,8.5c4.3,3,9.3,1.4,9.3,1.4s-3.3,1.2-8.4-1.9C1.2,18.4,1.3,14,1.3,14z" />
                            <path class="st2" d="M23.5,9c0,0,2.8,5.1-1,10.4c-2.1,2.9-4.1,3.5-4.1,3.5s4-2.5,5.1-6.5C24.8,12.2,23.5,9,23.5,9z" />
                            <path class="st2" d="M19.9,15.5c0,0,0.7,4-3,6.6c-2,1.4-3.5,1.3-3.5,1.3s3.2-0.7,4.8-3.1C20,17.9,19.9,15.5,19.9,15.5z" />
                        </g>
                    </svg>
                    <div id="msgNotificationAlert" class="row"></div>
                    <form class="col-12" id="form1" runat="server">
                        <asp:ContentPlaceHolder ID="ctPlaceHold" runat="server"></asp:ContentPlaceHolder>
                    </form>
                </div>
            <div>
            <!-- keyboard -->
            <div id="virtualkeyboard-wrap" class="col-3">
                <div id="keyboardAndBtn" class="text-center">
                    <div id="virtualkeyboard"></div>
                </div>
            <div>
        </div>
    </div>
    <!-- MODAL GENERIC-->
    <div class="modal fade col-9" tabindex="-1" role="dialog" id="modalGeneric"  aria-labelledby="modalGenericLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalGenericLabel"></h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>

            </div>
        </div>
    </div>

     <div class="alert alert-success fade fixed-top" id="alertSuccess" role="alert">
        <h4 class="alert-heading mb-0"></h4>
        <p class="mb-0"></p>
    </div>
    <div class="alert alert-danger fade fixed-top" id="alertError" role="alert">
        <h4 class="alert-heading mb-0"></h4>
        <p class="mb-0"></p>
    </div>
    <span id="settingsPlateform" runat="server" class="d-none"></span>
    <span id="settingsCustomer" runat="server" class="d-none"></span>
    <span id="settingsDefaultCustomer" runat="server" class="d-none"></span>

    <!-- bootstrap -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.0/umd/popper.min.js" integrity="sha384-cs/chFZiN24E4KMATLdqdvsezGxaGsi4hLGOzlXwp5UZB1LY//20VyM2taTB4QvJ" crossorigin="anonymous"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js" integrity="sha384-JZR6Spejh4U02d8jOt6vLEHfe/JQGiRRSQQxSfFWpi1MquVdAyjUar5+76PVCmYl" crossorigin="anonymous"></script>
    <!-- font awesome-->
    <script defer src="https://use.fontawesome.com/releases/v5.2.0/js/all.js" integrity="sha384-4oV5EgaV02iISL2ban6c/RmotsABqE4yZxZLcYMAdG7FAPsyHYAPpywE9PJo+Khy" crossorigin="anonymous"></script>
    <script src="/assets/LIBS/xml2json/xml2json.js"></script>

    <script type="text/javascript">
        var settings = "";
        if ( ($('#settingsCustomer').text() != "" || $('#settingsPlateform').text() != "") && $('#settingsDefaultCustomer').text() != "")
        {
            settings = $.extend(true, {}, JSON.parse($('#settingsDefaultCustomer').text()), JSON.parse($('#settingsCustomer').text()), JSON.parse($('#settingsPlateform').text()));
            
            var lessJson = {
                '@primaryColor': settings.global.primaryColor,
                '@secondaryColor': settings.global.secondaryColor,
                '@tertiaryColor': settings.global.tertiaryColor,
                '@bannerColor': settings.global.bannerColor
            }

            if(settings.global.globalFontUrl != "" && settings.global.globalFontUrl != undefined) {
                lessJson["@globalFontUrl"] = '"' + settings.global.globalFontUrl + '"'
            }
            
            if(settings.global.globalFontName != "" && settings.global.globalFontName != undefined) {
                lessJson["@globalFontName"] = '"' + settings.global.globalFontName + '"'
            }

            less.modifyVars(lessJson);
            
            
        }
        less.pageLoadFinished.then(function() {
            lessReady()
        });

    </script>
</body>
</html>
