<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="wctrlLoginConnect.ascx.cs" Inherits="customerArea.wuctrlLoginConnect" %>
<!-- tabindex : 1  -->
<!-- msg entete -->
<div id="msgEnteteLoginConnect" runat="server">
	<p class="text-center" data-trad="msg_ententeloginconnect"></p>
</div>

<!-- fb connect -->
<div id="divConnectFacebook" runat="server" visible="false">
	<button class="btn btn-facebook btn-block" id="btn_facebook" data-trad="btn_connect_facebook" data-title="btn_connect_facebook" tabindex=1>Connect with Facebook</button>
	<div class="orsepar" data-trad="msg_or">or</div>
</div>
<!-- payPal connect -->
<div id="divConnectPayPal" runat="server" visible="false" class="text-center">
	<div id="manual-paypal-btn" tabindex=1></div>
	<div class="orsepar" data-trad="msg_or">or</div>
</div>

<!-- openID connect -->
<div id="divConnectUnidy" runat="server" visible="false">
	<button class="btn btn-unidy btn-block" id="btn_unidy" data-trad="btn_connect_unidy"></button>
	<div class="orsepar" data-trad="msg_or">or</div>
</div>

<div id="loginForm" class="form-horizontal" role="form">

	<div class="row form-group">
		<label class="col-12 control-label text-md-right sr-only">
			<asp:Label id="lbidentiteid" runat="server"  data-trad="lbl_identite_id"></asp:Label>
		</label>
		<div class="col-12">
			<asp:TextBox id="txtIdentiteid" runat="server" type="text" 
			class="form-control input-sm idoremailAddress" data-validator-type="identiteid" AutoComplete="section-login identiteid" placeholder="ID" data-tradplaceholder="placeholder_identite_id" tabindex=1></asp:TextBox>
		</div>
	</div>

	<div class="row form-group">
		<label class="col-12 control-label text-md-right sr-only">
			<asp:Label id="lbemail" runat="server" data-trad="lbl_email"></asp:Label>
		</label>
		<div class="col-12">
			<asp:TextBox id="TxtEmailOrIdOrPseudo" runat="server"  AutoComplete="section-login email" AutoCompleteType="Email"  type="email" 
			class="form-control input-sm" data-validator-type="email" placeholder="E-mail" data-tradplaceholder="placeholder_email" tabindex=1></asp:TextBox>
		</div>

	</div>
	<div class="row form-group">
		<label class="col-12 control-label text-md-right sr-only">
			<asp:Label id="lbPassword" runat="server" data-trad="lbl_confirm_email"></asp:Label>
		</label>
		<div class="col-12">
			<div class="input-group mb-3">
				<asp:TextBox id="TxtPassword" runat="server" TextMode="Password" type="password" class="form-control input-sm" data-validator-type="confirmemail" placeholder="Password"  AutoComplete="section-login password" data-tradplaceholder="placeholder_password" tabindex=1></asp:TextBox> 

				   <div class="input-group-append">
				    <button class="btn btn-outline-secondary" type="button" id="btn-show-password"><i class="fa fa-eye"></i></button>
				  </div>
			</div>

		</div>

	</div>
	<div class="row form-group">
		<div class="col-12 text-right">
			<!-- btn login -->
			<asp:LinkButton ID="LnkBtnLogin" runat="server" CssClass="btn btn-primary btn-block" data-trad="btn_login" data-title="btn_login" tabindex=1>Login</asp:LinkButton>
			<asp:Image ID="BtnLogin" runat="server"  />
			<!-- btn mdp -->
			<asp:LinkButton ID="LnkBtnPassForget" runat="server" CssClass="btn btn-link btn-block d-none" data-trad="link_forget_password" tabindex=1>Forgot your password?</asp:LinkButton>
			<asp:Image ID="LinkBtnPassForget" runat="server"/>
			
			<a href="#" data-tabtarget="#forgottenPwTab" class="btn btn-link btn-block" data-trad="link_forget_password" tabindex=1>Forgot your password?</a>
		</div>
	</div>
</div>
<div ID="explicationConnexion" class="block" runat="server">
	<p class="text-justify">
		<asp:Label ID="lbForExplicationConnexion" runat="server" Text=" "></asp:Label>
		<asp:Label id="lblemailIDInfo" runat="server"></asp:Label>
	</p>
</div>
<asp:HiddenField id="hiddenPP" runat="server"></asp:HiddenField>

<asp:HyperLink ID="hlHomePage" class="d-none" runat="server">homePage</asp:HyperLink>